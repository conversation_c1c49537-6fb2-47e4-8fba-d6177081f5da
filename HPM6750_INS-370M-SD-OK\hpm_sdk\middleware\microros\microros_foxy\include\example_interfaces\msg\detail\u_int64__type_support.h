// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from example_interfaces:msg/UInt64.idl
// generated code does not contain a copyright notice

#ifndef EXAMPLE_INTERFACES__MSG__DETAIL__U_INT64__TYPE_SUPPORT_H_
#define EXAMPLE_INTERFACES__MSG__DETAIL__U_INT64__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "example_interfaces/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_example_interfaces
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  example_interfaces,
  msg,
  UInt64
)();

#ifdef __cplusplus
}
#endif

#endif  // EXAMPLE_INTERFACES__MSG__DETAIL__U_INT64__TYPE_SUPPORT_H_
