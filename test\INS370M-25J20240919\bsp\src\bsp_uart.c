#include "bsp_uart.h"
#include "fpgad.h"

// UART接收缓冲区相关变量 - 匹配fpgad.h中的定义
unsigned char grxbuffer[U4RX_MAXCOUNT + 2];
int grxlen = 0;
int grxst = 0;

void bsp_systick_init(uint32_t com)
{

	rcu_periph_clock_enable( RCU_GPIOA);

	/* enable USART clock */
	rcu_periph_clock_enable(RCU_USART0);

	/* connect port to USARTx_Tx */
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9);

	/* connect port to USARTx_Rx */
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_10);

	/* configure USART Tx as alternate function push-pull */
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_9);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_9);

	/* configure USART Rx as alternate function push-pull */
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_10);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);

	/* USART configure - 统一使用USART0 */
	usart_deinit(USART0);
	usart_baudrate_set(USART0,115200U);
	usart_receive_config(USART0, USART_RECEIVE_ENABLE);
	usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
	usart_enable(USART0);
}

void bsp_systick_init01(uint32_t com)
{

	rcu_periph_clock_enable( RCU_GPIOC);
	// rcu_periph_clock_enable( RCU_GPIOD);  // UART3不需要GPIOD

	/* enable USART clock - 改为UART3 */
	rcu_periph_clock_enable(RCU_UART3);

	/* connect port to UART3_Tx (PC10) */
	gpio_af_set(GPIOC, GPIO_AF_7, GPIO_PIN_10);

	/* connect port to UART3_Rx (PC11) */
	gpio_af_set(GPIOC, GPIO_AF_7, GPIO_PIN_11);

	/* configure UART3 Tx as alternate function push-pull */
	gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_10);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_10);

	/* configure UART3 Rx as alternate function push-pull */
	gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_11);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_11);

	/* USART configure - 改为UART3用于导航数据输出 */
	usart_deinit(UART3);
	usart_baudrate_set(UART3,115200U);
	usart_receive_config(UART3, USART_RECEIVE_ENABLE);
	usart_transmit_config(UART3, USART_TRANSMIT_ENABLE);
	usart_enable(UART3);
}



/*!
    \brief      this function handles USART0 exception
    \param[in]  none
    \param[out] none
    \retval     none
*/

// 改为UART3中断处理函数，用于导航数据输出
void USART3_IRQHandler(void)
{
    if((RESET != usart_interrupt_flag_get(UART3, USART_INT_FLAG_RBNE)) &&
            (RESET != usart_flag_get(UART3, USART_FLAG_RBNE))) {
        /* receive data */
		if (grxlen >= U4RX_MAXCOUNT) {
			grxbuffer[(grxst + grxlen) % U4RX_MAXCOUNT] = usart_data_receive(UART3);
		}
        //if(grxcount == U4RX_MAXCOUNT) {
            //usart_interrupt_disable(USART0, USART_INT_RBNE);
        //}
    }
    if((RESET != usart_flag_get(UART3, USART_FLAG_TBE)) &&
            (RESET != usart_interrupt_flag_get(UART3, USART_INT_FLAG_TBE))) {

		//gtxcount++;
        /* transmit data */
//        usart_data_transmit(USART0, txbuffer[txcount++]);
//        if(txcount == tx_size) {
//            //usart_interrupt_disable(USART0, USART_INT_TBE);
//        }
    }
}
