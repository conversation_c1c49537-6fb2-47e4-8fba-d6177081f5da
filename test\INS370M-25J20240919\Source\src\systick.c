/*!
    \file  systick.c
    \brief the systick configuration file
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#include "systick.h"
#include "board.h"

volatile static uint32_t delay_count;

void delay_1ms(uint32_t count)
{
    delay_ms_impl(count);
}

void delay_init(uint8_t SYSCLK)
{
    /* configure systick */
    systick_clksource_set(SYSTICK_CLKSOURCE_HCLK_DIV8);
}

//nus
//nus:us.
//nus:0~204522252(2^32/fac_us@fac_us=168)
void delay_us(uint32_t nus)
{
    uint32_t i;
    for(i = 0; i < nus; i++)
    {
        __NOP();
    }
}

//nms
//nms:ms
//nms:0~65535
void delay_ms_impl(uint32_t nms)
{
    uint32_t i, j;
    for(i = 0; i < nms; i++)
    {
        // 增加循环次数，并使用volatile防止编译器优化
        for(j = 0; j < 50000; j++)
        {
            __NOP();  // 空操作，防止编译器优化
        }
    }
}

// 添加delay_ms函数定义
void delay_ms(uint32_t nms)
{
    delay_ms_impl(nms);
}

//nms,
//nms:ms
void delay_xms(uint32_t nms)
{
    uint32_t i;
    for(i=0;i<nms;i++)
        delay_ms_impl(1);
}
