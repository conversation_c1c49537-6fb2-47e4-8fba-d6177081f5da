#include "bsp_tim.h"

uint32_t time_periodic_sec_cnt = 0;
uint32_t time_periodic_min_cnt = 0;
uint32_t time_periodic_hour_cnt = 0;
uint8_t time_sync_flag = 0;

uint32_t time_base_periodic_cnt = 0;

uint32_t time_base_100ms_periodic_cnt = 0;
uint32_t time_base_100ms_Flag = 0;

uint32_t time_base_20ms_periodic_cnt=0;
uint32_t time_base_20ms_Flag = 0;

void bsp_tim_init(void)
{
	timer_parameter_struct timer_initpara;
	timer_oc_parameter_struct timer_ocintpara;

	rcu_periph_clock_enable(RCU_TIMER0);
	rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

	timer_deinit(TIMER0);

	/* TIMER0 configuration */
	timer_initpara.prescaler         = 199;
	timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
	timer_initpara.counterdirection  = TIMER_COUNTER_UP;
	timer_initpara.period            = 999;
	timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
	timer_initpara.repetitioncounter = 0;
	timer_init(TIMER0,&timer_initpara);

	// 启用TIMER0的NVIC中断
	nvic_irq_enable(TIMER0_UP_TIMER9_IRQn, 2, 0);
	timer_interrupt_enable(TIMER0,TIMER_INT_UP);
	timer_enable(TIMER0);
	
	//TIMER2��Ϊʱ�����
	
	rcu_periph_clock_enable(RCU_TIMER2);
	rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

	timer_deinit(TIMER2);

	/* TIMER2 configuration */
	timer_initpara.prescaler         = 199;
	timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
	timer_initpara.counterdirection  = TIMER_COUNTER_UP;
	timer_initpara.period            = 999;
	timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
	timer_initpara.repetitioncounter = 0;
	timer_init(TIMER2,&timer_initpara);
	
	nvic_priority_group_set(NVIC_PRIGROUP_PRE4_SUB0);
	nvic_irq_enable(TIMER2_IRQn, 1, 1);
	
	timer_interrupt_enable(TIMER2,TIMER_INT_UP);
	timer_enable(TIMER2);
	
	//TIMER 1 PWM - 为FPGA提供时钟信号
	rcu_periph_clock_enable(RCU_TIMER1);
	rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

	timer_deinit(TIMER1);

	/* TIMER1 configuration for FPGA clock (目标：10MHz) */
	// 系统时钟200MHz × 4 = 800MHz
	// 预分频：4，Timer时钟 = 800MHz/4 = 200MHz
	// 周期：20，PWM频率 = 200MHz/20 = 10MHz
	timer_initpara.prescaler         = 3;      // 4分频 (3+1)
	timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
	timer_initpara.counterdirection  = TIMER_COUNTER_UP;
	timer_initpara.period            = 19;     // 20个计数 (19+1)
	timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
	timer_initpara.repetitioncounter = 0;
	timer_init(TIMER1,&timer_initpara);

	timer_ocintpara.ocpolarity  = TIMER_OC_POLARITY_HIGH;
	timer_ocintpara.outputstate = TIMER_CCX_ENABLE;
	timer_ocintpara.ocnpolarity  = TIMER_OCN_POLARITY_HIGH;
	timer_ocintpara.outputnstate = TIMER_CCXN_DISABLE;
	timer_ocintpara.ocidlestate  = TIMER_OC_IDLE_STATE_LOW;
	timer_ocintpara.ocnidlestate = TIMER_OCN_IDLE_STATE_LOW;

	timer_channel_output_config(TIMER1,TIMER_CH_0,&timer_ocintpara);
	timer_channel_output_pulse_value_config(TIMER1,TIMER_CH_0,9);  // 50%占空比 (19+1)/2 - 1 = 9
	timer_channel_output_mode_config(TIMER1,TIMER_CH_0,TIMER_OC_MODE_PWM0);
	timer_channel_output_shadow_config(TIMER1,TIMER_CH_0,TIMER_OC_SHADOW_DISABLE);

	timer_auto_reload_shadow_enable(TIMER1);
	timer_enable(TIMER1);
	
}




