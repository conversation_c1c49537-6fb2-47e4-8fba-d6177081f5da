#include "bsp_tim.h"

uint32_t time_periodic_sec_cnt = 0;
uint32_t time_periodic_min_cnt = 0;
uint32_t time_periodic_hour_cnt = 0;
uint8_t time_sync_flag = 0;

uint32_t time_base_periodic_cnt = 0;

uint32_t time_base_100ms_periodic_cnt = 0;
uint32_t time_base_100ms_Flag = 0;

uint32_t time_base_20ms_periodic_cnt=0;
uint32_t time_base_20ms_Flag = 0;

void bsp_tim_init(void)
{
	timer_parameter_struct timer_initpara;
	timer_oc_parameter_struct timer_ocintpara;

	printf("=== Timer Initialization Started ===\n");

	// 第一步：只初始化TIMER1 PWM（无中断），测试基本功能
	printf("Step 1: Initializing TIMER1 PWM (FPGA clock)...\n");

	rcu_periph_clock_enable(RCU_TIMER1);
	printf("  - TIMER1 clock enabled\n");

	// 注意：不要重复调用时钟预分频配置
	// rcu_timer_clock_prescaler_config(RCU_TIMER_PSC_MUL4);

	timer_deinit(TIMER1);
	printf("  - TIMER1 deinitialized\n");

	/* TIMER1 configuration for FPGA clock (目标：10MHz) */
	timer_initpara.prescaler         = 3;      // 4分频 (3+1)
	timer_initpara.alignedmode       = TIMER_COUNTER_EDGE;
	timer_initpara.counterdirection  = TIMER_COUNTER_UP;
	timer_initpara.period            = 19;     // 20个计数 (19+1)
	timer_initpara.clockdivision     = TIMER_CKDIV_DIV1;
	timer_initpara.repetitioncounter = 0;
	timer_init(TIMER1,&timer_initpara);
	printf("  - TIMER1 basic configuration done\n");

	timer_ocintpara.ocpolarity  = TIMER_OC_POLARITY_HIGH;
	timer_ocintpara.outputstate = TIMER_CCX_ENABLE;
	timer_ocintpara.ocnpolarity  = TIMER_OCN_POLARITY_HIGH;
	timer_ocintpara.outputnstate = TIMER_CCXN_DISABLE;
	timer_ocintpara.ocidlestate  = TIMER_OC_IDLE_STATE_LOW;
	timer_ocintpara.ocnidlestate = TIMER_OCN_IDLE_STATE_LOW;

	timer_channel_output_config(TIMER1,TIMER_CH_0,&timer_ocintpara);
	timer_channel_output_pulse_value_config(TIMER1,TIMER_CH_0,9);  // 50%占空比
	timer_channel_output_mode_config(TIMER1,TIMER_CH_0,TIMER_OC_MODE_PWM0);
	timer_channel_output_shadow_config(TIMER1,TIMER_CH_0,TIMER_OC_SHADOW_DISABLE);
	printf("  - TIMER1 PWM channel configured\n");

	timer_auto_reload_shadow_enable(TIMER1);
	timer_enable(TIMER1);
	printf("  - TIMER1 enabled successfully!\n");

	printf("=== Timer Initialization Completed ===\n");

	// 暂时注释掉TIMER0和TIMER2，避免中断冲突
	// 等TIMER1工作正常后再逐步添加
}




