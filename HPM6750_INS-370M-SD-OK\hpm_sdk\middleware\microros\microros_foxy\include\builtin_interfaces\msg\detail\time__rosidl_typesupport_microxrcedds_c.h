// generated from rosidl_typesupport_microxrcedds_c/resource/idl__rosidl_typesupport_c.h.em
// with input from builtin_interfaces:msg/Time.idl
// generated code does not contain a copyright notice
#ifndef BUILTIN_INTERFACES__MSG__TIME__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
#define BUILTIN_INTERFACES__MSG__TIME__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_


#include <stddef.h>
#include <stdbool.h>
#include <stdint.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "builtin_interfaces/msg/rosidl_typesupport_microxrcedds_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_builtin_interfaces
size_t get_serialized_size_builtin_interfaces__msg__Time(
  const void * untyped_ros_message,
  size_t current_alignment);

R<PERSON><PERSON>L_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_builtin_interfaces
size_t max_serialized_size_builtin_interfaces__msg__Time(
  bool * full_bounded,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_PUBLIC_builtin_interfaces
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_microxrcedds_c, builtin_interfaces, msg, Time)();

#ifdef __cplusplus
}
#endif


#endif  // BUILTIN_INTERFACES__MSG__TIME__ROSIDL_TYPESUPPORT_MICROXRCEDDS_C_H_
