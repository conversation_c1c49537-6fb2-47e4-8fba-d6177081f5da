T82D4 000:011.699   SEGGER J-Link V7.22b Log File
T82D4 000:012.012   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.052   Logging started @ 2025-06-11 02:25
T82D4 000:012.071 - 12.084ms
T82D4 000:012.105 JLINK_SetWarnOutHandler(...)
T82D4 000:012.130 - 0.037ms
T82D4 000:012.154 JLINK_OpenEx(...)
T82D4 000:013.666   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.030   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.198   Decompressing FW timestamp took 109 us
T82D4 000:019.913   Hardware: V9.40
T82D4 000:019.958   S/N: 59406895
T82D4 000:019.988   OEM: SEGGER
T82D4 000:020.016   Feature(s): R<PERSON>, GD<PERSON>, FlashDL, FlashB<PERSON>, J<PERSON><PERSON>
T82D4 000:020.833   TELNET listener socket opened on port 19021
T82D4 000:021.008   WEBSRV Starting webserver
T82D4 000:021.150   WEBSRV Webserver running on local port 19080
T82D4 000:021.183 - 9.037ms returns "O.K."
T82D4 000:021.208 JLINK_GetEmuCaps()
T82D4 000:021.227 - 0.029ms returns 0xB9FF7BBF
T82D4 000:021.251 JLINK_TIF_GetAvailable(...)
T82D4 000:021.376 - 0.138ms
T82D4 000:021.402 JLINK_SetErrorOutHandler(...)
T82D4 000:021.421 - 0.027ms
T82D4 000:021.456 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:031.605   Device "GD32F450II" selected.
T82D4 000:032.303 - 10.883ms returns 0x00
T82D4 000:032.403 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.046   Device "GD32F450II" selected.
T82D4 000:033.681 - 1.272ms returns 0x00
T82D4 000:033.713 JLINK_GetHardwareVersion()
T82D4 000:033.733 - 0.028ms returns 94000
T82D4 000:033.752 JLINK_GetDLLVersion()
T82D4 000:033.771 - 0.029ms returns 72202
T82D4 000:033.791 JLINK_GetOEMString(...)
T82D4 000:033.811 JLINK_GetFirmwareString(...)
T82D4 000:033.832 - 0.030ms
T82D4 000:033.863 JLINK_GetDLLVersion()
T82D4 000:033.883 - 0.028ms returns 72202
T82D4 000:033.900 JLINK_GetCompileDateTime()
T82D4 000:033.920 - 0.028ms
T82D4 000:033.943 JLINK_GetFirmwareString(...)
T82D4 000:033.963 - 0.028ms
T82D4 000:033.985 JLINK_GetHardwareVersion()
T82D4 000:034.004 - 0.028ms returns 94000
T82D4 000:034.026 JLINK_GetSN()
T82D4 000:034.046 - 0.028ms returns 59406895
T82D4 000:034.068 JLINK_GetOEMString(...)
T82D4 000:034.100 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:034.600 - 0.519ms returns 0x00
T82D4 000:034.632 JLINK_HasError()
T82D4 000:034.666 JLINK_SetSpeed(2000)
T82D4 000:034.745 - 0.092ms
T82D4 000:034.770 JLINK_GetId()
T82D4 000:035.505   Found SW-DP with ID 0x2BA01477
T82D4 000:038.109   DPIDR: 0x2BA01477
T82D4 000:038.214   Scanning AP map to find all available APs
T82D4 000:038.712   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:038.749   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:038.781   Iterating through AP map to find AHB-AP to use
T82D4 000:039.555   AP[0]: Core found
T82D4 000:039.592   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.051   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.089   Found Cortex-M4 r0p1, Little endian.
