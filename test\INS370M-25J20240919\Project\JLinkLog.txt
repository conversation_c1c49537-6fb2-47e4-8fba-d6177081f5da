T82D4 000:012.574   SEGGER J-Link V7.22b Log File
T82D4 000:012.898   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.926   Logging started @ 2025-06-11 02:20
T82D4 000:012.952 - 12.963ms
T82D4 000:012.985 JLINK_SetWarnOutHandler(...)
T82D4 000:013.014 - 0.039ms
T82D4 000:013.036 JLINK_OpenEx(...)
T82D4 000:014.487   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.764   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.943   Decompressing FW timestamp took 122 us
T82D4 000:020.663   Hardware: V9.40
T82D4 000:020.708   S/N: 59406895
T82D4 000:020.741   OEM: SEGGER
T82D4 000:020.770   Feature(s): <PERSON><PERSON>, GD<PERSON>, FlashDL, <PERSON>B<PERSON>, J<PERSON>lash
T82D4 000:021.602   TELNET listener socket opened on port 19021
T82D4 000:021.793   WEBSRV Starting webserver
T82D4 000:021.951   WEBSRV Webserver running on local port 19080
T82D4 000:021.987 - 8.959ms returns "O.K."
T82D4 000:022.016 JLINK_GetEmuCaps()
T82D4 000:022.036 - 0.030ms returns 0xB9FF7BBF
T82D4 000:022.060 JLINK_TIF_GetAvailable(...)
T82D4 000:022.183 - 0.138ms
T82D4 000:022.210 JLINK_SetErrorOutHandler(...)
T82D4 000:022.230 - 0.029ms
T82D4 000:022.265 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.125   Device "GD32F450II" selected.
T82D4 000:032.762 - 10.513ms returns 0x00
T82D4 000:032.808 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.528   Device "GD32F450II" selected.
T82D4 000:034.160 - 1.346ms returns 0x00
T82D4 000:034.199 JLINK_GetHardwareVersion()
T82D4 000:034.219 - 0.029ms returns 94000
T82D4 000:034.239 JLINK_GetDLLVersion()
T82D4 000:034.260 - 0.030ms returns 72202
T82D4 000:034.280 JLINK_GetOEMString(...)
T82D4 000:034.344 JLINK_GetFirmwareString(...)
T82D4 000:034.369 - 0.040ms
T82D4 000:034.425 JLINK_GetDLLVersion()
T82D4 000:034.457 - 0.046ms returns 72202
T82D4 000:034.486 JLINK_GetCompileDateTime()
T82D4 000:034.514 - 0.041ms
T82D4 000:034.552 JLINK_GetFirmwareString(...)
T82D4 000:034.585 - 0.045ms
T82D4 000:034.619 JLINK_GetHardwareVersion()
T82D4 000:034.651 - 0.048ms returns 94000
T82D4 000:034.691 JLINK_GetSN()
T82D4 000:034.720 - 0.042ms returns 59406895
T82D4 000:034.754 JLINK_GetOEMString(...)
T82D4 000:034.815 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.343 - 0.548ms returns 0x00
T82D4 000:035.377 JLINK_HasError()
T82D4 000:035.413 JLINK_SetSpeed(2000)
T82D4 000:035.491 - 0.092ms
T82D4 000:035.518 JLINK_GetId()
T82D4 000:036.119   Found SW-DP with ID 0x2BA01477
T82D4 000:038.409   DPIDR: 0x2BA01477
T82D4 000:038.452   Scanning AP map to find all available APs
T82D4 000:038.939   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:038.978   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:039.009   Iterating through AP map to find AHB-AP to use
T82D4 000:039.794   AP[0]: Core found
T82D4 000:039.831   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.282   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.323   Found Cortex-M4 r0p1, Little endian.
T82D4 000:141.508   -- Max. mem block: 0x00010E60
T82D4 000:141.681   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:142.050   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:142.598   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:143.041   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:143.118   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:143.505   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:143.924   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:144.316   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:144.686   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:145.003   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:145.442   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:145.775   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:146.114   CoreSight components:
T82D4 000:146.150   ROMTbl[0] @ E00FF000
T82D4 000:146.179   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:146.997   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:147.618   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:147.728   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:148.329   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:148.361   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:148.948   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:149.038   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:149.612   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:149.744   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:150.340   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:150.435   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:151.002   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:151.266 - 115.762ms returns 0x2BA01477
T82D4 000:151.296 JLINK_GetDLLVersion()
T82D4 000:151.335 - 0.047ms returns 72202
T82D4 000:151.355 JLINK_CORE_GetFound()
T82D4 000:151.409 - 0.062ms returns 0xE0000FF
T82D4 000:151.428 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:151.463   Value=0xE00FF000
T82D4 000:151.511 - 0.092ms returns 0
T82D4 000:151.573 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:151.593   Value=0xE00FF000
T82D4 000:151.634 - 0.069ms returns 0
T82D4 000:151.651 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:151.691   Value=0x00000000
T82D4 000:151.717 - 0.073ms returns 0
T82D4 000:151.801 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:151.854   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:152.266   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:152.298 - 0.504ms returns 16 (0x10)
T82D4 000:152.316 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:152.335   Value=0x00000000
T82D4 000:152.359 - 0.050ms returns 0
T82D4 000:152.377 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:152.394   Value=0x********
T82D4 000:152.418 - 0.050ms returns 0
T82D4 000:152.437 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:152.454   Value=0x********
T82D4 000:152.478 - 0.050ms returns 0
T82D4 000:152.496 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:152.513   Value=0xE0001000
T82D4 000:152.538 - 0.051ms returns 0
T82D4 000:152.556 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:152.573   Value=0xE0002000
T82D4 000:152.599 - 0.050ms returns 0
T82D4 000:152.616 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:152.633   Value=0xE000E000
T82D4 000:152.658 - 0.050ms returns 0
T82D4 000:152.674 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:152.693   Value=0xE000EDF0
T82D4 000:152.717 - 0.050ms returns 0
T82D4 000:152.734 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:152.752   Value=0x00000001
T82D4 000:152.775 - 0.049ms returns 0
T82D4 000:152.792 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:152.814   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:153.148   Data:  41 C2 0F 41
T82D4 000:153.181   Debug reg: CPUID
T82D4 000:153.206 - 0.421ms returns 1 (0x1)
T82D4 000:153.224 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:153.242   Value=0x00000000
T82D4 000:153.266 - 0.050ms returns 0
T82D4 000:153.285 JLINK_HasError()
T82D4 000:153.303 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:153.322 - 0.027ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:153.340 JLINK_Reset()
T82D4 000:153.366   CPU is running
T82D4 000:153.394   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:153.720   CPU is running
T82D4 000:153.751   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:154.078   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:154.556   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:154.587   CPU is running
T82D4 000:154.613   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:208.182   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:208.749   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.160   CPU is running
T82D4 000:209.191   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.583   CPU is running
T82D4 000:209.674   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:215.634   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:219.760   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:220.233   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:220.664   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:221.081 - 67.760ms
T82D4 000:221.125 JLINK_HasError()
T82D4 000:221.146 JLINK_ReadReg(R15 (PC))
T82D4 000:221.173 - 0.037ms returns 0x080001C4
T82D4 000:221.194 JLINK_ReadReg(XPSR)
T82D4 000:221.212 - 0.026ms returns 0x01000000
T82D4 000:221.314 JLINK_Halt()
T82D4 000:221.344 - 0.038ms returns 0x00
T82D4 000:221.363 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:221.387   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:221.790   Data:  03 00 03 00
T82D4 000:221.827   Debug reg: DHCSR
T82D4 000:221.852 - 0.498ms returns 1 (0x1)
T82D4 000:221.871 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:221.893   Debug reg: DHCSR
T82D4 000:222.122   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:222.512 - 0.660ms returns 0 (0x00000000)
T82D4 000:222.543 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:222.564   Debug reg: DEMCR
T82D4 000:222.599   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:222.997 - 0.473ms returns 0 (0x00000000)
T82D4 000:223.061 JLINK_GetHWStatus(...)
T82D4 000:223.234 - 0.192ms returns 0
T82D4 000:223.279 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:223.299 - 0.029ms returns 0x06
T82D4 000:223.317 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:223.334 - 0.026ms returns 0x2000
T82D4 000:223.353 JLINK_GetNumWPUnits()
T82D4 000:223.369 - 0.024ms returns 4
T82D4 000:223.396 JLINK_GetSpeed()
T82D4 000:223.414 - 0.026ms returns 2000
T82D4 000:223.436 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.460   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.832   Data:  02 00 00 00
T82D4 000:223.868 - 0.440ms returns 1 (0x1)
T82D4 000:223.887 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.909   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:224.256   Data:  02 00 00 00
T82D4 000:224.294 - 0.415ms returns 1 (0x1)
T82D4 000:224.313 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:224.331   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:224.362   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:224.974 - 0.680ms returns 0x1C
T82D4 000:225.014 JLINK_HasError()
T82D4 000:225.035 JLINK_ReadReg(R15 (PC))
T82D4 000:225.054 - 0.028ms returns 0x080001C4
T82D4 000:225.074 JLINK_ReadReg(XPSR)
T82D4 000:225.093 - 0.027ms returns 0x01000000
T82D4 000:227.461 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:227.493   Data:  00 00 00 00
T82D4 000:227.519   Debug reg: DWT_CYCCNT
T82D4 000:227.543 - 0.089ms returns 4 (0x4)
T82D4 000:307.516 JLINK_HasError()
T82D4 000:307.575 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:307.593 - 0.026ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:307.611 JLINK_Reset()
T82D4 000:307.638   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:308.018   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:308.485   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:308.860   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:308.908   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:362.224   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:362.925   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:363.671   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:364.430   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:370.195   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:374.495   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:374.916   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:375.341   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:375.787 - 68.189ms
T82D4 000:375.909 JLINK_HasError()
T82D4 000:375.933 JLINK_ReadReg(R15 (PC))
T82D4 000:375.954 - 0.029ms returns 0x080001C4
T82D4 000:375.972 JLINK_ReadReg(XPSR)
T82D4 000:375.991 - 0.027ms returns 0x01000000
T82D4 000:376.072 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:376.098   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:376.937    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:376.967    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:376.992   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:377.017 - 0.953ms returns 60 (0x3C)
T82D4 000:377.035 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.054    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:377.079   Data:  06 48
T82D4 000:377.103 - 0.075ms returns 2 (0x2)
T82D4 000:377.160 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.180    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:377.207   Data:  80 47
T82D4 000:377.231 - 0.078ms returns 2 (0x2)
T82D4 000:377.269 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.289    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:377.314   Data:  80 47
T82D4 000:377.339 - 0.079ms returns 2 (0x2)
T82D4 000:377.357 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:377.375   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:378.254    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:378.283    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:378.310   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:378.334 - 0.984ms returns 60 (0x3C)
T82D4 000:378.351 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.370    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:378.395   Data:  06 48
T82D4 000:378.420 - 0.076ms returns 2 (0x2)
T82D4 000:378.444 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:378.463    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:378.489   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:378.513 - 0.077ms returns 60 (0x3C)
T82D4 000:378.530 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.549    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:378.574   Data:  06 48
T82D4 000:378.599 - 0.077ms returns 2 (0x2)
T82D4 000:378.617 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.633    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:378.659   Data:  00 47
T82D4 000:378.683 - 0.074ms returns 2 (0x2)
T82D4 000:378.706 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.725    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:378.749   Data:  00 47
T82D4 000:378.775 - 0.077ms returns 2 (0x2)
T82D4 000:378.793 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:378.809    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:378.836   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:378.859 - 0.074ms returns 60 (0x3C)
T82D4 000:378.876 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.895    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:378.919   Data:  FE E7
T82D4 000:378.945 - 0.077ms returns 2 (0x2)
T82D4 000:378.968 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:378.986    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:379.012   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:379.036 - 0.075ms returns 60 (0x3C)
T82D4 000:379.053 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:379.071    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:379.095   Data:  FE E7
T82D4 000:379.120 - 0.075ms returns 2 (0x2)
T82D4 000:379.138 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:379.155    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:379.181   Data:  FE E7
T82D4 000:379.206 - 0.076ms returns 2 (0x2)
