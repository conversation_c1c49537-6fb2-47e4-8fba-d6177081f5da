T82D4 000:012.210   SEGGER J-Link V7.22b Log File
T82D4 000:012.655   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.683   Logging started @ 2025-06-11 06:56
T82D4 000:012.707 - 12.718ms
T82D4 000:012.739 JLINK_SetWarnOutHandler(...)
T82D4 000:012.769 - 0.038ms
T82D4 000:012.806 JLINK_OpenEx(...)
T82D4 000:014.219   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.480   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.642   Decompressing FW timestamp took 113 us
T82D4 000:020.290   Hardware: V9.40
T82D4 000:020.331   S/N: 59406895
T82D4 000:020.360   OEM: SEGGER
T82D4 000:020.389   Feature(s): R<PERSON>, G<PERSON><PERSON>, <PERSON><PERSON><PERSON>, FlashB<PERSON>, J<PERSON>lash
T82D4 000:021.189   TELNET listener socket opened on port 19021
T82D4 000:021.354   WEBSRV Starting webserver
T82D4 000:021.497   WEBSRV Webserver running on local port 19080
T82D4 000:021.531 - 8.735ms returns "O.K."
T82D4 000:021.558 JLINK_GetEmuCaps()
T82D4 000:021.578 - 0.028ms returns 0xB9FF7BBF
T82D4 000:021.599 JLINK_TIF_GetAvailable(...)
T82D4 000:021.765 - 0.179ms
T82D4 000:021.790 JLINK_SetErrorOutHandler(...)
T82D4 000:021.807 - 0.025ms
T82D4 000:021.841 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.065   Device "GD32F450II" selected.
T82D4 000:032.682 - 10.858ms returns 0x00
T82D4 000:032.726 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.397   Device "GD32F450II" selected.
T82D4 000:033.910 - 1.177ms returns 0x00
T82D4 000:033.944 JLINK_GetHardwareVersion()
T82D4 000:033.964 - 0.028ms returns 94000
T82D4 000:033.983 JLINK_GetDLLVersion()
T82D4 000:034.000 - 0.027ms returns 72202
T82D4 000:034.020 JLINK_GetOEMString(...)
T82D4 000:034.040 JLINK_GetFirmwareString(...)
T82D4 000:034.062 - 0.031ms
T82D4 000:034.092 JLINK_GetDLLVersion()
T82D4 000:034.121 - 0.037ms returns 72202
T82D4 000:034.141 JLINK_GetCompileDateTime()
T82D4 000:034.159 - 0.027ms
T82D4 000:034.183 JLINK_GetFirmwareString(...)
T82D4 000:034.203 - 0.029ms
T82D4 000:034.226 JLINK_GetHardwareVersion()
T82D4 000:034.246 - 0.029ms returns 94000
T82D4 000:034.269 JLINK_GetSN()
T82D4 000:034.290 - 0.029ms returns 59406895
T82D4 000:034.312 JLINK_GetOEMString(...)
T82D4 000:034.346 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:034.815 - 0.486ms returns 0x00
T82D4 000:034.844 JLINK_HasError()
T82D4 000:034.875 JLINK_SetSpeed(2000)
T82D4 000:034.950 - 0.089ms
T82D4 000:034.976 JLINK_GetId()
T82D4 000:035.540   Found SW-DP with ID 0x2BA01477
T82D4 000:038.218   DPIDR: 0x2BA01477
T82D4 000:038.263   Scanning AP map to find all available APs
T82D4 000:038.777   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:038.813   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:038.847   Iterating through AP map to find AHB-AP to use
T82D4 000:039.661   AP[0]: Core found
T82D4 000:039.697   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.142   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.177   Found Cortex-M4 r0p1, Little endian.
T82D4 000:141.003   -- Max. mem block: 0x00010E60
T82D4 000:141.184   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:141.578   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:142.052   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:142.477   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:142.515   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:142.906   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:143.316   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:143.768   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:144.184   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:144.668   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:145.070   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:145.529   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:145.926   CoreSight components:
T82D4 000:145.967   ROMTbl[0] @ E00FF000
T82D4 000:145.996   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:146.901   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:147.672   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:147.712   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:148.383   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:148.423   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:149.189   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:149.228   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:149.825   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:149.863   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:150.658   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:150.696   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:151.467   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:151.805 - 116.849ms returns 0x2BA01477
T82D4 000:151.841 JLINK_GetDLLVersion()
T82D4 000:151.860 - 0.027ms returns 72202
T82D4 000:151.972 JLINK_CORE_GetFound()
T82D4 000:152.002 - 0.038ms returns 0xE0000FF
T82D4 000:152.021 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.040   Value=0xE00FF000
T82D4 000:152.065 - 0.052ms returns 0
T82D4 000:152.091 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.111   Value=0xE00FF000
T82D4 000:152.137 - 0.055ms returns 0
T82D4 000:152.155 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:152.172   Value=0x00000000
T82D4 000:152.199 - 0.051ms returns 0
T82D4 000:152.223 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:152.260   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:152.790   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:152.828 - 0.653ms returns 16 (0x10)
T82D4 000:152.903 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:152.922   Value=0x00000000
T82D4 000:152.989 - 0.116ms returns 0
T82D4 000:153.045 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:153.091   Value=0x********
T82D4 000:153.131 - 0.095ms returns 0
T82D4 000:153.150 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:153.168   Value=0x********
T82D4 000:153.215 - 0.073ms returns 0
T82D4 000:153.251 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:153.291   Value=0xE0001000
T82D4 000:153.333 - 0.091ms returns 0
T82D4 000:153.351 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:153.368   Value=0xE0002000
T82D4 000:153.394 - 0.051ms returns 0
T82D4 000:153.411 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:153.428   Value=0xE000E000
T82D4 000:153.478 - 0.090ms returns 0
T82D4 000:153.531 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:153.551   Value=0xE000EDF0
T82D4 000:153.591 - 0.088ms returns 0
T82D4 000:153.644 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:153.663   Value=0x00000001
T82D4 000:153.688 - 0.051ms returns 0
T82D4 000:153.706 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:153.730   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:154.091   Data:  41 C2 0F 41
T82D4 000:154.129   Debug reg: CPUID
T82D4 000:154.179 - 0.484ms returns 1 (0x1)
T82D4 000:154.209 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:154.242   Value=0x00000000
T82D4 000:154.267 - 0.066ms returns 0
T82D4 000:154.286 JLINK_HasError()
T82D4 000:154.306 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:154.374 - 0.078ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:154.396 JLINK_Reset()
T82D4 000:154.510   CPU is running
T82D4 000:154.546   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:154.914   CPU is running
T82D4 000:154.950   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:155.299   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:155.607   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:155.638   CPU is running
T82D4 000:155.665   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:208.365   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.151   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.942   CPU is running
T82D4 000:210.137   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:210.913   CPU is running
T82D4 000:211.369   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:218.314   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:222.369   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:222.788   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:223.157   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:223.529 - 69.148ms
T82D4 000:223.616 JLINK_HasError()
T82D4 000:223.639 JLINK_ReadReg(R15 (PC))
T82D4 000:223.664 - 0.034ms returns 0x080001C4
T82D4 000:223.683 JLINK_ReadReg(XPSR)
T82D4 000:223.703 - 0.028ms returns 0x01000000
T82D4 000:223.722 JLINK_Halt()
T82D4 000:223.740 - 0.026ms returns 0x00
T82D4 000:223.758 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:223.780   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:224.146   Data:  03 00 03 00
T82D4 000:224.177   Debug reg: DHCSR
T82D4 000:224.201 - 0.451ms returns 1 (0x1)
T82D4 000:224.220 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:224.242   Debug reg: DHCSR
T82D4 000:224.461   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:224.889 - 0.682ms returns 0 (0x00000000)
T82D4 000:224.914 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:224.932   Debug reg: DEMCR
T82D4 000:224.962   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:225.385 - 0.484ms returns 0 (0x00000000)
T82D4 000:225.441 JLINK_GetHWStatus(...)
T82D4 000:225.590 - 0.161ms returns 0
T82D4 000:225.626 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:225.646 - 0.029ms returns 0x06
T82D4 000:225.664 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:225.681 - 0.025ms returns 0x2000
T82D4 000:225.700 JLINK_GetNumWPUnits()
T82D4 000:225.718 - 0.025ms returns 4
T82D4 000:225.743 JLINK_GetSpeed()
T82D4 000:225.763 - 0.028ms returns 2000
T82D4 000:225.787 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:225.810   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:226.169   Data:  02 00 00 00
T82D4 000:226.199 - 0.420ms returns 1 (0x1)
T82D4 000:226.219 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:226.239   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:226.620   Data:  02 00 00 00
T82D4 000:226.660 - 0.450ms returns 1 (0x1)
T82D4 000:226.680 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:226.699   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:226.730   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:227.293 - 0.721ms returns 0x1C
T82D4 000:227.420 JLINK_HasError()
T82D4 000:227.440 JLINK_ReadReg(R15 (PC))
T82D4 000:227.459 - 0.028ms returns 0x080001C4
T82D4 000:227.477 JLINK_ReadReg(XPSR)
T82D4 000:227.496 - 0.027ms returns 0x01000000
T82D4 000:230.498 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:230.534   Data:  00 00 00 00
T82D4 000:230.624   Debug reg: DWT_CYCCNT
T82D4 000:230.651 - 0.162ms returns 4 (0x4)
T82D4 000:313.607 JLINK_HasError()
T82D4 000:313.667 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:313.685 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:313.706 JLINK_Reset()
T82D4 000:313.733   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:314.090   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:314.431   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:314.770   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:314.803   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:368.414   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:369.170   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:369.705   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:370.400   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:376.762   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:380.803   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:381.216   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:381.654   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:382.066 - 68.379ms
T82D4 000:382.234 JLINK_HasError()
T82D4 000:382.266 JLINK_ReadReg(R15 (PC))
T82D4 000:382.286 - 0.029ms returns 0x080001C4
T82D4 000:382.304 JLINK_ReadReg(XPSR)
T82D4 000:382.323 - 0.027ms returns 0x01000000
T82D4 000:382.401 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:382.427   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:383.435    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:383.472    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:383.498   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:383.523 - 1.130ms returns 60 (0x3C)
T82D4 000:383.542 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.560    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:383.587   Data:  06 48
T82D4 000:383.611 - 0.077ms returns 2 (0x2)
T82D4 000:383.667 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.688    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:383.714   Data:  80 47
T82D4 000:383.738 - 0.079ms returns 2 (0x2)
T82D4 000:383.770 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.791    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:383.816   Data:  80 47
T82D4 000:383.840 - 0.079ms returns 2 (0x2)
T82D4 000:383.859 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:383.878   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:384.797    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:384.847    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:384.893   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:384.918 - 1.067ms returns 60 (0x3C)
T82D4 000:384.937 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:384.957    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:384.982   Data:  06 48
T82D4 000:385.008 - 0.080ms returns 2 (0x2)
T82D4 000:385.035 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:385.054    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:385.081   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:385.105 - 0.077ms returns 60 (0x3C)
T82D4 000:385.122 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.141    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:385.166   Data:  06 48
T82D4 000:385.191 - 0.078ms returns 2 (0x2)
T82D4 000:385.210 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.228    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:385.254   Data:  00 47
T82D4 000:385.279 - 0.076ms returns 2 (0x2)
T82D4 000:385.302 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.323    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:385.348   Data:  00 47
T82D4 000:385.373 - 0.079ms returns 2 (0x2)
T82D4 000:385.391 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:385.409    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:385.465   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:385.490 - 0.106ms returns 60 (0x3C)
T82D4 000:385.507 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.526    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:385.552   Data:  FE E7
T82D4 000:385.577 - 0.078ms returns 2 (0x2)
T82D4 000:385.600 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:385.648    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:385.691   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:385.754 - 0.161ms returns 60 (0x3C)
T82D4 000:385.792 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.813    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:385.852   Data:  FE E7
T82D4 000:385.878 - 0.114ms returns 2 (0x2)
T82D4 000:385.917 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:385.935    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:385.963   Data:  FE E7
T82D4 000:385.988 - 0.080ms returns 2 (0x2)
T82D4 000:386.016 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:386.038    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:386.081   Data:  FE E7
T82D4 000:386.107 - 0.098ms returns 2 (0x2)
T82D4 000:386.124 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:386.141    -- Read from C cache (60 bytes @ 0x080001D0)
T82D4 000:386.253   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:386.283 - 0.169ms returns 60 (0x3C)
T82D4 000:386.305 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:386.329    -- Read from C cache (2 bytes @ 0x080001D0)
T82D4 000:386.362   Data:  FE E7
T82D4 000:386.396 - 0.102ms returns 2 (0x2)
T82D4 001:609.820 JLINK_ReadMemEx(0x080161FE, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:609.869   CPU_ReadMem(64 bytes @ 0x080161C0)
T82D4 001:610.691    -- Updating C cache (64 bytes @ 0x080161C0)
T82D4 001:610.738    -- Read from C cache (2 bytes @ 0x080161FE)
T82D4 001:610.766   Data:  12 48
T82D4 001:610.795 - 0.983ms returns 2 (0x2)
T82D4 001:610.857 JLINK_ReadMemEx(0x08016200, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:610.882   CPU_ReadMem(64 bytes @ 0x08016200)
T82D4 001:611.695    -- Updating C cache (64 bytes @ 0x08016200)
T82D4 001:611.733    -- Read from C cache (60 bytes @ 0x08016200)
T82D4 001:611.760   Data:  B4 FB F0 F1 00 FB 11 40 48 B9 01 20 10 49 08 70 ...
T82D4 001:611.789 - 0.941ms returns 60 (0x3C)
T82D4 001:611.809 JLINK_ReadMemEx(0x08016200, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:611.831    -- Read from C cache (2 bytes @ 0x08016200)
T82D4 001:611.858   Data:  B4 FB
T82D4 001:611.884 - 0.084ms returns 2 (0x2)
T82D4 001:611.912 JLINK_ReadMemEx(0x08016200, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:611.933    -- Read from C cache (60 bytes @ 0x08016200)
T82D4 001:611.961   Data:  B4 FB F0 F1 00 FB 11 40 48 B9 01 20 10 49 08 70 ...
T82D4 001:611.988 - 0.084ms returns 60 (0x3C)
T82D4 001:612.007 JLINK_ReadMemEx(0x08016200, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:612.027    -- Read from C cache (2 bytes @ 0x08016200)
T82D4 001:612.055   Data:  B4 FB
T82D4 001:612.081 - 0.084ms returns 2 (0x2)
T82D4 001:612.102 JLINK_ReadMemEx(0x08016202, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:612.122    -- Read from C cache (2 bytes @ 0x08016202)
T82D4 001:612.149   Data:  F0 F1
T82D4 001:612.177 - 0.083ms returns 2 (0x2)
T82D4 001:612.199 JLINK_ReadMemEx(0x08016204, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:612.220    -- Read from C cache (60 bytes @ 0x08016204)
T82D4 001:612.248   Data:  00 FB 11 40 48 B9 01 20 10 49 08 70 0E 48 00 68 ...
T82D4 001:612.274 - 0.085ms returns 60 (0x3C)
T82D4 001:612.295 JLINK_ReadMemEx(0x08016204, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:612.314    -- Read from C cache (2 bytes @ 0x08016204)
T82D4 001:612.341   Data:  00 FB
T82D4 001:612.368 - 0.082ms returns 2 (0x2)
T82D4 001:612.388 JLINK_ReadMemEx(0x08016206, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:612.409    -- Read from C cache (2 bytes @ 0x08016206)
T82D4 001:612.436   Data:  11 40
T82D4 001:612.463 - 0.084ms returns 2 (0x2)
T82D4 001:612.486 JLINK_ReadMemEx(0x08016208, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:612.506   CPU_ReadMem(64 bytes @ 0x08016240)
T82D4 001:613.291    -- Updating C cache (64 bytes @ 0x08016240)
T82D4 001:613.324    -- Read from C cache (60 bytes @ 0x08016208)
T82D4 001:613.351   Data:  48 B9 01 20 10 49 08 70 0E 48 00 68 40 1C 0D 49 ...
T82D4 001:613.379 - 0.903ms returns 60 (0x3C)
T82D4 001:613.400 JLINK_ReadMemEx(0x08016208, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:613.420    -- Read from C cache (2 bytes @ 0x08016208)
T82D4 001:613.448   Data:  48 B9
T82D4 001:613.474 - 0.082ms returns 2 (0x2)
T82D4 001:613.493 JLINK_ReadMemEx(0x0801620A, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:613.513    -- Read from C cache (2 bytes @ 0x0801620A)
T82D4 001:613.540   Data:  01 20
T82D4 001:613.568 - 0.083ms returns 2 (0x2)
T82D4 001:774.228 JLINK_HasError()
T82D4 001:774.291 JLINK_ReadReg(R0)
T82D4 001:774.633 - 0.358ms returns 0x00002D6C
T82D4 001:774.661 JLINK_ReadReg(R1)
T82D4 001:774.681 - 0.030ms returns 0x00000000
T82D4 001:774.701 JLINK_ReadReg(R2)
T82D4 001:774.720 - 0.029ms returns 0x200229E1
T82D4 001:774.741 JLINK_ReadReg(R3)
T82D4 001:774.759 - 0.027ms returns 0x200229E0
T82D4 001:774.779 JLINK_ReadReg(R4)
T82D4 001:774.799 - 0.029ms returns 0x00002D6C
T82D4 001:774.818 JLINK_ReadReg(R5)
T82D4 001:774.838 - 0.034ms returns 0x00000000
T82D4 001:774.865 JLINK_ReadReg(R6)
T82D4 001:774.884 - 0.029ms returns 0x00000000
T82D4 001:774.905 JLINK_ReadReg(R7)
T82D4 001:774.923 - 0.027ms returns 0x00000000
T82D4 001:774.943 JLINK_ReadReg(R8)
T82D4 001:774.976 - 0.042ms returns 0x00000000
T82D4 001:774.999 JLINK_ReadReg(R9)
T82D4 001:775.018 - 0.029ms returns 0x20000348
T82D4 001:775.039 JLINK_ReadReg(R10)
T82D4 001:775.059 - 0.030ms returns 0x00000000
T82D4 001:775.080 JLINK_ReadReg(R11)
T82D4 001:775.100 - 0.029ms returns 0x00000000
T82D4 001:775.120 JLINK_ReadReg(R12)
T82D4 001:775.140 - 0.029ms returns 0x000001E0
T82D4 001:775.161 JLINK_ReadReg(R13 (SP))
T82D4 001:775.181 - 0.029ms returns 0x20008038
T82D4 001:775.201 JLINK_ReadReg(R14)
T82D4 001:775.221 - 0.030ms returns 0xFFFFFFFF
T82D4 001:775.242 JLINK_ReadReg(R15 (PC))
T82D4 001:775.261 - 0.029ms returns 0x080001C4
T82D4 001:775.283 JLINK_ReadReg(XPSR)
T82D4 001:775.302 - 0.028ms returns 0x01000000
T82D4 001:775.324 JLINK_ReadReg(MSP)
T82D4 001:775.344 - 0.029ms returns 0x20008038
T82D4 001:775.364 JLINK_ReadReg(PSP)
T82D4 001:775.384 - 0.030ms returns 0x20001000
T82D4 001:775.405 JLINK_ReadReg(CFBP)
T82D4 001:775.425 - 0.029ms returns 0x00000000
T82D4 001:775.445 JLINK_ReadReg(FPSCR)
T82D4 001:780.844 - 5.441ms returns 0x00000000
T82D4 001:780.901 JLINK_ReadReg(FPS0)
T82D4 001:780.927 - 0.036ms returns 0x00000000
T82D4 001:780.949 JLINK_ReadReg(FPS1)
T82D4 001:780.968 - 0.028ms returns 0x3FF00000
T82D4 001:780.989 JLINK_ReadReg(FPS2)
T82D4 001:781.009 - 0.029ms returns 0x40174000
T82D4 001:781.030 JLINK_ReadReg(FPS3)
T82D4 001:781.050 - 0.029ms returns 0x6A610350
T82D4 001:781.070 JLINK_ReadReg(FPS4)
T82D4 001:781.091 - 0.030ms returns 0x004D402C
T82D4 001:781.111 JLINK_ReadReg(FPS5)
T82D4 001:781.131 - 0.029ms returns 0x77490280
T82D4 001:781.151 JLINK_ReadReg(FPS6)
T82D4 001:781.172 - 0.030ms returns 0xD8863040
T82D4 001:781.192 JLINK_ReadReg(FPS7)
T82D4 001:781.212 - 0.029ms returns 0x6F4222A1
T82D4 001:781.233 JLINK_ReadReg(FPS8)
T82D4 001:781.252 - 0.028ms returns 0x1A100861
T82D4 001:781.273 JLINK_ReadReg(FPS9)
T82D4 001:781.292 - 0.028ms returns 0x4C607626
T82D4 001:781.313 JLINK_ReadReg(FPS10)
T82D4 001:781.333 - 0.029ms returns 0x790C9808
T82D4 001:781.353 JLINK_ReadReg(FPS11)
T82D4 001:781.373 - 0.029ms returns 0x432099A0
T82D4 001:781.394 JLINK_ReadReg(FPS12)
T82D4 001:781.414 - 0.029ms returns 0x8A2080C8
T82D4 001:781.434 JLINK_ReadReg(FPS13)
T82D4 001:781.454 - 0.029ms returns 0x1B944171
T82D4 001:781.474 JLINK_ReadReg(FPS14)
T82D4 001:781.494 - 0.029ms returns 0x4502A8C8
T82D4 001:781.515 JLINK_ReadReg(FPS15)
T82D4 001:781.534 - 0.029ms returns 0x192E0105
T82D4 001:781.555 JLINK_ReadReg(FPS16)
T82D4 001:781.574 - 0.028ms returns 0x80A80080
T82D4 001:781.596 JLINK_ReadReg(FPS17)
T82D4 001:781.615 - 0.027ms returns 0x01012210
T82D4 001:781.636 JLINK_ReadReg(FPS18)
T82D4 001:781.655 - 0.028ms returns 0x6FC0024F
T82D4 001:781.675 JLINK_ReadReg(FPS19)
T82D4 001:781.695 - 0.029ms returns 0x0A109945
T82D4 001:781.716 JLINK_ReadReg(FPS20)
T82D4 001:781.736 - 0.029ms returns 0x02052300
T82D4 001:781.755 JLINK_ReadReg(FPS21)
T82D4 001:781.777 - 0.031ms returns 0x2806C828
T82D4 001:781.798 JLINK_ReadReg(FPS22)
T82D4 001:781.817 - 0.029ms returns 0x10036492
T82D4 001:781.838 JLINK_ReadReg(FPS23)
T82D4 001:781.858 - 0.029ms returns 0x89B0B474
T82D4 001:781.878 JLINK_ReadReg(FPS24)
T82D4 001:781.897 - 0.029ms returns 0x8624820A
T82D4 001:781.919 JLINK_ReadReg(FPS25)
T82D4 001:781.937 - 0.028ms returns 0xC00F1403
T82D4 001:781.959 JLINK_ReadReg(FPS26)
T82D4 001:781.978 - 0.028ms returns 0x202A04C0
T82D4 001:781.999 JLINK_ReadReg(FPS27)
T82D4 001:782.019 - 0.029ms returns 0x1D2712AA
T82D4 001:782.039 JLINK_ReadReg(FPS28)
T82D4 001:782.059 - 0.029ms returns 0x544840BB
T82D4 001:782.079 JLINK_ReadReg(FPS29)
T82D4 001:782.099 - 0.030ms returns 0x01482220
T82D4 001:782.120 JLINK_ReadReg(FPS30)
T82D4 001:782.140 - 0.029ms returns 0x0247A931
T82D4 001:782.160 JLINK_ReadReg(FPS31)
T82D4 001:782.182 - 0.034ms returns 0x608C08A3
T82D4 001:841.413 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:841.488   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 001:842.345    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 001:842.471    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:842.498   Data:  00 00
T82D4 001:842.522 - 1.118ms returns 2 (0x2)
T82D4 001:842.617 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:842.642    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:842.670   Data:  00 00
T82D4 001:842.695 - 0.087ms returns 2 (0x2)
T82D4 001:842.728 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:842.750    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:842.796   Data:  00 00
T82D4 001:842.825 - 0.121ms returns 2 (0x2)
T82D4 001:848.696 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.740    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:848.768   Data:  00 00 00 00
T82D4 001:848.794 - 0.106ms returns 4 (0x4)
T82D4 001:848.823 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.844    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:848.871   Data:  00 00 00 00
T82D4 001:848.895 - 0.081ms returns 4 (0x4)
T82D4 001:848.924 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.946    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:848.971   Data:  00 00 00 00
T82D4 001:848.998 - 0.082ms returns 4 (0x4)
T82D4 001:864.560 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:864.626    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:864.653   Data:  00 00 00 00
T82D4 001:864.681 - 0.130ms returns 4 (0x4)
T82D4 001:864.712 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:864.734    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:864.762   Data:  00 00 00 00
T82D4 001:864.787 - 0.084ms returns 4 (0x4)
T82D4 001:864.818 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:864.841    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:864.868   Data:  00 00 00 00
T82D4 001:864.895 - 0.086ms returns 4 (0x4)
T82D4 001:879.615 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:879.680    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:879.706   Data:  00 00 00 00
T82D4 001:879.736 - 0.130ms returns 4 (0x4)
T82D4 001:879.768 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:879.805    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:879.832   Data:  00 00 00 00
T82D4 001:879.856 - 0.096ms returns 4 (0x4)
T82D4 001:879.886 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:879.908    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:879.933   Data:  00 00 00 00
T82D4 001:879.958 - 0.081ms returns 4 (0x4)
T82D4 001:889.414 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:889.480    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:889.511   Data:  00 00 00 00
T82D4 001:889.537 - 0.132ms returns 4 (0x4)
T82D4 001:889.571 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:889.595    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:889.621   Data:  00 00 00 00
T82D4 001:889.647 - 0.085ms returns 4 (0x4)
T82D4 001:889.682 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:889.703    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:889.730   Data:  00 00 00 00
T82D4 001:889.755 - 0.081ms returns 4 (0x4)
T82D4 001:897.475 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:897.540    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:897.567   Data:  00 00 00 00
T82D4 001:897.593 - 0.128ms returns 4 (0x4)
T82D4 001:897.628 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:897.649    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:897.700   Data:  00 00 00 00
T82D4 001:897.727 - 0.108ms returns 4 (0x4)
T82D4 001:897.760 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:897.788    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:897.817   Data:  00 00 00 00
T82D4 001:897.880 - 0.130ms returns 4 (0x4)
T82D4 001:956.628 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:956.700   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 001:957.575    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 001:957.695    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:957.721   Data:  00 00 00 00
T82D4 001:957.748 - 1.129ms returns 4 (0x4)
T82D4 001:957.846 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:957.872    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:957.899   Data:  00 00 00 00
T82D4 001:957.925 - 0.089ms returns 4 (0x4)
T82D4 001:957.962 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:957.983    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:958.033   Data:  00 00 00 00
T82D4 001:958.074 - 0.121ms returns 4 (0x4)
T82D4 001:968.710 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:968.823    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:968.853   Data:  00 00 00 00
T82D4 001:968.882 - 0.183ms returns 4 (0x4)
T82D4 001:968.940 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:968.963    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:968.994   Data:  00 00 00 00
T82D4 001:969.024 - 0.093ms returns 4 (0x4)
T82D4 001:969.061 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:969.084    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:969.111   Data:  00 00 00 00
T82D4 001:969.140 - 0.087ms returns 4 (0x4)
T82D4 001:982.512 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:982.582   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 001:983.466    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 001:983.503    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:983.530   Data:  00 00 00 00
T82D4 001:983.557 - 1.053ms returns 4 (0x4)
T82D4 001:983.602 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:983.627    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:983.654   Data:  00 00 00 00
T82D4 001:983.680 - 0.087ms returns 4 (0x4)
T82D4 001:983.714 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:983.736    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:983.764   Data:  00 00 00 00
T82D4 001:983.790 - 0.084ms returns 4 (0x4)
T82D4 001:995.367 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:995.435   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 001:996.303    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 001:996.420    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:996.446   Data:  00 00 00 00
T82D4 001:996.473 - 1.115ms returns 4 (0x4)
T82D4 001:996.513 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:996.536    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:996.564   Data:  00 00 00 00
T82D4 001:996.589 - 0.084ms returns 4 (0x4)
T82D4 001:996.622 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:996.645    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:996.672   Data:  00 00 00 00
T82D4 001:996.700 - 0.086ms returns 4 (0x4)
T82D4 002:007.810 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:007.876    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:007.905   Data:  00 00 00 00
T82D4 002:007.930 - 0.129ms returns 4 (0x4)
T82D4 002:007.967 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:007.990    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:008.017   Data:  00 00 00 00
T82D4 002:008.043 - 0.085ms returns 4 (0x4)
T82D4 002:008.076 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:008.097    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:008.125   Data:  00 00 00 00
T82D4 002:008.150 - 0.083ms returns 4 (0x4)
T82D4 002:021.039 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:021.110   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:022.115    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:022.159    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:022.186   Data:  71
T82D4 002:022.211 - 1.182ms returns 1 (0x1)
T82D4 002:022.260 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:022.284    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:022.312   Data:  71
T82D4 002:022.338 - 0.086ms returns 1 (0x1)
T82D4 002:022.374 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:022.401    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:022.428   Data:  71
T82D4 002:022.455 - 0.091ms returns 1 (0x1)
T82D4 002:035.014 JLINK_ReadMemEx(0x20019D64, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:035.110   CPU_ReadMem(128 bytes @ 0x20019D40)
T82D4 002:036.530    -- Updating C cache (128 bytes @ 0x20019D40)
T82D4 002:036.656    -- Read from C cache (32 bytes @ 0x20019D64)
T82D4 002:036.689   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:036.721 - 1.717ms returns 32 (0x20)
T82D4 002:115.725 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:115.784    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:115.813   Data:  00 00 00 00
T82D4 002:115.839 - 0.122ms returns 4 (0x4)
T82D4 002:115.875 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:115.899    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:115.925   Data:  00 00 00 00
T82D4 002:115.953 - 0.086ms returns 4 (0x4)
T82D4 002:115.987 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:116.010    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:116.036   Data:  00 00 00 00
T82D4 002:116.062 - 0.084ms returns 4 (0x4)
T82D4 002:130.003 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:130.064    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:130.093   Data:  00 00 00 00
T82D4 002:130.120 - 0.125ms returns 4 (0x4)
T82D4 002:130.153 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:130.177    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:130.204   Data:  00 00 00 00
T82D4 002:130.229 - 0.086ms returns 4 (0x4)
T82D4 002:130.265 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:130.288    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:130.315   Data:  00 00 00 00
T82D4 002:130.342 - 0.085ms returns 4 (0x4)
T82D4 002:143.218 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:143.278    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:143.305   Data:  00 00 00 00
T82D4 002:143.331 - 0.122ms returns 4 (0x4)
T82D4 002:143.363 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:143.385    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:143.413   Data:  00 00 00 00
T82D4 002:143.437 - 0.082ms returns 4 (0x4)
T82D4 002:143.469 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:143.491    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:143.516   Data:  00 00 00 00
T82D4 002:143.542 - 0.081ms returns 4 (0x4)
T82D4 002:209.129 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:209.193    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:209.221   Data:  71
T82D4 002:209.245 - 0.124ms returns 1 (0x1)
T82D4 002:209.281 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:209.308    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:209.334   Data:  71
T82D4 002:209.360 - 0.087ms returns 1 (0x1)
T82D4 002:209.392 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:209.414    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:209.440   Data:  71
T82D4 002:209.464 - 0.079ms returns 1 (0x1)
T82D4 002:301.761 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:301.871    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:301.924   Data:  00 00 00 00
T82D4 002:302.017 - 0.266ms returns 4 (0x4)
T82D4 002:302.109 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:302.145    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:302.176   Data:  00 00 00 00
T82D4 002:302.203 - 0.101ms returns 4 (0x4)
T82D4 002:302.239 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:302.262    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:302.288   Data:  00 00 00 00
T82D4 002:302.334 - 0.105ms returns 4 (0x4)
T82D4 002:315.870 JLINK_ReadMemEx(0x08018330, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:315.931   CPU_ReadMem(64 bytes @ 0x08018300)
T82D4 002:316.877    -- Updating C cache (64 bytes @ 0x08018300)
T82D4 002:317.010    -- Read from C cache (8 bytes @ 0x08018330)
T82D4 002:317.036   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:317.062 - 1.200ms returns 8 (0x8)
T82D4 002:317.096 JLINK_ReadMemEx(0x08018330, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:317.118    -- Read from C cache (8 bytes @ 0x08018330)
T82D4 002:317.143   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:317.167 - 0.080ms returns 8 (0x8)
T82D4 002:317.203 JLINK_ReadMemEx(0x08018330, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:317.224    -- Read from C cache (8 bytes @ 0x08018330)
T82D4 002:317.250   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:317.276 - 0.080ms returns 8 (0x8)
T82D4 002:342.680 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:342.748   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:343.805    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:343.865    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:343.908   Data:  00 00 00 00
T82D4 002:343.934 - 1.262ms returns 4 (0x4)
T82D4 002:344.008 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:344.047    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:344.072   Data:  00 00 00 00
T82D4 002:344.096 - 0.098ms returns 4 (0x4)
T82D4 002:344.153 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:344.192    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:344.218   Data:  00 00 00 00
T82D4 002:344.243 - 0.098ms returns 4 (0x4)
T82D4 002:408.444 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:408.509    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:408.539   Data:  00 00 00 00
T82D4 002:408.564 - 0.128ms returns 4 (0x4)
T82D4 002:408.598 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:408.621    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:408.646   Data:  00 00 00 00
T82D4 002:408.672 - 0.082ms returns 4 (0x4)
T82D4 002:408.704 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:408.725    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:408.751   Data:  00 00 00 00
T82D4 002:408.776 - 0.079ms returns 4 (0x4)
T82D4 002:428.506 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:428.565    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:428.594   Data:  00 00 00 00
T82D4 002:428.618 - 0.121ms returns 4 (0x4)
T82D4 002:428.645 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:428.667    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:428.693   Data:  00 00 00 00
T82D4 002:428.718 - 0.082ms returns 4 (0x4)
T82D4 002:428.745 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:428.765    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:428.791   Data:  00 00 00 00
T82D4 002:428.815 - 0.078ms returns 4 (0x4)
T4740 002:473.949 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T4740 002:474.012    -- Read from C cache (2 bytes @ 0x080001C4)
T4740 002:474.040   Data:  06 48
T4740 002:474.065 - 0.125ms returns 2 (0x2)
T4740 002:474.084 JLINK_HasError()
T4740 002:474.105 JLINK_SetBPEx(Addr = 0x080161D0, Type = 0xFFFFFFF2)
T4740 002:474.127 - 0.032ms returns 0x00000001
T4740 002:474.147 JLINK_HasError()
T4740 002:474.164 JLINK_HasError()
T4740 002:474.184 JLINK_Go()
T4740 002:474.624   CPU_WriteMem(4 bytes @ 0xE0002000)
T4740 002:475.049   CPU_ReadMem(4 bytes @ 0xE0001000)
T4740 002:475.392   CPU_WriteMem(4 bytes @ 0xE0002008)
T4740 002:475.435   CPU_WriteMem(4 bytes @ 0xE000200C)
T4740 002:475.468   CPU_WriteMem(4 bytes @ 0xE0002010)
T4740 002:475.494   CPU_WriteMem(4 bytes @ 0xE0002014)
T4740 002:475.521   CPU_WriteMem(4 bytes @ 0xE0002018)
T4740 002:475.546   CPU_WriteMem(4 bytes @ 0xE000201C)
T4740 002:477.010   CPU_WriteMem(4 bytes @ 0xE0001004)
T4740 002:477.824 - 3.681ms
T4740 002:578.331 JLINK_HasError()
T4740 002:578.397 JLINK_IsHalted()
T4740 002:578.831 - 0.456ms returns FALSE
T4740 002:679.173 JLINK_HasError()
T4740 002:679.237 JLINK_IsHalted()
T4740 002:683.378 - 4.178ms returns TRUE
T4740 002:683.429 JLINK_HasError()
T4740 002:683.449 JLINK_Halt()
T4740 002:683.465 - 0.025ms returns 0x00
T4740 002:683.484 JLINK_IsHalted()
T4740 002:683.501 - 0.025ms returns TRUE
T4740 002:683.519 JLINK_IsHalted()
T4740 002:683.537 - 0.026ms returns TRUE
T4740 002:683.554 JLINK_IsHalted()
T4740 002:683.571 - 0.026ms returns TRUE
T4740 002:683.590 JLINK_HasError()
T4740 002:683.608 JLINK_ReadReg(R15 (PC))
T4740 002:683.629 - 0.030ms returns 0x080161D0
T4740 002:683.647 JLINK_ReadReg(XPSR)
T4740 002:683.665 - 0.027ms returns 0x61000000
T4740 002:683.687 JLINK_HasError()
T4740 002:683.705 JLINK_ClrBPEx(BPHandle = 0x00000001)
T4740 002:683.724 - 0.027ms returns 0x00
T4740 002:683.741 JLINK_HasError()
T4740 002:683.759 JLINK_HasError()
T4740 002:683.778 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T4740 002:683.802   CPU_ReadMem(4 bytes @ 0xE000ED30)
T4740 002:684.117   Data:  02 00 00 00
T4740 002:684.148 - 0.379ms returns 1 (0x1)
T4740 002:684.168 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T4740 002:684.189   CPU_ReadMem(4 bytes @ 0xE0001028)
T4740 002:684.521   Data:  00 00 00 00
T4740 002:684.565   Debug reg: DWT_FUNC[0]
T4740 002:684.591 - 0.431ms returns 1 (0x1)
T4740 002:684.630 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T4740 002:684.668   CPU_ReadMem(4 bytes @ 0xE0001038)
T4740 002:684.993   Data:  00 02 00 00
T4740 002:685.028   Debug reg: DWT_FUNC[1]
T4740 002:685.052 - 0.431ms returns 1 (0x1)
T4740 002:685.072 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T4740 002:685.093   CPU_ReadMem(4 bytes @ 0xE0001048)
T4740 002:685.414   Data:  00 00 00 00
T4740 002:685.443   Debug reg: DWT_FUNC[2]
T4740 002:685.467 - 0.403ms returns 1 (0x1)
T4740 002:685.485 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T4740 002:685.506   CPU_ReadMem(4 bytes @ 0xE0001058)
T4740 002:685.815   Data:  00 00 00 00
T4740 002:685.846   Debug reg: DWT_FUNC[3]
T4740 002:685.870 - 0.393ms returns 1 (0x1)
T4740 002:685.954 JLINK_HasError()
T4740 002:685.976 JLINK_ReadReg(R0)
T4740 002:685.994 - 0.027ms returns 0x080161D1
T4740 002:686.013 JLINK_ReadReg(R1)
T4740 002:686.032 - 0.027ms returns 0x20022A10
T4740 002:686.050 JLINK_ReadReg(R2)
T4740 002:686.068 - 0.027ms returns 0x00000000
T4740 002:686.086 JLINK_ReadReg(R3)
T4740 002:686.105 - 0.027ms returns 0x080129F9
T4740 002:686.124 JLINK_ReadReg(R4)
T4740 002:686.141 - 0.025ms returns 0x080188A0
T4740 002:686.159 JLINK_ReadReg(R5)
T4740 002:686.177 - 0.026ms returns 0x080188A0
T4740 002:686.195 JLINK_ReadReg(R6)
T4740 002:686.214 - 0.027ms returns 0x00000000
T4740 002:686.232 JLINK_ReadReg(R7)
T4740 002:686.250 - 0.027ms returns 0x00000000
T4740 002:686.269 JLINK_ReadReg(R8)
T4740 002:686.286 - 0.025ms returns 0x00000000
T4740 002:686.304 JLINK_ReadReg(R9)
T4740 002:686.322 - 0.026ms returns 0x20000348
T4740 002:686.340 JLINK_ReadReg(R10)
T4740 002:686.359 - 0.027ms returns 0x00000000
T4740 002:686.377 JLINK_ReadReg(R11)
T4740 002:686.394 - 0.027ms returns 0x00000000
T4740 002:686.413 JLINK_ReadReg(R12)
T4740 002:686.430 - 0.025ms returns 0x000001E0
T4740 002:686.449 JLINK_ReadReg(R13 (SP))
T4740 002:686.467 - 0.026ms returns 0x20022A10
T4740 002:686.485 JLINK_ReadReg(R14)
T4740 002:686.503 - 0.027ms returns 0x08004E6D
T4740 002:686.521 JLINK_ReadReg(R15 (PC))
T4740 002:686.539 - 0.027ms returns 0x080161D0
T4740 002:686.558 JLINK_ReadReg(XPSR)
T4740 002:686.575 - 0.026ms returns 0x61000000
T4740 002:686.595 JLINK_ReadReg(MSP)
T4740 002:686.613 - 0.026ms returns 0x20022A10
T4740 002:686.630 JLINK_ReadReg(PSP)
T4740 002:686.652 - 0.032ms returns 0x20001000
T4740 002:686.672 JLINK_ReadReg(CFBP)
T4740 002:686.691 - 0.027ms returns 0x00000000
T4740 002:686.709 JLINK_ReadReg(FPSCR)
T4740 002:692.229 - 5.532ms returns 0x00000000
T4740 002:692.252 JLINK_ReadReg(FPS0)
T4740 002:692.271 - 0.027ms returns 0x00000000
T4740 002:692.289 JLINK_ReadReg(FPS1)
T4740 002:692.308 - 0.026ms returns 0x3FF00000
T4740 002:692.325 JLINK_ReadReg(FPS2)
T4740 002:692.343 - 0.027ms returns 0x40174000
T4740 002:692.362 JLINK_ReadReg(FPS3)
T4740 002:692.379 - 0.025ms returns 0x6A610350
T4740 002:692.398 JLINK_ReadReg(FPS4)
T4740 002:692.416 - 0.026ms returns 0x004D402C
T4740 002:692.434 JLINK_ReadReg(FPS5)
T4740 002:692.452 - 0.026ms returns 0x77490280
T4740 002:692.470 JLINK_ReadReg(FPS6)
T4740 002:692.488 - 0.027ms returns 0xD8863040
T4740 002:692.506 JLINK_ReadReg(FPS7)
T4740 002:692.524 - 0.025ms returns 0x6F4222A1
T4740 002:692.542 JLINK_ReadReg(FPS8)
T4740 002:692.560 - 0.026ms returns 0x1A100861
T4740 002:692.578 JLINK_ReadReg(FPS9)
T4740 002:692.596 - 0.026ms returns 0x4C607626
T4740 002:692.614 JLINK_ReadReg(FPS10)
T4740 002:692.632 - 0.027ms returns 0x790C9808
T4740 002:692.651 JLINK_ReadReg(FPS11)
T4740 002:692.668 - 0.026ms returns 0x432099A0
T4740 002:692.687 JLINK_ReadReg(FPS12)
T4740 002:692.704 - 0.026ms returns 0x8A2080C8
T4740 002:692.722 JLINK_ReadReg(FPS13)
T4740 002:692.741 - 0.026ms returns 0x1B944171
T4740 002:692.758 JLINK_ReadReg(FPS14)
T4740 002:692.776 - 0.027ms returns 0x4502A8C8
T4740 002:692.795 JLINK_ReadReg(FPS15)
T4740 002:692.812 - 0.025ms returns 0x192E0105
T4740 002:692.831 JLINK_ReadReg(FPS16)
T4740 002:692.849 - 0.026ms returns 0x80A80080
T4740 002:692.866 JLINK_ReadReg(FPS17)
T4740 002:692.885 - 0.026ms returns 0x01012210
T4740 002:692.902 JLINK_ReadReg(FPS18)
T4740 002:692.920 - 0.027ms returns 0x6FC0024F
T4740 002:692.939 JLINK_ReadReg(FPS19)
T4740 002:692.956 - 0.025ms returns 0x0A109945
T4740 002:692.975 JLINK_ReadReg(FPS20)
T4740 002:692.993 - 0.026ms returns 0x02052300
T4740 002:693.011 JLINK_ReadReg(FPS21)
T4740 002:693.029 - 0.026ms returns 0x2806C828
T4740 002:693.047 JLINK_ReadReg(FPS22)
T4740 002:693.065 - 0.027ms returns 0x10036492
T4740 002:693.084 JLINK_ReadReg(FPS23)
T4740 002:693.101 - 0.025ms returns 0x89B0B474
T4740 002:693.120 JLINK_ReadReg(FPS24)
T4740 002:693.139 - 0.028ms returns 0x8624820A
T4740 002:693.157 JLINK_ReadReg(FPS25)
T4740 002:693.222 - 0.073ms returns 0xC00F1403
T4740 002:693.239 JLINK_ReadReg(FPS26)
T4740 002:693.258 - 0.027ms returns 0x202A04C0
T4740 002:693.277 JLINK_ReadReg(FPS27)
T4740 002:693.294 - 0.026ms returns 0x1D2712AA
T4740 002:693.313 JLINK_ReadReg(FPS28)
T4740 002:693.331 - 0.026ms returns 0x544840BB
T4740 002:693.349 JLINK_ReadReg(FPS29)
T4740 002:693.367 - 0.027ms returns 0x01482220
T4740 002:693.385 JLINK_ReadReg(FPS30)
T4740 002:693.403 - 0.027ms returns 0x0247A931
T4740 002:693.421 JLINK_ReadReg(FPS31)
T4740 002:693.439 - 0.026ms returns 0x608C08A3
T82D4 002:695.117 JLINK_HasError()
T82D4 002:695.151 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:695.174   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:695.554   Data:  DC F4 13 00
T82D4 002:695.585   Debug reg: DWT_CYCCNT
T82D4 002:695.611 - 0.468ms returns 1 (0x1)
T82D4 002:698.006 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:698.057   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 002:698.995    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 002:699.125    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 002:699.152   Data:  00 00
T82D4 002:699.176 - 1.179ms returns 2 (0x2)
T82D4 002:699.203 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:699.225    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:699.258   Data:  00 00 00 00
T82D4 002:699.283 - 0.089ms returns 4 (0x4)
T82D4 002:699.754 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:699.784    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 002:699.809   Data:  00 00 00 00
T82D4 002:699.834 - 0.091ms returns 4 (0x4)
T82D4 002:699.891 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:699.928    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:699.955   Data:  00 00 00 00
T82D4 002:699.979 - 0.096ms returns 4 (0x4)
T82D4 002:700.002 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:700.023    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:700.048   Data:  00 00 00 00
T82D4 002:700.073 - 0.080ms returns 4 (0x4)
T82D4 002:700.097 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:700.117    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:700.143   Data:  00 00 00 00
T82D4 002:700.167 - 0.078ms returns 4 (0x4)
T82D4 002:700.206 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:700.230   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:701.087    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:701.222    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:701.271   Data:  00 00 00 00
T82D4 002:701.319 - 1.122ms returns 4 (0x4)
T82D4 002:701.365 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:701.420    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:701.463   Data:  00 00 00 00
T82D4 002:701.510 - 0.153ms returns 4 (0x4)
T82D4 002:701.553 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:701.576   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:702.445    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:702.481    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:702.506   Data:  00 00 00 00
T82D4 002:702.531 - 0.986ms returns 4 (0x4)
T82D4 002:702.557 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.581   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:703.437    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:703.567    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:703.594   Data:  00 00 00 00
T82D4 002:703.618 - 1.068ms returns 4 (0x4)
T82D4 002:703.643 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:703.665    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:703.690   Data:  00 00 00 00
T82D4 002:703.716 - 0.081ms returns 4 (0x4)
T82D4 002:703.739 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:703.760   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:704.611    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:704.742    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:704.767   Data:  00
T82D4 002:704.792 - 1.062ms returns 1 (0x1)
T82D4 002:704.986 JLINK_ReadMemEx(0x20019D64, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:705.015   CPU_ReadMem(128 bytes @ 0x20019D40)
T82D4 002:706.318    -- Updating C cache (128 bytes @ 0x20019D40)
T82D4 002:706.350    -- Read from C cache (32 bytes @ 0x20019D64)
T82D4 002:706.375   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:706.399 - 1.420ms returns 32 (0x20)
T82D4 002:706.507 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:706.531    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:706.557   Data:  00 00 00 00
T82D4 002:706.582 - 0.082ms returns 4 (0x4)
T82D4 002:706.606 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:706.633    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:706.658   Data:  00 00 00 00
T82D4 002:706.684 - 0.087ms returns 4 (0x4)
T82D4 002:706.707 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:706.726    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:706.753   Data:  00 00 00 00
T82D4 002:706.777 - 0.079ms returns 4 (0x4)
T82D4 002:706.810 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:706.831    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:706.857   Data:  00
T82D4 002:706.883 - 0.082ms returns 1 (0x1)
T82D4 002:706.933 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:706.955    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:706.980   Data:  00 00 00 00
T82D4 002:707.004 - 0.080ms returns 4 (0x4)
T82D4 002:707.030 JLINK_ReadMemEx(0x08018330, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:707.053   CPU_ReadMem(64 bytes @ 0x08018300)
T82D4 002:707.915    -- Updating C cache (64 bytes @ 0x08018300)
T82D4 002:708.047    -- Read from C cache (8 bytes @ 0x08018330)
T82D4 002:708.072   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:708.098 - 1.077ms returns 8 (0x8)
T82D4 002:708.233 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:708.258   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:709.119    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:709.244    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:709.270   Data:  00 00 00 00
T82D4 002:709.294 - 1.069ms returns 4 (0x4)
T82D4 002:709.516 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:709.541    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:709.567   Data:  00 00 00 00
T82D4 002:709.591 - 0.083ms returns 4 (0x4)
T82D4 002:714.353 JLINK_ReadMemEx(0x080160D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:714.413   CPU_ReadMem(128 bytes @ 0x080160C0)
T82D4 002:715.838    -- Updating C cache (128 bytes @ 0x080160C0)
T82D4 002:715.950    -- Read from C cache (60 bytes @ 0x080160D0)
T82D4 002:715.980   Data:  DD E9 14 23 01 F0 E2 FD 04 A8 0F C8 CD E9 0A 23 ...
T82D4 002:716.006 - 1.662ms returns 60 (0x3C)
T82D4 002:716.027 JLINK_ReadMemEx(0x080160D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.049    -- Read from C cache (2 bytes @ 0x080160D0)
T82D4 002:716.076   Data:  DD E9
T82D4 002:716.104 - 0.085ms returns 2 (0x2)
T82D4 002:716.123 JLINK_ReadMemEx(0x080160D2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.142    -- Read from C cache (2 bytes @ 0x080160D2)
T82D4 002:716.171   Data:  14 23
T82D4 002:716.196 - 0.081ms returns 2 (0x2)
T82D4 002:716.216 JLINK_ReadMemEx(0x080160D4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:716.237    -- Read from C cache (60 bytes @ 0x080160D4)
T82D4 002:716.264   Data:  01 F0 E2 FD 04 A8 0F C8 CD E9 0A 23 CD E9 08 01 ...
T82D4 002:716.292 - 0.084ms returns 60 (0x3C)
T82D4 002:716.311 JLINK_ReadMemEx(0x080160D4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.329    -- Read from C cache (2 bytes @ 0x080160D4)
T82D4 002:716.357   Data:  01 F0
T82D4 002:716.384 - 0.081ms returns 2 (0x2)
T82D4 002:716.402 JLINK_ReadMemEx(0x080160D6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.422    -- Read from C cache (2 bytes @ 0x080160D6)
T82D4 002:716.449   Data:  E2 FD
T82D4 002:716.477 - 0.083ms returns 2 (0x2)
T82D4 002:716.496 JLINK_ReadMemEx(0x080160D8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:716.515    -- Read from C cache (60 bytes @ 0x080160D8)
T82D4 002:716.543   Data:  04 A8 0F C8 CD E9 0A 23 CD E9 08 01 C4 EB C4 00 ...
T82D4 002:716.569 - 0.081ms returns 60 (0x3C)
T82D4 002:716.587 JLINK_ReadMemEx(0x080160D8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.607    -- Read from C cache (2 bytes @ 0x080160D8)
T82D4 002:716.634   Data:  04 A8
T82D4 002:716.662 - 0.083ms returns 2 (0x2)
T82D4 002:716.681 JLINK_ReadMemEx(0x080160DA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.700    -- Read from C cache (2 bytes @ 0x080160DA)
T82D4 002:716.728   Data:  0F C8
T82D4 002:716.754 - 0.082ms returns 2 (0x2)
T82D4 002:716.776 JLINK_ReadMemEx(0x080160DA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.796    -- Read from C cache (2 bytes @ 0x080160DA)
T82D4 002:716.822   Data:  0F C8
T82D4 002:716.850 - 0.082ms returns 2 (0x2)
T82D4 002:716.868 JLINK_ReadMemEx(0x080160DC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:716.888    -- Read from C cache (60 bytes @ 0x080160DC)
T82D4 002:716.916   Data:  CD E9 0A 23 CD E9 08 01 C4 EB C4 00 16 4A 02 EB ...
T82D4 002:716.942 - 0.082ms returns 60 (0x3C)
T82D4 002:716.961 JLINK_ReadMemEx(0x080160DC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.981    -- Read from C cache (2 bytes @ 0x080160DC)
T82D4 002:717.008   Data:  CD E9
T82D4 002:717.036 - 0.083ms returns 2 (0x2)
T82D4 002:717.055 JLINK_ReadMemEx(0x080160DC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.074    -- Read from C cache (60 bytes @ 0x080160DC)
T82D4 002:717.106   Data:  CD E9 0A 23 CD E9 08 01 C4 EB C4 00 16 4A 02 EB ...
T82D4 002:717.134 - 0.088ms returns 60 (0x3C)
T82D4 002:717.154 JLINK_ReadMemEx(0x080160DC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.174    -- Read from C cache (2 bytes @ 0x080160DC)
T82D4 002:717.201   Data:  CD E9
T82D4 002:717.228 - 0.083ms returns 2 (0x2)
T82D4 002:717.247 JLINK_ReadMemEx(0x080160DE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.266    -- Read from C cache (2 bytes @ 0x080160DE)
T82D4 002:717.294   Data:  0A 23
T82D4 002:717.320 - 0.082ms returns 2 (0x2)
T82D4 002:717.340 JLINK_ReadMemEx(0x080160E0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.360    -- Read from C cache (60 bytes @ 0x080160E0)
T82D4 002:717.387   Data:  CD E9 08 01 C4 EB C4 00 16 4A 02 EB C0 01 04 A8 ...
T82D4 002:717.415 - 0.083ms returns 60 (0x3C)
T82D4 002:717.433 JLINK_ReadMemEx(0x080160E0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.453    -- Read from C cache (2 bytes @ 0x080160E0)
T82D4 002:717.480   Data:  CD E9
T82D4 002:717.506 - 0.081ms returns 2 (0x2)
T82D4 002:717.525 JLINK_ReadMemEx(0x080160E2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.544    -- Read from C cache (2 bytes @ 0x080160E2)
T82D4 002:717.570   Data:  08 01
T82D4 002:717.598 - 0.082ms returns 2 (0x2)
T82D4 002:717.618 JLINK_ReadMemEx(0x080160E4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.637    -- Read from C cache (60 bytes @ 0x080160E4)
T82D4 002:717.665   Data:  C4 EB C4 00 16 4A 02 EB C0 01 04 A8 FD F7 9E FB ...
T82D4 002:717.691 - 0.081ms returns 60 (0x3C)
T82D4 002:717.710 JLINK_ReadMemEx(0x080160E4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.730    -- Read from C cache (2 bytes @ 0x080160E4)
T82D4 002:717.756   Data:  C4 EB
T82D4 002:717.785 - 0.084ms returns 2 (0x2)
T82D4 002:717.804 JLINK_ReadMemEx(0x080160E6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.824    -- Read from C cache (2 bytes @ 0x080160E6)
T82D4 002:717.851   Data:  C4 00
T82D4 002:717.878 - 0.083ms returns 2 (0x2)
T82D4 002:717.899 JLINK_ReadMemEx(0x080160E8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.918    -- Read from C cache (60 bytes @ 0x080160E8)
T82D4 002:717.946   Data:  16 4A 02 EB C0 01 04 A8 FD F7 9E FB 04 A8 0F C8 ...
T82D4 002:717.973 - 0.082ms returns 60 (0x3C)
T82D4 002:717.991 JLINK_ReadMemEx(0x080160E8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.011    -- Read from C cache (2 bytes @ 0x080160E8)
T82D4 002:718.039   Data:  16 4A
T82D4 002:718.065 - 0.083ms returns 2 (0x2)
T82D4 002:718.085 JLINK_ReadMemEx(0x080160EA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.104    -- Read from C cache (2 bytes @ 0x080160EA)
T82D4 002:718.132   Data:  02 EB
T82D4 002:718.159 - 0.082ms returns 2 (0x2)
T82D4 002:718.178 JLINK_ReadMemEx(0x080160EA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.198    -- Read from C cache (2 bytes @ 0x080160EA)
T82D4 002:718.225   Data:  02 EB
T82D4 002:718.251 - 0.083ms returns 2 (0x2)
T82D4 002:718.272 JLINK_ReadMemEx(0x080160EC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.292    -- Read from C cache (60 bytes @ 0x080160EC)
T82D4 002:718.319   Data:  C0 01 04 A8 FD F7 9E FB 04 A8 0F C8 8D E8 0F 00 ...
T82D4 002:718.346 - 0.083ms returns 60 (0x3C)
T82D4 002:718.366 JLINK_ReadMemEx(0x080160EC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.387    -- Read from C cache (2 bytes @ 0x080160EC)
T82D4 002:718.414   Data:  C0 01
T82D4 002:718.440 - 0.083ms returns 2 (0x2)
T82D4 002:718.461 JLINK_ReadMemEx(0x080160EE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.480    -- Read from C cache (2 bytes @ 0x080160EE)
T82D4 002:718.507   Data:  04 A8
T82D4 002:718.534 - 0.082ms returns 2 (0x2)
T82D4 002:718.552 JLINK_ReadMemEx(0x080160F0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.573    -- Read from C cache (60 bytes @ 0x080160F0)
T82D4 002:718.601   Data:  FD F7 9E FB 04 A8 0F C8 8D E8 0F 00 08 A8 0F C8 ...
T82D4 002:718.628 - 0.086ms returns 60 (0x3C)
T82D4 002:718.649 JLINK_ReadMemEx(0x080160F0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.669    -- Read from C cache (2 bytes @ 0x080160F0)
T82D4 002:718.698   Data:  FD F7
T82D4 002:718.730 - 0.089ms returns 2 (0x2)
T82D4 002:718.749 JLINK_ReadMemEx(0x080160F0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.771    -- Read from C cache (60 bytes @ 0x080160F0)
T82D4 002:718.799   Data:  FD F7 9E FB 04 A8 0F C8 8D E8 0F 00 08 A8 0F C8 ...
T82D4 002:718.826 - 0.086ms returns 60 (0x3C)
T82D4 002:718.846 JLINK_ReadMemEx(0x080160F0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.864    -- Read from C cache (2 bytes @ 0x080160F0)
T82D4 002:718.892   Data:  FD F7
T82D4 002:718.919 - 0.082ms returns 2 (0x2)
T82D4 002:718.939 JLINK_ReadMemEx(0x080160F2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.959    -- Read from C cache (2 bytes @ 0x080160F2)
T82D4 002:718.986   Data:  9E FB
T82D4 002:719.013 - 0.084ms returns 2 (0x2)
T82D4 002:719.034 JLINK_ReadMemEx(0x080160F4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.053    -- Read from C cache (60 bytes @ 0x080160F4)
T82D4 002:719.081   Data:  04 A8 0F C8 8D E8 0F 00 08 A8 0F C8 01 F0 0F FE ...
T82D4 002:719.107 - 0.082ms returns 60 (0x3C)
T82D4 002:719.126 JLINK_ReadMemEx(0x080160F4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.146    -- Read from C cache (2 bytes @ 0x080160F4)
T82D4 002:719.173   Data:  04 A8
T82D4 002:719.200 - 0.084ms returns 2 (0x2)
T82D4 002:719.220 JLINK_ReadMemEx(0x080160F6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.239    -- Read from C cache (2 bytes @ 0x080160F6)
T82D4 002:719.268   Data:  0F C8
T82D4 002:719.294 - 0.082ms returns 2 (0x2)
T82D4 002:719.314 JLINK_ReadMemEx(0x080160F6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.334    -- Read from C cache (2 bytes @ 0x080160F6)
T82D4 002:719.361   Data:  0F C8
T82D4 002:719.388 - 0.084ms returns 2 (0x2)
T82D4 002:719.408 JLINK_ReadMemEx(0x080160F8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.427    -- Read from C cache (60 bytes @ 0x080160F8)
T82D4 002:719.455   Data:  8D E8 0F 00 08 A8 0F C8 01 F0 0F FE B0 EE 40 8A ...
T82D4 002:719.481 - 0.081ms returns 60 (0x3C)
T82D4 002:719.500 JLINK_ReadMemEx(0x080160F8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.520    -- Read from C cache (2 bytes @ 0x080160F8)
T82D4 002:719.548   Data:  8D E8
T82D4 002:719.576 - 0.085ms returns 2 (0x2)
T82D4 002:719.596 JLINK_ReadMemEx(0x080160F8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.615    -- Read from C cache (60 bytes @ 0x080160F8)
T82D4 002:719.643   Data:  8D E8 0F 00 08 A8 0F C8 01 F0 0F FE B0 EE 40 8A ...
T82D4 002:719.669 - 0.082ms returns 60 (0x3C)
T82D4 002:719.688 JLINK_ReadMemEx(0x080160F8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.708    -- Read from C cache (2 bytes @ 0x080160F8)
T82D4 002:719.734   Data:  8D E8
T82D4 002:719.762 - 0.083ms returns 2 (0x2)
T82D4 002:719.782 JLINK_ReadMemEx(0x080160FA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.801    -- Read from C cache (2 bytes @ 0x080160FA)
T82D4 002:719.830   Data:  0F 00
T82D4 002:719.856 - 0.083ms returns 2 (0x2)
T82D4 002:719.875 JLINK_ReadMemEx(0x080160FC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.896    -- Read from C cache (60 bytes @ 0x080160FC)
T82D4 002:719.929   Data:  08 A8 0F C8 01 F0 0F FE B0 EE 40 8A F0 EE 60 8A ...
T82D4 002:719.957 - 0.090ms returns 60 (0x3C)
T82D4 002:719.976 JLINK_ReadMemEx(0x080160FC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.994    -- Read from C cache (2 bytes @ 0x080160FC)
T82D4 002:720.022   Data:  08 A8
T82D4 002:720.048 - 0.081ms returns 2 (0x2)
T82D4 002:720.066 JLINK_ReadMemEx(0x080160FE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.086    -- Read from C cache (2 bytes @ 0x080160FE)
T82D4 002:720.112   Data:  0F C8
T82D4 002:720.140 - 0.082ms returns 2 (0x2)
T82D4 002:720.159 JLINK_ReadMemEx(0x080160FE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.178    -- Read from C cache (2 bytes @ 0x080160FE)
T82D4 002:720.206   Data:  0F C8
T82D4 002:720.232 - 0.081ms returns 2 (0x2)
T82D4 002:720.250 JLINK_ReadMemEx(0x08016100, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.270    -- Read from C cache (60 bytes @ 0x08016100)
T82D4 002:720.297   Data:  01 F0 0F FE B0 EE 40 8A F0 EE 60 8A 9F ED 0E 0B ...
T82D4 002:720.392 - 0.152ms returns 60 (0x3C)
T82D4 002:720.416 JLINK_ReadMemEx(0x08016100, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.440    -- Read from C cache (2 bytes @ 0x08016100)
T82D4 002:720.477   Data:  01 F0
T82D4 002:720.509 - 0.103ms returns 2 (0x2)
T82D4 002:720.534 JLINK_ReadMemEx(0x08016100, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.561    -- Read from C cache (60 bytes @ 0x08016100)
T82D4 002:720.591   Data:  01 F0 0F FE B0 EE 40 8A F0 EE 60 8A 9F ED 0E 0B ...
T82D4 002:720.619 - 0.094ms returns 60 (0x3C)
T82D4 002:720.638 JLINK_ReadMemEx(0x08016100, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.657    -- Read from C cache (2 bytes @ 0x08016100)
T82D4 002:720.687   Data:  01 F0
T82D4 002:720.737 - 0.109ms returns 2 (0x2)
T82D4 002:720.760 JLINK_ReadMemEx(0x08016102, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.779    -- Read from C cache (2 bytes @ 0x08016102)
T82D4 002:720.808   Data:  0F FE
T82D4 002:720.834 - 0.083ms returns 2 (0x2)
T82D4 002:720.853 JLINK_ReadMemEx(0x08016104, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.874    -- Read from C cache (60 bytes @ 0x08016104)
T82D4 002:720.901   Data:  B0 EE 40 8A F0 EE 60 8A 9F ED 0E 0B 53 EC 10 2B ...
T82D4 002:720.929 - 0.085ms returns 60 (0x3C)
T82D4 002:720.949 JLINK_ReadMemEx(0x08016104, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.967    -- Read from C cache (2 bytes @ 0x08016104)
T82D4 002:720.995   Data:  B0 EE
T82D4 002:721.022 - 0.081ms returns 2 (0x2)
T82D4 002:721.040 JLINK_ReadMemEx(0x08016106, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.060    -- Read from C cache (2 bytes @ 0x08016106)
T82D4 002:721.086   Data:  40 8A
T82D4 002:721.113 - 0.082ms returns 2 (0x2)
T82D4 002:721.133 JLINK_ReadMemEx(0x08016108, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:721.155   CPU_ReadMem(64 bytes @ 0x08016140)
T82D4 002:721.995    -- Updating C cache (64 bytes @ 0x08016140)
T82D4 002:722.113    -- Read from C cache (60 bytes @ 0x08016108)
T82D4 002:722.140   Data:  F0 EE 60 8A 9F ED 0E 0B 53 EC 10 2B 51 EC 18 0B ...
T82D4 002:722.166 - 1.043ms returns 60 (0x3C)
T82D4 002:722.187 JLINK_ReadMemEx(0x08016108, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.206    -- Read from C cache (2 bytes @ 0x08016108)
T82D4 002:722.234   Data:  F0 EE
T82D4 002:722.260 - 0.081ms returns 2 (0x2)
T82D4 002:722.279 JLINK_ReadMemEx(0x0801610A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.299    -- Read from C cache (2 bytes @ 0x0801610A)
T82D4 002:722.326   Data:  60 8A
T82D4 002:722.353 - 0.084ms returns 2 (0x2)
T82D4 002:722.373 JLINK_ReadMemEx(0x0801610C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.392    -- Read from C cache (60 bytes @ 0x0801610C)
T82D4 002:722.420   Data:  9F ED 0E 0B 53 EC 10 2B 51 EC 18 0B EE F7 60 FC ...
T82D4 002:722.446 - 0.081ms returns 60 (0x3C)
T82D4 002:722.464 JLINK_ReadMemEx(0x0801610C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.484    -- Read from C cache (2 bytes @ 0x0801610C)
T82D4 002:722.510   Data:  9F ED
T82D4 002:722.538 - 0.083ms returns 2 (0x2)
T82D4 002:722.558 JLINK_ReadMemEx(0x0801610E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.576    -- Read from C cache (2 bytes @ 0x0801610E)
T82D4 002:722.604   Data:  0E 0B
T82D4 002:722.630 - 0.081ms returns 2 (0x2)
T82D4 002:722.649 JLINK_ReadMemEx(0x08016110, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.670    -- Read from C cache (60 bytes @ 0x08016110)
T82D4 002:722.697   Data:  53 EC 10 2B 51 EC 18 0B EE F7 60 FC 09 D8 08 A8 ...
T82D4 002:722.725 - 0.085ms returns 60 (0x3C)
T82D4 002:722.744 JLINK_ReadMemEx(0x08016110, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.762    -- Read from C cache (2 bytes @ 0x08016110)
T82D4 002:722.790   Data:  53 EC
T82D4 002:722.816 - 0.081ms returns 2 (0x2)
T82D4 002:722.835 JLINK_ReadMemEx(0x08016112, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.855    -- Read from C cache (2 bytes @ 0x08016112)
T82D4 002:722.882   Data:  10 2B
T82D4 002:722.909 - 0.084ms returns 2 (0x2)
T82D4 002:722.929 JLINK_ReadMemEx(0x08016114, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.950    -- Read from C cache (60 bytes @ 0x08016114)
T82D4 002:722.981   Data:  51 EC 18 0B EE F7 60 FC 09 D8 08 A8 0F C8 85 E8 ...
T82D4 002:723.007 - 0.087ms returns 60 (0x3C)
T82D4 002:723.027 JLINK_ReadMemEx(0x08016114, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.047    -- Read from C cache (2 bytes @ 0x08016114)
T82D4 002:723.073   Data:  51 EC
T82D4 002:723.101 - 0.082ms returns 2 (0x2)
T82D4 002:723.120 JLINK_ReadMemEx(0x08016116, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.139    -- Read from C cache (2 bytes @ 0x08016116)
T82D4 002:723.167   Data:  18 0B
T82D4 002:723.193 - 0.082ms returns 2 (0x2)
T82D4 002:723.213 JLINK_ReadMemEx(0x08016118, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:723.233    -- Read from C cache (60 bytes @ 0x08016118)
T82D4 002:723.260   Data:  EE F7 60 FC 09 D8 08 A8 0F C8 85 E8 0F 00 0D B0 ...
T82D4 002:723.288 - 0.083ms returns 60 (0x3C)
T82D4 002:723.306 JLINK_ReadMemEx(0x08016118, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.325    -- Read from C cache (2 bytes @ 0x08016118)
T82D4 002:723.352   Data:  EE F7
T82D4 002:723.378 - 0.080ms returns 2 (0x2)
T82D4 002:723.398 JLINK_ReadMemEx(0x0801611A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.417    -- Read from C cache (2 bytes @ 0x0801611A)
T82D4 002:723.443   Data:  60 FC
T82D4 002:723.471 - 0.082ms returns 2 (0x2)
T82D4 002:723.490 JLINK_ReadMemEx(0x0801611C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:723.510    -- Read from C cache (60 bytes @ 0x0801611C)
T82D4 002:723.538   Data:  09 D8 08 A8 0F C8 85 E8 0F 00 0D B0 BD EC 02 8B ...
T82D4 002:723.564 - 0.082ms returns 60 (0x3C)
T82D4 002:723.583 JLINK_ReadMemEx(0x0801611C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.603    -- Read from C cache (2 bytes @ 0x0801611C)
T82D4 002:723.629   Data:  09 D8
T82D4 002:723.657 - 0.082ms returns 2 (0x2)
T82D4 002:723.676 JLINK_ReadMemEx(0x0801611E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.695    -- Read from C cache (2 bytes @ 0x0801611E)
T82D4 002:723.723   Data:  08 A8
T82D4 002:723.748 - 0.081ms returns 2 (0x2)
T82D4 002:723.768 JLINK_ReadMemEx(0x0801611E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.788    -- Read from C cache (2 bytes @ 0x0801611E)
T82D4 002:723.814   Data:  08 A8
T82D4 002:723.843 - 0.083ms returns 2 (0x2)
T82D4 002:723.862 JLINK_ReadMemEx(0x08016120, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:723.881    -- Read from C cache (60 bytes @ 0x08016120)
T82D4 002:723.909   Data:  0F C8 85 E8 0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 ...
T82D4 002:723.936 - 0.083ms returns 60 (0x3C)
T82D4 002:723.956 JLINK_ReadMemEx(0x08016120, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.975    -- Read from C cache (2 bytes @ 0x08016120)
T82D4 002:724.002   Data:  0F C8
T82D4 002:724.029 - 0.081ms returns 2 (0x2)
T82D4 002:724.048 JLINK_ReadMemEx(0x08016120, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.068    -- Read from C cache (60 bytes @ 0x08016120)
T82D4 002:724.096   Data:  0F C8 85 E8 0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 ...
T82D4 002:724.122 - 0.083ms returns 60 (0x3C)
T82D4 002:724.142 JLINK_ReadMemEx(0x08016120, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.161    -- Read from C cache (2 bytes @ 0x08016120)
T82D4 002:724.189   Data:  0F C8
T82D4 002:724.216 - 0.083ms returns 2 (0x2)
T82D4 002:724.235 JLINK_ReadMemEx(0x08016122, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.255    -- Read from C cache (2 bytes @ 0x08016122)
T82D4 002:724.281   Data:  85 E8
T82D4 002:724.307 - 0.082ms returns 2 (0x2)
T82D4 002:724.328 JLINK_ReadMemEx(0x08016122, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.346    -- Read from C cache (2 bytes @ 0x08016122)
T82D4 002:724.373   Data:  85 E8
T82D4 002:724.401 - 0.082ms returns 2 (0x2)
T82D4 002:724.420 JLINK_ReadMemEx(0x08016124, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.440    -- Read from C cache (60 bytes @ 0x08016124)
T82D4 002:724.467   Data:  0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 14 FB 64 1C ...
T82D4 002:724.493 - 0.082ms returns 60 (0x3C)
T82D4 002:724.513 JLINK_ReadMemEx(0x08016124, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.531    -- Read from C cache (2 bytes @ 0x08016124)
T82D4 002:724.560   Data:  0F 00
T82D4 002:724.588 - 0.083ms returns 2 (0x2)
T82D4 002:724.607 JLINK_ReadMemEx(0x08016126, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.627    -- Read from C cache (2 bytes @ 0x08016126)
T82D4 002:724.653   Data:  0D B0
T82D4 002:724.679 - 0.081ms returns 2 (0x2)
T82D4 002:724.699 JLINK_ReadMemEx(0x08016128, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.718    -- Read from C cache (60 bytes @ 0x08016128)
T82D4 002:724.745   Data:  BD EC 02 8B 30 BC 5D F8 14 FB 64 1C 12 2C BB DB ...
T82D4 002:724.773 - 0.082ms returns 60 (0x3C)
T82D4 002:724.791 JLINK_ReadMemEx(0x08016128, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.812    -- Read from C cache (2 bytes @ 0x08016128)
T82D4 002:724.838   Data:  BD EC
T82D4 002:724.864 - 0.082ms returns 2 (0x2)
T82D4 002:724.885 JLINK_ReadMemEx(0x08016128, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.904    -- Read from C cache (60 bytes @ 0x08016128)
T82D4 002:724.933   Data:  BD EC 02 8B 30 BC 5D F8 14 FB 64 1C 12 2C BB DB ...
T82D4 002:724.975 - 0.098ms returns 60 (0x3C)
T82D4 002:724.993 JLINK_ReadMemEx(0x08016128, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.014    -- Read from C cache (2 bytes @ 0x08016128)
T82D4 002:725.040   Data:  BD EC
T82D4 002:725.068 - 0.083ms returns 2 (0x2)
T82D4 002:725.087 JLINK_ReadMemEx(0x0801612A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.105    -- Read from C cache (2 bytes @ 0x0801612A)
T82D4 002:725.134   Data:  02 8B
T82D4 002:725.160 - 0.081ms returns 2 (0x2)
T82D4 002:725.179 JLINK_ReadMemEx(0x0801612C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:725.199    -- Read from C cache (60 bytes @ 0x0801612C)
T82D4 002:725.226   Data:  30 BC 5D F8 14 FB 64 1C 12 2C BB DB 14 A8 0F C8 ...
T82D4 002:725.253 - 0.083ms returns 60 (0x3C)
T82D4 002:725.272 JLINK_ReadMemEx(0x0801612C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.290    -- Read from C cache (2 bytes @ 0x0801612C)
T82D4 002:725.319   Data:  30 BC
T82D4 002:725.345 - 0.081ms returns 2 (0x2)
T82D4 002:725.364 JLINK_ReadMemEx(0x0801612E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.385    -- Read from C cache (2 bytes @ 0x0801612E)
T82D4 002:725.412   Data:  5D F8
T82D4 002:725.439 - 0.084ms returns 2 (0x2)
T82D4 002:725.459 JLINK_ReadMemEx(0x0801612E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.477    -- Read from C cache (2 bytes @ 0x0801612E)
T82D4 002:725.506   Data:  5D F8
T82D4 002:725.532 - 0.081ms returns 2 (0x2)
T82D4 002:725.551 JLINK_ReadMemEx(0x08016130, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:725.571    -- Read from C cache (60 bytes @ 0x08016130)
T82D4 002:725.598   Data:  14 FB 64 1C 12 2C BB DB 14 A8 0F C8 85 E8 0F 00 ...
T82D4 002:725.626 - 0.084ms returns 60 (0x3C)
T82D4 002:725.644 JLINK_ReadMemEx(0x08016130, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.663    -- Read from C cache (2 bytes @ 0x08016130)
T82D4 002:725.691   Data:  14 FB
T82D4 002:725.722 - 0.086ms returns 2 (0x2)
T82D4 002:725.741 JLINK_ReadMemEx(0x08016132, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.761    -- Read from C cache (2 bytes @ 0x08016132)
T82D4 002:725.788   Data:  64 1C
T82D4 002:725.816 - 0.084ms returns 2 (0x2)
T82D4 002:725.835 JLINK_ReadMemEx(0x08016134, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:725.854    -- Read from C cache (60 bytes @ 0x08016134)
T82D4 002:725.882   Data:  12 2C BB DB 14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 ...
T82D4 002:725.908 - 0.081ms returns 60 (0x3C)
T82D4 002:725.926 JLINK_ReadMemEx(0x08016134, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.947    -- Read from C cache (2 bytes @ 0x08016134)
T82D4 002:725.973   Data:  12 2C
T82D4 002:726.002 - 0.084ms returns 2 (0x2)
T82D4 002:726.021 JLINK_ReadMemEx(0x08016134, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.041    -- Read from C cache (60 bytes @ 0x08016134)
T82D4 002:726.068   Data:  12 2C BB DB 14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 ...
T82D4 002:726.094 - 0.082ms returns 60 (0x3C)
T82D4 002:726.114 JLINK_ReadMemEx(0x08016134, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.133    -- Read from C cache (2 bytes @ 0x08016134)
T82D4 002:726.162   Data:  12 2C
T82D4 002:726.189 - 0.084ms returns 2 (0x2)
T82D4 002:726.208 JLINK_ReadMemEx(0x08016136, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.228    -- Read from C cache (2 bytes @ 0x08016136)
T82D4 002:726.256   Data:  BB DB
T82D4 002:726.282 - 0.082ms returns 2 (0x2)
T82D4 002:726.301 JLINK_ReadMemEx(0x08016136, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.321    -- Read from C cache (2 bytes @ 0x08016136)
T82D4 002:726.348   Data:  BB DB
T82D4 002:726.376 - 0.083ms returns 2 (0x2)
T82D4 002:726.395 JLINK_ReadMemEx(0x08016138, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.415    -- Read from C cache (60 bytes @ 0x08016138)
T82D4 002:726.443   Data:  14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 B0 00 00 20 ...
T82D4 002:726.469 - 0.082ms returns 60 (0x3C)
T82D4 002:726.488 JLINK_ReadMemEx(0x08016138, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.508    -- Read from C cache (2 bytes @ 0x08016138)
T82D4 002:726.534   Data:  14 A8
T82D4 002:726.562 - 0.083ms returns 2 (0x2)
T82D4 002:726.582 JLINK_ReadMemEx(0x08016138, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.602    -- Read from C cache (60 bytes @ 0x08016138)
T82D4 002:726.630   Data:  14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 B0 00 00 20 ...
T82D4 002:726.656 - 0.082ms returns 60 (0x3C)
T82D4 002:726.675 JLINK_ReadMemEx(0x08016138, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.695    -- Read from C cache (2 bytes @ 0x08016138)
T82D4 002:726.722   Data:  14 A8
T82D4 002:726.750 - 0.083ms returns 2 (0x2)
T82D4 002:726.769 JLINK_ReadMemEx(0x0801613A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.788    -- Read from C cache (2 bytes @ 0x0801613A)
T82D4 002:726.829   Data:  0F C8
T82D4 002:726.854 - 0.093ms returns 2 (0x2)
T82D4 002:726.873 JLINK_ReadMemEx(0x0801613A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.892    -- Read from C cache (2 bytes @ 0x0801613A)
T82D4 002:726.917   Data:  0F C8
T82D4 002:726.945 - 0.080ms returns 2 (0x2)
T82D4 002:726.963 JLINK_ReadMemEx(0x0801613C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.983    -- Read from C cache (60 bytes @ 0x0801613C)
T82D4 002:727.030   Data:  85 E8 0F 00 00 BF F0 E7 B0 00 00 20 00 00 00 00 ...
T82D4 002:727.056 - 0.102ms returns 60 (0x3C)
T82D4 002:727.076 JLINK_ReadMemEx(0x0801613C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.096    -- Read from C cache (2 bytes @ 0x0801613C)
T82D4 002:727.166   Data:  85 E8
T82D4 002:727.194 - 0.142ms returns 2 (0x2)
T82D4 002:727.228 JLINK_ReadMemEx(0x0801613C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.248    -- Read from C cache (60 bytes @ 0x0801613C)
T82D4 002:727.274   Data:  85 E8 0F 00 00 BF F0 E7 B0 00 00 20 00 00 00 00 ...
T82D4 002:727.299 - 0.079ms returns 60 (0x3C)
T82D4 002:727.318 JLINK_ReadMemEx(0x0801613C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.336    -- Read from C cache (2 bytes @ 0x0801613C)
T82D4 002:727.362   Data:  85 E8
T82D4 002:727.388 - 0.078ms returns 2 (0x2)
T82D4 002:727.406 JLINK_ReadMemEx(0x0801613E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.426    -- Read from C cache (2 bytes @ 0x0801613E)
T82D4 002:727.451   Data:  0F 00
T82D4 002:727.475 - 0.078ms returns 2 (0x2)
T82D4 002:727.495 JLINK_ReadMemEx(0x08016140, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.513    -- Read from C cache (60 bytes @ 0x08016140)
T82D4 002:727.540   Data:  00 BF F0 E7 B0 00 00 20 00 00 00 00 00 00 00 00 ...
T82D4 002:727.565 - 0.078ms returns 60 (0x3C)
T82D4 002:727.582 JLINK_ReadMemEx(0x08016140, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.602    -- Read from C cache (2 bytes @ 0x08016140)
T82D4 002:727.628   Data:  00 BF
T82D4 002:727.652 - 0.079ms returns 2 (0x2)
T82D4 002:727.672 JLINK_ReadMemEx(0x08016142, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.690    -- Read from C cache (2 bytes @ 0x08016142)
T82D4 002:727.716   Data:  F0 E7
T82D4 002:727.742 - 0.078ms returns 2 (0x2)
T82D4 002:727.760 JLINK_ReadMemEx(0x08016142, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.780    -- Read from C cache (2 bytes @ 0x08016142)
T82D4 002:727.805   Data:  F0 E7
T82D4 002:727.832 - 0.081ms returns 2 (0x2)
T82D4 002:727.852 JLINK_ReadMemEx(0x08016144, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.870    -- Read from C cache (60 bytes @ 0x08016144)
T82D4 002:727.897   Data:  B0 00 00 20 00 00 00 00 00 00 00 00 70 B5 8A 21 ...
T82D4 002:727.958 - 0.131ms returns 60 (0x3C)
T82D4 002:728.001 JLINK_ReadMemEx(0x08016144, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.030    -- Read from C cache (2 bytes @ 0x08016144)
T82D4 002:728.071   Data:  B0 00
T82D4 002:728.102 - 0.113ms returns 2 (0x2)
T82D4 002:728.131 JLINK_ReadMemEx(0x08016150, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:728.157   CPU_ReadMem(64 bytes @ 0x08016180)
T82D4 002:728.968    -- Updating C cache (64 bytes @ 0x08016180)
T82D4 002:729.021    -- Read from C cache (60 bytes @ 0x08016150)
T82D4 002:729.047   Data:  70 B5 8A 21 18 48 EE F7 60 F8 94 21 17 48 EE F7 ...
T82D4 002:729.072 - 0.949ms returns 60 (0x3C)
T82D4 002:729.092 JLINK_ReadMemEx(0x08016150, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.111    -- Read from C cache (2 bytes @ 0x08016150)
T82D4 002:729.138   Data:  70 B5
T82D4 002:729.164 - 0.080ms returns 2 (0x2)
T82D4 002:729.182 JLINK_ReadMemEx(0x08016152, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.201    -- Read from C cache (2 bytes @ 0x08016152)
T82D4 002:729.227   Data:  8A 21
T82D4 002:729.252 - 0.079ms returns 2 (0x2)
T82D4 002:729.272 JLINK_ReadMemEx(0x08016152, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.291    -- Read from C cache (2 bytes @ 0x08016152)
T82D4 002:729.317   Data:  8A 21
T82D4 002:729.343 - 0.079ms returns 2 (0x2)
T82D4 002:729.361 JLINK_ReadMemEx(0x08016154, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.380    -- Read from C cache (60 bytes @ 0x08016154)
T82D4 002:729.406   Data:  18 48 EE F7 60 F8 94 21 17 48 EE F7 5C F8 15 4C ...
T82D4 002:729.430 - 0.078ms returns 60 (0x3C)
T82D4 002:729.449 JLINK_ReadMemEx(0x08016154, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.467    -- Read from C cache (2 bytes @ 0x08016154)
T82D4 002:729.493   Data:  18 48
T82D4 002:729.519 - 0.078ms returns 2 (0x2)
T82D4 002:729.537 JLINK_ReadMemEx(0x08016154, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.557    -- Read from C cache (60 bytes @ 0x08016154)
T82D4 002:729.583   Data:  18 48 EE F7 60 F8 94 21 17 48 EE F7 5C F8 15 4C ...
T82D4 002:729.607 - 0.079ms returns 60 (0x3C)
T82D4 002:729.627 JLINK_ReadMemEx(0x08016154, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.645    -- Read from C cache (2 bytes @ 0x08016154)
T82D4 002:729.671   Data:  18 48
T82D4 002:729.697 - 0.078ms returns 2 (0x2)
T82D4 002:729.715 JLINK_ReadMemEx(0x08016156, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.734    -- Read from C cache (2 bytes @ 0x08016156)
T82D4 002:729.759   Data:  EE F7
T82D4 002:729.784 - 0.078ms returns 2 (0x2)
T82D4 002:729.804 JLINK_ReadMemEx(0x08016156, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.821    -- Read from C cache (2 bytes @ 0x08016156)
T82D4 002:729.847   Data:  EE F7
T82D4 002:729.874 - 0.079ms returns 2 (0x2)
T82D4 002:729.893 JLINK_ReadMemEx(0x08016158, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.935    -- Read from C cache (60 bytes @ 0x08016158)
T82D4 002:729.976   Data:  60 F8 94 21 17 48 EE F7 5C F8 15 4C 00 25 16 A1 ...
T82D4 002:730.002 - 0.118ms returns 60 (0x3C)
T82D4 002:730.023 JLINK_ReadMemEx(0x08016158, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.041    -- Read from C cache (2 bytes @ 0x08016158)
T82D4 002:730.068   Data:  60 F8
T82D4 002:730.093 - 0.078ms returns 2 (0x2)
T82D4 002:730.112 JLINK_ReadMemEx(0x0801615A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.131    -- Read from C cache (2 bytes @ 0x0801615A)
T82D4 002:730.157   Data:  94 21
T82D4 002:730.184 - 0.080ms returns 2 (0x2)
T82D4 002:730.202 JLINK_ReadMemEx(0x0801615C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.222    -- Read from C cache (60 bytes @ 0x0801615C)
T82D4 002:730.247   Data:  17 48 EE F7 5C F8 15 4C 00 25 16 A1 A0 1D EE F7 ...
T82D4 002:730.272 - 0.078ms returns 60 (0x3C)
T82D4 002:730.292 JLINK_ReadMemEx(0x0801615C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.311    -- Read from C cache (2 bytes @ 0x0801615C)
T82D4 002:730.339   Data:  17 48
T82D4 002:730.365 - 0.081ms returns 2 (0x2)
T82D4 002:730.384 JLINK_ReadMemEx(0x0801615C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.404    -- Read from C cache (60 bytes @ 0x0801615C)
T82D4 002:730.430   Data:  17 48 EE F7 5C F8 15 4C 00 25 16 A1 A0 1D EE F7 ...
T82D4 002:730.456 - 0.081ms returns 60 (0x3C)
T82D4 002:730.475 JLINK_ReadMemEx(0x0801615C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.494    -- Read from C cache (2 bytes @ 0x0801615C)
T82D4 002:730.520   Data:  17 48
T82D4 002:730.545 - 0.077ms returns 2 (0x2)
T82D4 002:730.564 JLINK_ReadMemEx(0x0801615E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.583    -- Read from C cache (2 bytes @ 0x0801615E)
T82D4 002:730.610   Data:  EE F7
T82D4 002:730.635 - 0.079ms returns 2 (0x2)
T82D4 002:730.654 JLINK_ReadMemEx(0x0801615E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.674    -- Read from C cache (2 bytes @ 0x0801615E)
T82D4 002:730.699   Data:  EE F7
T82D4 002:730.725 - 0.079ms returns 2 (0x2)
T82D4 002:730.744 JLINK_ReadMemEx(0x08016160, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.762    -- Read from C cache (60 bytes @ 0x08016160)
T82D4 002:730.790   Data:  5C F8 15 4C 00 25 16 A1 A0 1D EE F7 74 F8 01 21 ...
T82D4 002:730.815 - 0.079ms returns 60 (0x3C)
T82D4 002:730.833 JLINK_ReadMemEx(0x08016160, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.853    -- Read from C cache (2 bytes @ 0x08016160)
T82D4 002:730.878   Data:  5C F8
T82D4 002:730.905 - 0.081ms returns 2 (0x2)
T82D4 002:730.925 JLINK_ReadMemEx(0x08016162, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.945    -- Read from C cache (2 bytes @ 0x08016162)
T82D4 002:730.970   Data:  15 4C
T82D4 002:730.996 - 0.080ms returns 2 (0x2)
T82D4 002:731.016 JLINK_ReadMemEx(0x08016164, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.034    -- Read from C cache (60 bytes @ 0x08016164)
T82D4 002:731.062   Data:  00 25 16 A1 A0 1D EE F7 74 F8 01 21 20 46 81 75 ...
T82D4 002:731.087 - 0.079ms returns 60 (0x3C)
T82D4 002:731.106 JLINK_ReadMemEx(0x08016164, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.125    -- Read from C cache (2 bytes @ 0x08016164)
T82D4 002:731.151   Data:  00 25
T82D4 002:731.178 - 0.080ms returns 2 (0x2)
T82D4 002:731.197 JLINK_ReadMemEx(0x08016164, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.216    -- Read from C cache (60 bytes @ 0x08016164)
T82D4 002:731.242   Data:  00 25 16 A1 A0 1D EE F7 74 F8 01 21 20 46 81 75 ...
T82D4 002:731.267 - 0.079ms returns 60 (0x3C)
T82D4 002:731.287 JLINK_ReadMemEx(0x08016164, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.306    -- Read from C cache (2 bytes @ 0x08016164)
T82D4 002:731.332   Data:  00 25
T82D4 002:731.357 - 0.078ms returns 2 (0x2)
T82D4 002:731.376 JLINK_ReadMemEx(0x08016166, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.396    -- Read from C cache (2 bytes @ 0x08016166)
T82D4 002:731.425   Data:  16 A1
T82D4 002:731.451 - 0.084ms returns 2 (0x2)
T82D4 002:731.471 JLINK_ReadMemEx(0x08016166, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.490    -- Read from C cache (2 bytes @ 0x08016166)
T82D4 002:731.516   Data:  16 A1
T82D4 002:731.541 - 0.078ms returns 2 (0x2)
T82D4 002:731.560 JLINK_ReadMemEx(0x08016168, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.579    -- Read from C cache (60 bytes @ 0x08016168)
T82D4 002:731.606   Data:  A0 1D EE F7 74 F8 01 21 20 46 81 75 C1 75 00 21 ...
T82D4 002:731.632 - 0.080ms returns 60 (0x3C)
T82D4 002:731.650 JLINK_ReadMemEx(0x08016168, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.669    -- Read from C cache (2 bytes @ 0x08016168)
T82D4 002:731.695   Data:  A0 1D
T82D4 002:731.720 - 0.079ms returns 2 (0x2)
T82D4 002:731.740 JLINK_ReadMemEx(0x08016168, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.759    -- Read from C cache (60 bytes @ 0x08016168)
T82D4 002:731.786   Data:  A0 1D EE F7 74 F8 01 21 20 46 81 75 C1 75 00 21 ...
T82D4 002:731.810 - 0.078ms returns 60 (0x3C)
T82D4 002:731.829 JLINK_ReadMemEx(0x08016168, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.849    -- Read from C cache (2 bytes @ 0x08016168)
T82D4 002:731.875   Data:  A0 1D
T82D4 002:731.902 - 0.082ms returns 2 (0x2)
T82D4 002:731.922 JLINK_ReadMemEx(0x0801616A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.942    -- Read from C cache (2 bytes @ 0x0801616A)
T82D4 002:731.967   Data:  EE F7
T82D4 002:731.992 - 0.079ms returns 2 (0x2)
T82D4 002:732.012 JLINK_ReadMemEx(0x0801616A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.030    -- Read from C cache (2 bytes @ 0x0801616A)
T82D4 002:732.057   Data:  EE F7
T82D4 002:732.082 - 0.078ms returns 2 (0x2)
T82D4 002:732.101 JLINK_ReadMemEx(0x0801616C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:732.120    -- Read from C cache (60 bytes @ 0x0801616C)
T82D4 002:732.147   Data:  74 F8 01 21 20 46 81 75 C1 75 00 21 81 61 C1 61 ...
T82D4 002:732.173 - 0.080ms returns 60 (0x3C)
T82D4 002:732.191 JLINK_ReadMemEx(0x0801616C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.211    -- Read from C cache (2 bytes @ 0x0801616C)
T82D4 002:732.237   Data:  74 F8
T82D4 002:732.261 - 0.079ms returns 2 (0x2)
T82D4 002:732.282 JLINK_ReadMemEx(0x0801616E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.300    -- Read from C cache (2 bytes @ 0x0801616E)
T82D4 002:732.327   Data:  01 21
T82D4 002:732.352 - 0.078ms returns 2 (0x2)
T82D4 002:732.371 JLINK_ReadMemEx(0x08016170, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:732.390    -- Read from C cache (60 bytes @ 0x08016170)
T82D4 002:732.417   Data:  20 46 81 75 C1 75 00 21 81 61 C1 61 01 62 41 62 ...
T82D4 002:732.443 - 0.080ms returns 60 (0x3C)
T82D4 002:732.461 JLINK_ReadMemEx(0x08016170, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.480    -- Read from C cache (2 bytes @ 0x08016170)
T82D4 002:732.506   Data:  20 46
T82D4 002:732.531 - 0.078ms returns 2 (0x2)
T82D4 002:732.551 JLINK_ReadMemEx(0x08016170, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:732.570    -- Read from C cache (60 bytes @ 0x08016170)
T82D4 002:732.597   Data:  20 46 81 75 C1 75 00 21 81 61 C1 61 01 62 41 62 ...
T82D4 002:732.622 - 0.079ms returns 60 (0x3C)
T82D4 002:732.641 JLINK_ReadMemEx(0x08016170, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.660    -- Read from C cache (2 bytes @ 0x08016170)
T82D4 002:732.685   Data:  20 46
T82D4 002:732.710 - 0.078ms returns 2 (0x2)
T82D4 002:732.730 JLINK_ReadMemEx(0x08016172, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.748    -- Read from C cache (2 bytes @ 0x08016172)
T82D4 002:732.775   Data:  81 75
T82D4 002:732.800 - 0.078ms returns 2 (0x2)
T82D4 002:732.819 JLINK_ReadMemEx(0x08016172, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.838    -- Read from C cache (2 bytes @ 0x08016172)
T82D4 002:732.864   Data:  81 75
T82D4 002:732.890 - 0.079ms returns 2 (0x2)
T82D4 002:732.965 JLINK_ReadMemEx(0x08016174, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:732.989    -- Read from C cache (60 bytes @ 0x08016174)
T82D4 002:733.016   Data:  C1 75 00 21 81 61 C1 61 01 62 41 62 81 62 C1 62 ...
T82D4 002:733.041 - 0.083ms returns 60 (0x3C)
T82D4 002:733.059 JLINK_ReadMemEx(0x08016174, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.078    -- Read from C cache (2 bytes @ 0x08016174)
T82D4 002:733.103   Data:  C1 75
T82D4 002:733.127 - 0.078ms returns 2 (0x2)
T82D4 002:733.147 JLINK_ReadMemEx(0x08016174, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.165    -- Read from C cache (60 bytes @ 0x08016174)
T82D4 002:733.192   Data:  C1 75 00 21 81 61 C1 61 01 62 41 62 81 62 C1 62 ...
T82D4 002:733.218 - 0.079ms returns 60 (0x3C)
T82D4 002:733.236 JLINK_ReadMemEx(0x08016174, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.255    -- Read from C cache (2 bytes @ 0x08016174)
T82D4 002:733.280   Data:  C1 75
T82D4 002:733.305 - 0.078ms returns 2 (0x2)
T82D4 002:733.325 JLINK_ReadMemEx(0x08016176, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.343    -- Read from C cache (2 bytes @ 0x08016176)
T82D4 002:733.368   Data:  00 21
T82D4 002:733.394 - 0.077ms returns 2 (0x2)
T82D4 002:733.412 JLINK_ReadMemEx(0x08016176, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.431    -- Read from C cache (2 bytes @ 0x08016176)
T82D4 002:733.460   Data:  00 21
T82D4 002:733.484 - 0.081ms returns 2 (0x2)
T82D4 002:733.504 JLINK_ReadMemEx(0x08016178, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.522    -- Read from C cache (60 bytes @ 0x08016178)
T82D4 002:733.549   Data:  81 61 C1 61 01 62 41 62 81 62 C1 62 01 63 41 63 ...
T82D4 002:733.574 - 0.079ms returns 60 (0x3C)
T82D4 002:733.592 JLINK_ReadMemEx(0x08016178, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.611    -- Read from C cache (2 bytes @ 0x08016178)
T82D4 002:733.636   Data:  81 61
T82D4 002:733.661 - 0.078ms returns 2 (0x2)
T82D4 002:733.681 JLINK_ReadMemEx(0x08016178, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.699    -- Read from C cache (60 bytes @ 0x08016178)
T82D4 002:733.725   Data:  81 61 C1 61 01 62 41 62 81 62 C1 62 01 63 41 63 ...
T82D4 002:733.751 - 0.078ms returns 60 (0x3C)
T82D4 002:733.769 JLINK_ReadMemEx(0x08016178, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.788    -- Read from C cache (2 bytes @ 0x08016178)
T82D4 002:733.813   Data:  81 61
T82D4 002:733.838 - 0.078ms returns 2 (0x2)
T82D4 002:733.857 JLINK_ReadMemEx(0x0801617A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.876    -- Read from C cache (2 bytes @ 0x0801617A)
T82D4 002:733.902   Data:  C1 61
T82D4 002:733.928 - 0.079ms returns 2 (0x2)
T82D4 002:733.946 JLINK_ReadMemEx(0x0801617A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.965    -- Read from C cache (2 bytes @ 0x0801617A)
T82D4 002:733.990   Data:  C1 61
T82D4 002:734.016 - 0.078ms returns 2 (0x2)
T82D4 002:734.034 JLINK_ReadMemEx(0x0801617C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.052    -- Read from C cache (60 bytes @ 0x0801617C)
T82D4 002:734.079   Data:  01 62 41 62 81 62 C1 62 01 63 41 63 10 49 41 64 ...
T82D4 002:734.105 - 0.078ms returns 60 (0x3C)
T82D4 002:734.122 JLINK_ReadMemEx(0x0801617C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.141    -- Read from C cache (2 bytes @ 0x0801617C)
T82D4 002:734.167   Data:  01 62
T82D4 002:734.192 - 0.079ms returns 2 (0x2)
T82D4 002:734.211 JLINK_ReadMemEx(0x0801617C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.229    -- Read from C cache (60 bytes @ 0x0801617C)
T82D4 002:734.256   Data:  01 62 41 62 81 62 C1 62 01 63 41 63 10 49 41 64 ...
T82D4 002:734.281 - 0.078ms returns 60 (0x3C)
T82D4 002:734.298 JLINK_ReadMemEx(0x0801617C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.317    -- Read from C cache (2 bytes @ 0x0801617C)
T82D4 002:734.343   Data:  01 62
T82D4 002:734.368 - 0.078ms returns 2 (0x2)
T82D4 002:734.387 JLINK_ReadMemEx(0x0801617E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.405    -- Read from C cache (2 bytes @ 0x0801617E)
T82D4 002:734.431   Data:  41 62
T82D4 002:734.456 - 0.077ms returns 2 (0x2)
T82D4 002:734.474 JLINK_ReadMemEx(0x0801617E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.493    -- Read from C cache (2 bytes @ 0x0801617E)
T82D4 002:734.519   Data:  41 62
T82D4 002:734.545 - 0.080ms returns 2 (0x2)
T82D4 002:734.585 JLINK_ReadMemEx(0x08016180, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.605    -- Read from C cache (60 bytes @ 0x08016180)
T82D4 002:734.647   Data:  81 62 C1 62 01 63 41 63 10 49 41 64 45 F2 AA 50 ...
T82D4 002:734.671 - 0.095ms returns 60 (0x3C)
T82D4 002:734.689 JLINK_ReadMemEx(0x08016180, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.708    -- Read from C cache (2 bytes @ 0x08016180)
T82D4 002:734.756   Data:  81 62
T82D4 002:734.783 - 0.119ms returns 2 (0x2)
T82D4 002:734.818 JLINK_ReadMemEx(0x08016180, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.836    -- Read from C cache (60 bytes @ 0x08016180)
T82D4 002:734.863   Data:  81 62 C1 62 01 63 41 63 10 49 41 64 45 F2 AA 50 ...
T82D4 002:734.888 - 0.078ms returns 60 (0x3C)
T82D4 002:734.906 JLINK_ReadMemEx(0x08016180, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.926    -- Read from C cache (2 bytes @ 0x08016180)
T82D4 002:734.951   Data:  81 62
T82D4 002:734.977 - 0.079ms returns 2 (0x2)
T82D4 002:734.995 JLINK_ReadMemEx(0x08016182, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.013    -- Read from C cache (2 bytes @ 0x08016182)
T82D4 002:735.042   Data:  C1 62
T82D4 002:735.067 - 0.080ms returns 2 (0x2)
T82D4 002:735.085 JLINK_ReadMemEx(0x08016182, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.105    -- Read from C cache (2 bytes @ 0x08016182)
T82D4 002:735.130   Data:  C1 62
T82D4 002:735.156 - 0.079ms returns 2 (0x2)
T82D4 002:735.175 JLINK_ReadMemEx(0x08016184, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.192    -- Read from C cache (60 bytes @ 0x08016184)
T82D4 002:735.220   Data:  01 63 41 63 10 49 41 64 45 F2 AA 50 21 46 08 80 ...
T82D4 002:735.244 - 0.078ms returns 60 (0x3C)
T82D4 002:735.262 JLINK_ReadMemEx(0x08016184, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.281    -- Read from C cache (2 bytes @ 0x08016184)
T82D4 002:735.306   Data:  01 63
T82D4 002:735.333 - 0.079ms returns 2 (0x2)
T82D4 002:735.351 JLINK_ReadMemEx(0x08016184, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.369    -- Read from C cache (60 bytes @ 0x08016184)
T82D4 002:735.396   Data:  01 63 41 63 10 49 41 64 45 F2 AA 50 21 46 08 80 ...
T82D4 002:735.422 - 0.079ms returns 60 (0x3C)
T82D4 002:735.439 JLINK_ReadMemEx(0x08016184, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.459    -- Read from C cache (2 bytes @ 0x08016184)
T82D4 002:735.485   Data:  01 63
T82D4 002:735.511 - 0.080ms returns 2 (0x2)
T82D4 002:735.530 JLINK_ReadMemEx(0x08016186, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.548    -- Read from C cache (2 bytes @ 0x08016186)
T82D4 002:735.575   Data:  41 63
T82D4 002:735.600 - 0.078ms returns 2 (0x2)
T82D4 002:735.619 JLINK_ReadMemEx(0x08016186, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.638    -- Read from C cache (2 bytes @ 0x08016186)
T82D4 002:735.663   Data:  41 63
T82D4 002:735.690 - 0.081ms returns 2 (0x2)
T82D4 002:735.709 JLINK_ReadMemEx(0x08016188, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.731   CPU_ReadMem(64 bytes @ 0x080161C0)
T82D4 002:736.539    -- Updating C cache (64 bytes @ 0x080161C0)
T82D4 002:736.571    -- Read from C cache (60 bytes @ 0x08016188)
T82D4 002:736.597   Data:  10 49 41 64 45 F2 AA 50 21 46 08 80 8A 20 48 80 ...
T82D4 002:736.623 - 0.922ms returns 60 (0x3C)
T82D4 002:736.642 JLINK_ReadMemEx(0x08016188, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.661    -- Read from C cache (2 bytes @ 0x08016188)
T82D4 002:736.687   Data:  10 49
T82D4 002:736.711 - 0.078ms returns 2 (0x2)
T82D4 002:736.731 JLINK_ReadMemEx(0x08016188, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:736.750    -- Read from C cache (60 bytes @ 0x08016188)
T82D4 002:736.776   Data:  10 49 41 64 45 F2 AA 50 21 46 08 80 8A 20 48 80 ...
T82D4 002:736.801 - 0.077ms returns 60 (0x3C)
T82D4 002:736.819 JLINK_ReadMemEx(0x08016188, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.838    -- Read from C cache (2 bytes @ 0x08016188)
T82D4 002:736.864   Data:  10 49
T82D4 002:736.891 - 0.082ms returns 2 (0x2)
T82D4 002:736.911 JLINK_ReadMemEx(0x0801618A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.929    -- Read from C cache (2 bytes @ 0x0801618A)
T82D4 002:736.955   Data:  41 64
T82D4 002:736.981 - 0.078ms returns 2 (0x2)
T82D4 002:736.999 JLINK_ReadMemEx(0x0801618A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.018    -- Read from C cache (2 bytes @ 0x0801618A)
T82D4 002:737.043   Data:  41 64
T82D4 002:737.068 - 0.078ms returns 2 (0x2)
T82D4 002:737.087 JLINK_ReadMemEx(0x0801618C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.105    -- Read from C cache (60 bytes @ 0x0801618C)
T82D4 002:737.132   Data:  45 F2 AA 50 21 46 08 80 8A 20 48 80 07 20 88 80 ...
T82D4 002:737.157 - 0.078ms returns 60 (0x3C)
T82D4 002:737.175 JLINK_ReadMemEx(0x0801618C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.194    -- Read from C cache (2 bytes @ 0x0801618C)
T82D4 002:737.219   Data:  45 F2
T82D4 002:737.244 - 0.078ms returns 2 (0x2)
T82D4 002:737.264 JLINK_ReadMemEx(0x0801618C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.282    -- Read from C cache (60 bytes @ 0x0801618C)
T82D4 002:737.308   Data:  45 F2 AA 50 21 46 08 80 8A 20 48 80 07 20 88 80 ...
T82D4 002:737.334 - 0.078ms returns 60 (0x3C)
T82D4 002:737.353 JLINK_ReadMemEx(0x0801618C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.373    -- Read from C cache (2 bytes @ 0x0801618C)
T82D4 002:737.398   Data:  45 F2
T82D4 002:737.422 - 0.078ms returns 2 (0x2)
T82D4 002:737.441 JLINK_ReadMemEx(0x0801618E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.459    -- Read from C cache (2 bytes @ 0x0801618E)
T82D4 002:737.485   Data:  AA 50
T82D4 002:737.511 - 0.078ms returns 2 (0x2)
T82D4 002:737.530 JLINK_ReadMemEx(0x08016190, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.549    -- Read from C cache (60 bytes @ 0x08016190)
T82D4 002:737.575   Data:  21 46 08 80 8A 20 48 80 07 20 88 80 00 20 04 E0 ...
T82D4 002:737.601 - 0.080ms returns 60 (0x3C)
T82D4 002:737.619 JLINK_ReadMemEx(0x08016190, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.637    -- Read from C cache (2 bytes @ 0x08016190)
T82D4 002:737.663   Data:  21 46
T82D4 002:737.688 - 0.078ms returns 2 (0x2)
T82D4 002:737.707 JLINK_ReadMemEx(0x08016192, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.726    -- Read from C cache (2 bytes @ 0x08016192)
T82D4 002:737.751   Data:  08 80
T82D4 002:737.777 - 0.079ms returns 2 (0x2)
T82D4 002:737.796 JLINK_ReadMemEx(0x08016192, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.814    -- Read from C cache (2 bytes @ 0x08016192)
T82D4 002:737.840   Data:  08 80
T82D4 002:737.866 - 0.078ms returns 2 (0x2)
T82D4 002:737.883 JLINK_ReadMemEx(0x08016194, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.903    -- Read from C cache (60 bytes @ 0x08016194)
T82D4 002:737.929   Data:  8A 20 48 80 07 20 88 80 00 20 04 E0 14 F8 01 2B ...
T82D4 002:737.956 - 0.082ms returns 60 (0x3C)
T82D4 002:737.975 JLINK_ReadMemEx(0x08016194, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.992    -- Read from C cache (2 bytes @ 0x08016194)
T82D4 002:738.019   Data:  8A 20
T82D4 002:738.044 - 0.077ms returns 2 (0x2)
T82D4 002:738.062 JLINK_ReadMemEx(0x08016194, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.081    -- Read from C cache (60 bytes @ 0x08016194)
T82D4 002:738.108   Data:  8A 20 48 80 07 20 88 80 00 20 04 E0 14 F8 01 2B ...
T82D4 002:738.133 - 0.079ms returns 60 (0x3C)
T82D4 002:738.152 JLINK_ReadMemEx(0x08016194, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.169    -- Read from C cache (2 bytes @ 0x08016194)
T82D4 002:738.196   Data:  8A 20
T82D4 002:738.221 - 0.077ms returns 2 (0x2)
T82D4 002:738.238 JLINK_ReadMemEx(0x08016196, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.258    -- Read from C cache (2 bytes @ 0x08016196)
T82D4 002:738.283   Data:  48 80
T82D4 002:738.308 - 0.078ms returns 2 (0x2)
T82D4 002:738.327 JLINK_ReadMemEx(0x08016196, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.344    -- Read from C cache (2 bytes @ 0x08016196)
T82D4 002:738.371   Data:  48 80
T82D4 002:738.396 - 0.077ms returns 2 (0x2)
T82D4 002:738.414 JLINK_ReadMemEx(0x08016198, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.433    -- Read from C cache (60 bytes @ 0x08016198)
T82D4 002:738.459   Data:  07 20 88 80 00 20 04 E0 14 F8 01 2B 51 19 8D B2 ...
T82D4 002:738.484 - 0.079ms returns 60 (0x3C)
T82D4 002:738.503 JLINK_ReadMemEx(0x08016198, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.520    -- Read from C cache (2 bytes @ 0x08016198)
T82D4 002:738.548   Data:  07 20
T82D4 002:738.573 - 0.078ms returns 2 (0x2)
T82D4 002:738.591 JLINK_ReadMemEx(0x08016198, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.610    -- Read from C cache (60 bytes @ 0x08016198)
T82D4 002:738.636   Data:  07 20 88 80 00 20 04 E0 14 F8 01 2B 51 19 8D B2 ...
T82D4 002:738.662 - 0.079ms returns 60 (0x3C)
T82D4 002:738.680 JLINK_ReadMemEx(0x08016198, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.697    -- Read from C cache (2 bytes @ 0x08016198)
T82D4 002:738.724   Data:  07 20
T82D4 002:738.749 - 0.077ms returns 2 (0x2)
T82D4 002:738.767 JLINK_ReadMemEx(0x0801619A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.786    -- Read from C cache (2 bytes @ 0x0801619A)
T82D4 002:738.811   Data:  88 80
T82D4 002:738.839 - 0.080ms returns 2 (0x2)
T82D4 002:738.857 JLINK_ReadMemEx(0x0801619A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.876    -- Read from C cache (2 bytes @ 0x0801619A)
T82D4 002:738.903   Data:  88 80
T82D4 002:738.927 - 0.078ms returns 2 (0x2)
T82D4 002:738.945 JLINK_ReadMemEx(0x0801619C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.964    -- Read from C cache (60 bytes @ 0x0801619C)
T82D4 002:738.990   Data:  00 20 04 E0 14 F8 01 2B 51 19 8D B2 40 1C 88 28 ...
T82D4 002:739.016 - 0.079ms returns 60 (0x3C)
T82D4 002:739.034 JLINK_ReadMemEx(0x0801619C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.051    -- Read from C cache (2 bytes @ 0x0801619C)
T82D4 002:739.078   Data:  00 20
T82D4 002:739.103 - 0.077ms returns 2 (0x2)
T82D4 002:739.121 JLINK_ReadMemEx(0x0801619C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.140    -- Read from C cache (60 bytes @ 0x0801619C)
T82D4 002:739.166   Data:  00 20 04 E0 14 F8 01 2B 51 19 8D B2 40 1C 88 28 ...
T82D4 002:739.192 - 0.080ms returns 60 (0x3C)
T82D4 002:739.210 JLINK_ReadMemEx(0x0801619C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.227    -- Read from C cache (2 bytes @ 0x0801619C)
T82D4 002:739.254   Data:  00 20
T82D4 002:739.279 - 0.076ms returns 2 (0x2)
T82D4 002:739.296 JLINK_ReadMemEx(0x0801619E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.315    -- Read from C cache (2 bytes @ 0x0801619E)
T82D4 002:739.341   Data:  04 E0
T82D4 002:739.367 - 0.079ms returns 2 (0x2)
T82D4 002:739.385 JLINK_ReadMemEx(0x0801619E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.403    -- Read from C cache (2 bytes @ 0x0801619E)
T82D4 002:739.430   Data:  04 E0
T82D4 002:739.454 - 0.077ms returns 2 (0x2)
T82D4 002:739.472 JLINK_ReadMemEx(0x080161A0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.491    -- Read from C cache (60 bytes @ 0x080161A0)
T82D4 002:739.517   Data:  14 F8 01 2B 51 19 8D B2 40 1C 88 28 F8 D3 02 48 ...
T82D4 002:739.565 - 0.101ms returns 60 (0x3C)
T82D4 002:739.598 JLINK_ReadMemEx(0x080161A0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.616    -- Read from C cache (2 bytes @ 0x080161A0)
T82D4 002:739.643   Data:  14 F8
T82D4 002:739.667 - 0.077ms returns 2 (0x2)
T82D4 002:739.686 JLINK_ReadMemEx(0x080161A0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.705    -- Read from C cache (60 bytes @ 0x080161A0)
T82D4 002:739.731   Data:  14 F8 01 2B 51 19 8D B2 40 1C 88 28 F8 D3 02 48 ...
T82D4 002:739.757 - 0.079ms returns 60 (0x3C)
T82D4 002:739.775 JLINK_ReadMemEx(0x080161A0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.793    -- Read from C cache (2 bytes @ 0x080161A0)
T82D4 002:739.818   Data:  14 F8
T82D4 002:739.843 - 0.076ms returns 2 (0x2)
T82D4 002:739.861 JLINK_ReadMemEx(0x080161A2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.879    -- Read from C cache (2 bytes @ 0x080161A2)
T82D4 002:739.919   Data:  01 2B
T82D4 002:739.945 - 0.092ms returns 2 (0x2)
T82D4 002:739.963 JLINK_ReadMemEx(0x080161A4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.981    -- Read from C cache (60 bytes @ 0x080161A4)
T82D4 002:740.008   Data:  51 19 8D B2 40 1C 88 28 F8 D3 02 48 A0 F8 88 50 ...
T82D4 002:740.032 - 0.076ms returns 60 (0x3C)
T82D4 002:740.050 JLINK_ReadMemEx(0x080161A4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.068    -- Read from C cache (2 bytes @ 0x080161A4)
T82D4 002:740.092   Data:  51 19
T82D4 002:740.118 - 0.076ms returns 2 (0x2)
T82D4 002:740.135 JLINK_ReadMemEx(0x080161A6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.153    -- Read from C cache (2 bytes @ 0x080161A6)
T82D4 002:740.179   Data:  8D B2
T82D4 002:740.203 - 0.075ms returns 2 (0x2)
T82D4 002:740.221 JLINK_ReadMemEx(0x080161A6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.239    -- Read from C cache (2 bytes @ 0x080161A6)
T82D4 002:740.264   Data:  8D B2
T82D4 002:740.289 - 0.076ms returns 2 (0x2)
T82D4 002:740.307 JLINK_ReadMemEx(0x080161A8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:740.325    -- Read from C cache (60 bytes @ 0x080161A8)
T82D4 002:740.351   Data:  40 1C 88 28 F8 D3 02 48 A0 F8 88 50 00 20 70 BD ...
T82D4 002:740.375 - 0.075ms returns 60 (0x3C)
T82D4 002:740.393 JLINK_ReadMemEx(0x080161A8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.412    -- Read from C cache (2 bytes @ 0x080161A8)
T82D4 002:740.437   Data:  40 1C
T82D4 002:740.462 - 0.077ms returns 2 (0x2)
T82D4 002:740.480 JLINK_ReadMemEx(0x080161A8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:740.498    -- Read from C cache (60 bytes @ 0x080161A8)
T82D4 002:740.525   Data:  40 1C 88 28 F8 D3 02 48 A0 F8 88 50 00 20 70 BD ...
T82D4 002:740.549 - 0.077ms returns 60 (0x3C)
T82D4 002:740.568 JLINK_ReadMemEx(0x080161A8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.586    -- Read from C cache (2 bytes @ 0x080161A8)
T82D4 002:740.611   Data:  40 1C
T82D4 002:740.636 - 0.076ms returns 2 (0x2)
T82D4 002:740.653 JLINK_ReadMemEx(0x080161AA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.672    -- Read from C cache (2 bytes @ 0x080161AA)
T82D4 002:740.696   Data:  88 28
T82D4 002:740.720 - 0.076ms returns 2 (0x2)
T82D4 002:740.739 JLINK_ReadMemEx(0x080161AA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.757    -- Read from C cache (2 bytes @ 0x080161AA)
T82D4 002:740.782   Data:  88 28
T82D4 002:740.808 - 0.076ms returns 2 (0x2)
T82D4 002:740.825 JLINK_ReadMemEx(0x080161AC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:740.844    -- Read from C cache (60 bytes @ 0x080161AC)
T82D4 002:740.869   Data:  F8 D3 02 48 A0 F8 88 50 00 20 70 BD FA 7C 01 20 ...
T82D4 002:740.893 - 0.076ms returns 60 (0x3C)
T82D4 002:740.911 JLINK_ReadMemEx(0x080161AC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.929    -- Read from C cache (2 bytes @ 0x080161AC)
T82D4 002:740.954   Data:  F8 D3
T82D4 002:740.979 - 0.076ms returns 2 (0x2)
T82D4 002:740.997 JLINK_ReadMemEx(0x080161AC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.015    -- Read from C cache (60 bytes @ 0x080161AC)
T82D4 002:741.040   Data:  F8 D3 02 48 A0 F8 88 50 00 20 70 BD FA 7C 01 20 ...
T82D4 002:741.064 - 0.076ms returns 60 (0x3C)
T82D4 002:741.083 JLINK_ReadMemEx(0x080161AC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.100    -- Read from C cache (2 bytes @ 0x080161AC)
T82D4 002:741.126   Data:  F8 D3
T82D4 002:741.151 - 0.076ms returns 2 (0x2)
T82D4 002:741.168 JLINK_ReadMemEx(0x080161AE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.187    -- Read from C cache (2 bytes @ 0x080161AE)
T82D4 002:741.212   Data:  02 48
T82D4 002:741.236 - 0.076ms returns 2 (0x2)
T82D4 002:741.255 JLINK_ReadMemEx(0x080161AE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.273    -- Read from C cache (2 bytes @ 0x080161AE)
T82D4 002:741.298   Data:  02 48
T82D4 002:741.323 - 0.076ms returns 2 (0x2)
T82D4 002:741.340 JLINK_ReadMemEx(0x080161B0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.359    -- Read from C cache (60 bytes @ 0x080161B0)
T82D4 002:741.384   Data:  A0 F8 88 50 00 20 70 BD FA 7C 01 20 62 95 01 20 ...
T82D4 002:741.408 - 0.079ms returns 60 (0x3C)
T82D4 002:741.430 JLINK_ReadMemEx(0x080161B0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.447    -- Read from C cache (2 bytes @ 0x080161B0)
T82D4 002:741.472   Data:  A0 F8
T82D4 002:741.497 - 0.076ms returns 2 (0x2)
T82D4 002:741.515 JLINK_ReadMemEx(0x080161B0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.535    -- Read from C cache (60 bytes @ 0x080161B0)
T82D4 002:741.560   Data:  A0 F8 88 50 00 20 70 BD FA 7C 01 20 62 95 01 20 ...
T82D4 002:741.585 - 0.078ms returns 60 (0x3C)
T82D4 002:741.603 JLINK_ReadMemEx(0x080161B0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.620    -- Read from C cache (2 bytes @ 0x080161B0)
T82D4 002:741.646   Data:  A0 F8
T82D4 002:741.670 - 0.075ms returns 2 (0x2)
T82D4 002:741.687 JLINK_ReadMemEx(0x080161B2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.706    -- Read from C cache (2 bytes @ 0x080161B2)
T82D4 002:741.731   Data:  88 50
T82D4 002:741.756 - 0.077ms returns 2 (0x2)
T82D4 002:741.775 JLINK_ReadMemEx(0x080161B4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.792    -- Read from C cache (60 bytes @ 0x080161B4)
T82D4 002:741.819   Data:  00 20 70 BD FA 7C 01 20 62 95 01 20 49 4E 53 36 ...
T82D4 002:741.843 - 0.076ms returns 60 (0x3C)
T82D4 002:741.860 JLINK_ReadMemEx(0x080161B4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.880    -- Read from C cache (2 bytes @ 0x080161B4)
T82D4 002:741.905   Data:  00 20
T82D4 002:741.930 - 0.078ms returns 2 (0x2)
T82D4 002:741.948 JLINK_ReadMemEx(0x080161B6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.965    -- Read from C cache (2 bytes @ 0x080161B6)
T82D4 002:741.991   Data:  70 BD
T82D4 002:742.015 - 0.076ms returns 2 (0x2)
T82D4 002:742.033 JLINK_ReadMemEx(0x080161B6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.052    -- Read from C cache (2 bytes @ 0x080161B6)
T82D4 002:742.077   Data:  70 BD
T82D4 002:742.101 - 0.077ms returns 2 (0x2)
T82D4 002:742.120 JLINK_ReadMemEx(0x080161B8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:742.137    -- Read from C cache (60 bytes @ 0x080161B8)
T82D4 002:742.164   Data:  FA 7C 01 20 62 95 01 20 49 4E 53 36 32 32 2D 32 ...
T82D4 002:742.188 - 0.076ms returns 60 (0x3C)
T82D4 002:742.205 JLINK_ReadMemEx(0x080161B8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.224    -- Read from C cache (2 bytes @ 0x080161B8)
T82D4 002:742.249   Data:  FA 7C
T82D4 002:742.273 - 0.077ms returns 2 (0x2)
T82D4 002:742.298 JLINK_ReadMemEx(0x080161CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.316    -- Read from C cache (2 bytes @ 0x080161CE)
T82D4 002:742.342   Data:  29 00
T82D4 002:742.367 - 0.077ms returns 2 (0x2)
T82D4 002:742.384 JLINK_ReadMemEx(0x080161D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:742.404   CPU_ReadMem(64 bytes @ 0x08016200)
T82D4 002:743.387    -- Updating C cache (64 bytes @ 0x08016200)
T82D4 002:743.425    -- Read from C cache (60 bytes @ 0x080161D0)
T82D4 002:743.452   Data:  00 25 00 24 00 26 00 27 A0 46 F9 F7 05 FC F5 F7 ...
T82D4 002:743.476 - 1.100ms returns 60 (0x3C)
T82D4 002:743.495 JLINK_ReadMemEx(0x080161D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.516    -- Read from C cache (2 bytes @ 0x080161D0)
T82D4 002:743.595   Data:  00 25
T82D4 002:743.635 - 0.151ms returns 2 (0x2)
T82D4 002:743.681 JLINK_ReadMemEx(0x080161D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:743.711    -- Read from C cache (60 bytes @ 0x080161D0)
T82D4 002:743.751   Data:  00 25 00 24 00 26 00 27 A0 46 F9 F7 05 FC F5 F7 ...
T82D4 002:743.788 - 0.120ms returns 60 (0x3C)
T82D4 002:743.814 JLINK_ReadMemEx(0x080161D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.835    -- Read from C cache (2 bytes @ 0x080161D0)
T82D4 002:743.916   Data:  00 25
T82D4 002:743.982 - 0.197ms returns 2 (0x2)
T82D4 002:744.021 JLINK_ReadMemEx(0x080161D2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.039    -- Read from C cache (2 bytes @ 0x080161D2)
T82D4 002:744.107   Data:  00 24
T82D4 002:744.131 - 0.118ms returns 2 (0x2)
T82D4 002:744.154 JLINK_ReadMemEx(0x080161D2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.173    -- Read from C cache (2 bytes @ 0x080161D2)
T82D4 002:744.198   Data:  00 24
T82D4 002:744.224 - 0.078ms returns 2 (0x2)
T82D4 002:744.242 JLINK_ReadMemEx(0x080161D4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:744.260    -- Read from C cache (60 bytes @ 0x080161D4)
T82D4 002:744.286   Data:  00 26 00 27 A0 46 F9 F7 05 FC F5 F7 B5 FB 2F E0 ...
T82D4 002:744.310 - 0.076ms returns 60 (0x3C)
T82D4 002:744.328 JLINK_ReadMemEx(0x080161D4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.347    -- Read from C cache (2 bytes @ 0x080161D4)
T82D4 002:744.371   Data:  00 26
T82D4 002:744.397 - 0.077ms returns 2 (0x2)
