T82D4 000:011.406   SEGGER J-Link V7.22b Log File
T82D4 000:011.638   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:011.663   Logging started @ 2025-06-11 02:29
T82D4 000:011.685 - 11.697ms
T82D4 000:011.716 JLINK_SetWarnOutHandler(...)
T82D4 000:011.741 - 0.035ms
T82D4 000:011.769 JLINK_OpenEx(...)
T82D4 000:013.549   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.003   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.309   Decompressing FW timestamp took 175 us
T82D4 000:020.722   Hardware: V9.40
T82D4 000:020.783   S/N: 59406895
T82D4 000:020.813   OEM: SEGGER
T82D4 000:020.841   Feature(s): <PERSON><PERSON>, GD<PERSON>, FlashD<PERSON>, <PERSON>B<PERSON>, J<PERSON>lash
T82D4 000:021.830   TELNET listener socket opened on port 19021
T82D4 000:022.030   WEBSRV Starting webserver
T82D4 000:022.191   WEBSRV Webserver running on local port 19080
T82D4 000:022.228 - 10.468ms returns "O.K."
T82D4 000:022.255 JLINK_GetEmuCaps()
T82D4 000:022.276 - 0.029ms returns 0xB9FF7BBF
T82D4 000:022.298 JLINK_TIF_GetAvailable(...)
T82D4 000:022.526 - 0.247ms
T82D4 000:022.559 JLINK_SetErrorOutHandler(...)
T82D4 000:022.579 - 0.028ms
T82D4 000:022.619 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.672   Device "GD32F450II" selected.
T82D4 000:033.328 - 10.729ms returns 0x00
T82D4 000:033.382 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:034.273   Device "GD32F450II" selected.
T82D4 000:034.915 - 1.526ms returns 0x00
T82D4 000:034.963 JLINK_GetHardwareVersion()
T82D4 000:034.995 - 0.042ms returns 94000
T82D4 000:035.025 JLINK_GetDLLVersion()
T82D4 000:035.044 - 0.028ms returns 72202
T82D4 000:035.064 JLINK_GetOEMString(...)
T82D4 000:035.084 JLINK_GetFirmwareString(...)
T82D4 000:035.109 - 0.037ms
T82D4 000:035.161 JLINK_GetDLLVersion()
T82D4 000:035.181 - 0.029ms returns 72202
T82D4 000:035.200 JLINK_GetCompileDateTime()
T82D4 000:035.217 - 0.025ms
T82D4 000:035.241 JLINK_GetFirmwareString(...)
T82D4 000:035.260 - 0.028ms
T82D4 000:035.283 JLINK_GetHardwareVersion()
T82D4 000:035.303 - 0.028ms returns 94000
T82D4 000:035.324 JLINK_GetSN()
T82D4 000:035.344 - 0.028ms returns 59406895
T82D4 000:035.366 JLINK_GetOEMString(...)
T82D4 000:035.400 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.902 - 0.520ms returns 0x00
T82D4 000:035.933 JLINK_HasError()
T82D4 000:035.963 JLINK_SetSpeed(2000)
T82D4 000:036.043 - 0.093ms
T82D4 000:036.068 JLINK_GetId()
T82D4 000:036.658   Found SW-DP with ID 0x2BA01477
T82D4 000:039.289   DPIDR: 0x2BA01477
T82D4 000:039.371   Scanning AP map to find all available APs
T82D4 000:039.864   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:039.900   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:039.929   Iterating through AP map to find AHB-AP to use
T82D4 000:040.747   AP[0]: Core found
T82D4 000:040.782   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:041.232   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:041.267   Found Cortex-M4 r0p1, Little endian.
T82D4 000:142.224   -- Max. mem block: 0x00010E60
T82D4 000:142.446   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:142.813   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:143.185   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:143.580   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:143.619   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:143.984   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:144.395   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:144.782   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:145.142   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:145.495   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:145.887   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:146.247   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:146.629   CoreSight components:
T82D4 000:146.671   ROMTbl[0] @ E00FF000
T82D4 000:146.698   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:147.524   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:148.155   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:148.186   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:148.963   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:149.002   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:149.752   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:149.790   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:150.503   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:150.541   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:151.322   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:151.360   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:151.962   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:152.245 - 116.197ms returns 0x2BA01477
T82D4 000:152.281 JLINK_GetDLLVersion()
T82D4 000:152.300 - 0.027ms returns 72202
T82D4 000:152.414 JLINK_CORE_GetFound()
T82D4 000:152.443 - 0.037ms returns 0xE0000FF
T82D4 000:152.462 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.479   Value=0xE00FF000
T82D4 000:152.508 - 0.054ms returns 0
T82D4 000:152.533 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.553   Value=0xE00FF000
T82D4 000:152.578 - 0.073ms returns 0
T82D4 000:152.633 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:152.651   Value=0x00000000
T82D4 000:152.725 - 0.101ms returns 0
T82D4 000:152.766 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:152.803   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:153.263   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:153.337 - 0.595ms returns 16 (0x10)
T82D4 000:153.372 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:153.390   Value=0x00000000
T82D4 000:153.415 - 0.051ms returns 0
T82D4 000:153.432 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:153.450   Value=0x********
T82D4 000:153.476 - 0.051ms returns 0
T82D4 000:153.494 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:153.544   Value=0x********
T82D4 000:153.577 - 0.092ms returns 0
T82D4 000:153.636 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:153.663   Value=0xE0001000
T82D4 000:153.700 - 0.090ms returns 0
T82D4 000:153.753 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:153.795   Value=0xE0002000
T82D4 000:153.840 - 0.096ms returns 0
T82D4 000:153.861 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:153.893   Value=0xE000E000
T82D4 000:153.933 - 0.080ms returns 0
T82D4 000:153.951 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:153.968   Value=0xE000EDF0
T82D4 000:153.993 - 0.050ms returns 0
T82D4 000:154.011 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:154.027   Value=0x00000001
T82D4 000:154.052 - 0.049ms returns 0
T82D4 000:154.070 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:154.093   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:154.408   Data:  41 C2 0F 41
T82D4 000:154.514   Debug reg: CPUID
T82D4 000:154.570 - 0.529ms returns 1 (0x1)
T82D4 000:154.611 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:154.689   Value=0x00000000
T82D4 000:154.743 - 0.141ms returns 0
T82D4 000:154.764 JLINK_HasError()
T82D4 000:154.786 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:154.806 - 0.053ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:154.862 JLINK_Reset()
T82D4 000:154.905   CPU is running
T82D4 000:154.937   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:155.285   CPU is running
T82D4 000:155.322   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:155.688   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:156.013   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:156.045   CPU is running
T82D4 000:156.072   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:208.619   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:208.990   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.376   CPU is running
T82D4 000:209.409   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.765   CPU is running
T82D4 000:209.865   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:215.313   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:219.304   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:219.680   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:219.984   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:220.333 - 65.485ms
T82D4 000:220.370 JLINK_HasError()
T82D4 000:220.390 JLINK_ReadReg(R15 (PC))
T82D4 000:220.418 - 0.036ms returns 0x080001C4
T82D4 000:220.437 JLINK_ReadReg(XPSR)
T82D4 000:220.456 - 0.028ms returns 0x01000000
T82D4 000:220.528 JLINK_Halt()
T82D4 000:220.550 - 0.029ms returns 0x00
T82D4 000:220.568 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:220.589   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:220.904   Data:  03 00 03 00
T82D4 000:220.938   Debug reg: DHCSR
T82D4 000:220.962 - 0.426ms returns 1 (0x1)
T82D4 000:221.005 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:221.042   Debug reg: DHCSR
T82D4 000:221.489   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:221.879 - 0.894ms returns 0 (0x00000000)
T82D4 000:221.912 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:221.931   Debug reg: DEMCR
T82D4 000:221.966   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:222.356 - 0.460ms returns 0 (0x00000000)
T82D4 000:222.416 JLINK_GetHWStatus(...)
T82D4 000:222.601 - 0.198ms returns 0
T82D4 000:222.637 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:222.658 - 0.028ms returns 0x06
T82D4 000:222.675 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:222.693 - 0.026ms returns 0x2000
T82D4 000:222.711 JLINK_GetNumWPUnits()
T82D4 000:222.728 - 0.026ms returns 4
T82D4 000:222.755 JLINK_GetSpeed()
T82D4 000:222.774 - 0.027ms returns 2000
T82D4 000:222.797 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:222.820   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.131   Data:  02 00 00 00
T82D4 000:223.161 - 0.372ms returns 1 (0x1)
T82D4 000:223.179 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.200   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.501   Data:  02 00 00 00
T82D4 000:223.531 - 0.360ms returns 1 (0x1)
T82D4 000:223.550 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:223.568   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:223.599   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:224.148 - 0.671ms returns 0x1C
T82D4 000:224.282 JLINK_HasError()
T82D4 000:224.302 JLINK_ReadReg(R15 (PC))
T82D4 000:224.321 - 0.027ms returns 0x080001C4
T82D4 000:224.339 JLINK_ReadReg(XPSR)
T82D4 000:224.357 - 0.026ms returns 0x01000000
T82D4 000:226.715 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:226.746   Data:  00 00 00 00
T82D4 000:226.773   Debug reg: DWT_CYCCNT
T82D4 000:226.798 - 0.090ms returns 4 (0x4)
T82D4 000:295.880 JLINK_HasError()
T82D4 000:295.940 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:295.960 - 0.029ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:295.982 JLINK_Reset()
T82D4 000:296.012   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:296.382   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:296.767   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:297.241   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:297.290   CPU_WriteMem(4 bytes @ 0xE000ED0C)
