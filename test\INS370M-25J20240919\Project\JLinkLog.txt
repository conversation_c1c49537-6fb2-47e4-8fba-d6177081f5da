T82D4 000:011.967   SEGGER J-Link V7.22b Log File
T82D4 000:012.218   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.244   Logging started @ 2025-06-11 02:11
T82D4 000:012.266 - 12.279ms
T82D4 000:012.300 JLINK_SetWarnOutHandler(...)
T82D4 000:012.326 - 0.037ms
T82D4 000:012.349 JLINK_OpenEx(...)
T82D4 000:013.920   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.337   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.501   Decompressing FW timestamp took 109 us
T82D4 000:020.485   Hardware: V9.40
T82D4 000:020.557   S/N: 59406895
T82D4 000:020.595   OEM: SEGGER
T82D4 000:020.624   Feature(s): R<PERSON>, GD<PERSON>, FlashDL, FlashB<PERSON>, J<PERSON>lash
T82D4 000:021.509   TELNET listener socket opened on port 19021
T82D4 000:021.706   WEBSRV Starting webserver
T82D4 000:021.912   WEBSRV Webserver running on local port 19080
T82D4 000:021.982 - 9.642ms returns "O.K."
T82D4 000:022.009 JLINK_GetEmuCaps()
T82D4 000:022.067 - 0.068ms returns 0xB9FF7BBF
T82D4 000:022.090 JLINK_TIF_GetAvailable(...)
T82D4 000:022.317 - 0.242ms
T82D4 000:022.345 JLINK_SetErrorOutHandler(...)
T82D4 000:022.384 - 0.047ms
T82D4 000:022.456 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.278   Device "GD32F450II" selected.
T82D4 000:032.857 - 10.417ms returns 0x00
T82D4 000:032.901 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.620   Device "GD32F450II" selected.
T82D4 000:034.117 - 1.203ms returns 0x00
T82D4 000:034.149 JLINK_GetHardwareVersion()
T82D4 000:034.168 - 0.027ms returns 94000
T82D4 000:034.186 JLINK_GetDLLVersion()
T82D4 000:034.203 - 0.026ms returns 72202
T82D4 000:034.222 JLINK_GetOEMString(...)
T82D4 000:034.240 JLINK_GetFirmwareString(...)
T82D4 000:034.261 - 0.029ms
T82D4 000:034.290 JLINK_GetDLLVersion()
T82D4 000:034.310 - 0.028ms returns 72202
T82D4 000:034.329 JLINK_GetCompileDateTime()
T82D4 000:034.346 - 0.025ms
T82D4 000:034.368 JLINK_GetFirmwareString(...)
T82D4 000:034.388 - 0.028ms
T82D4 000:034.409 JLINK_GetHardwareVersion()
T82D4 000:034.429 - 0.027ms returns 94000
T82D4 000:034.450 JLINK_GetSN()
T82D4 000:034.490 - 0.049ms returns 59406895
T82D4 000:034.512 JLINK_GetOEMString(...)
T82D4 000:034.560 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.200 - 0.657ms returns 0x00
T82D4 000:035.244 JLINK_HasError()
T82D4 000:035.303 JLINK_SetSpeed(2000)
T82D4 000:035.469 - 0.185ms
T82D4 000:035.500 JLINK_GetId()
T82D4 000:036.140   Found SW-DP with ID 0x2BA01477
T82D4 000:038.594   DPIDR: 0x2BA01477
T82D4 000:038.655   Scanning AP map to find all available APs
T82D4 000:039.183   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:039.228   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:039.260   Iterating through AP map to find AHB-AP to use
T82D4 000:040.087   AP[0]: Core found
T82D4 000:040.133   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.602   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.640   Found Cortex-M4 r0p1, Little endian.
