T82D4 000:011.733   SEG<PERSON>R J-Link V7.22b Log File
T82D4 000:011.981   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.006   Logging started @ 2025-06-11 02:22
T82D4 000:012.027 - 12.038ms
T82D4 000:012.059 JLINK_SetWarnOutHandler(...)
T82D4 000:012.087 - 0.054ms
T82D4 000:012.137 JLINK_OpenEx(...)
T82D4 000:013.699   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.068   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.224   Decompressing FW timestamp took 110 us
T82D4 000:020.079   Hardware: V9.40
T82D4 000:020.119   S/N: 59406895
T82D4 000:020.152   OEM: SEGGER
T82D4 000:020.179   Feature(s): <PERSON><PERSON>, G<PERSON><PERSON>, FlashDL, FlashB<PERSON>, J<PERSON><PERSON>
T82D4 000:021.022   TELNET listener socket opened on port 19021
T82D4 000:021.192   WEBSRV Starting webserver
T82D4 000:021.369   WEBSRV Webserver running on local port 19080
T82D4 000:021.428 - 9.300ms returns "O.K."
T82D4 000:021.454 JLINK_GetEmuCaps()
T82D4 000:021.513 - 0.068ms returns 0xB9FF7BBF
T82D4 000:021.539 JLINK_TIF_GetAvailable(...)
T82D4 000:021.713 - 0.188ms
T82D4 000:021.754 JLINK_SetErrorOutHandler(...)
T82D4 000:021.795 - 0.048ms
T82D4 000:021.864 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:031.765   Device "GD32F450II" selected.
T82D4 000:032.371 - 10.542ms returns 0x00
T82D4 000:032.433 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.102   Device "GD32F450II" selected.
T82D4 000:034.003 - 1.550ms returns 0x00
T82D4 000:034.056 JLINK_GetHardwareVersion()
T82D4 000:034.076 - 0.029ms returns 94000
T82D4 000:034.110 JLINK_GetDLLVersion()
T82D4 000:034.127 - 0.026ms returns 72202
T82D4 000:034.146 JLINK_GetOEMString(...)
T82D4 000:034.179 JLINK_GetFirmwareString(...)
T82D4 000:034.221 - 0.050ms
T82D4 000:034.311 JLINK_GetDLLVersion()
T82D4 000:034.333 - 0.030ms returns 72202
T82D4 000:034.367 JLINK_GetCompileDateTime()
T82D4 000:034.420 - 0.082ms
T82D4 000:034.466 JLINK_GetFirmwareString(...)
T82D4 000:034.486 - 0.028ms
T82D4 000:034.511 JLINK_GetHardwareVersion()
T82D4 000:034.567 - 0.065ms returns 94000
T82D4 000:034.592 JLINK_GetSN()
T82D4 000:034.649 - 0.065ms returns 59406895
T82D4 000:034.673 JLINK_GetOEMString(...)
T82D4 000:034.723 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.345 - 0.640ms returns 0x00
T82D4 000:035.376 JLINK_HasError()
T82D4 000:035.408 JLINK_SetSpeed(2000)
T82D4 000:035.484 - 0.089ms
T82D4 000:035.508 JLINK_GetId()
T82D4 000:036.155   Found SW-DP with ID 0x2BA01477
T82D4 000:038.626   DPIDR: 0x2BA01477
T82D4 000:038.694   Scanning AP map to find all available APs
T82D4 000:039.244   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:039.280   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:039.311   Iterating through AP map to find AHB-AP to use
T82D4 000:040.112   AP[0]: Core found
T82D4 000:040.148   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.585   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.620   Found Cortex-M4 r0p1, Little endian.
T82D4 000:141.604   -- Max. mem block: 0x00010E60
T82D4 000:142.143   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:142.905   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:143.675   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:144.484   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:144.793   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:145.880   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:146.943   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:147.649   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:148.219   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:148.633   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:148.990   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:149.357   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:149.740   CoreSight components:
T82D4 000:149.782   ROMTbl[0] @ E00FF000
T82D4 000:149.809   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:150.583   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:151.187   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:151.304   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:151.888   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:151.935   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:152.597   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:152.714   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:153.351   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:153.472   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:154.054   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:154.178   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:154.728   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:154.945 - 119.450ms returns 0x2BA01477
T82D4 000:154.973 JLINK_GetDLLVersion()
T82D4 000:154.991 - 0.026ms returns 72202
T82D4 000:155.012 JLINK_CORE_GetFound()
T82D4 000:155.030 - 0.026ms returns 0xE0000FF
T82D4 000:155.049 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:155.067   Value=0xE00FF000
T82D4 000:155.091 - 0.051ms returns 0
T82D4 000:155.115 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:155.135   Value=0xE00FF000
T82D4 000:155.159 - 0.051ms returns 0
T82D4 000:155.176 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:155.195   Value=0x00000000
T82D4 000:155.219 - 0.051ms returns 0
T82D4 000:155.244 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:155.281   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:155.779   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:155.809 - 0.574ms returns 16 (0x10)
T82D4 000:155.830 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:155.847   Value=0x00000000
T82D4 000:155.873 - 0.050ms returns 0
T82D4 000:155.890 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:155.908   Value=0x********
T82D4 000:155.932 - 0.050ms returns 0
T82D4 000:155.950 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:155.969   Value=0x********
T82D4 000:155.993 - 0.051ms returns 0
T82D4 000:156.012 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:156.053   Value=0xE0001000
T82D4 000:156.095 - 0.091ms returns 0
T82D4 000:156.113 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:156.131   Value=0xE0002000
T82D4 000:156.155 - 0.049ms returns 0
T82D4 000:156.172 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:156.191   Value=0xE000E000
T82D4 000:156.215 - 0.050ms returns 0
T82D4 000:156.233 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:156.251   Value=0xE000EDF0
T82D4 000:156.275 - 0.051ms returns 0
T82D4 000:156.295 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:156.312   Value=0x00000001
T82D4 000:156.337 - 0.050ms returns 0
T82D4 000:156.355 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:156.376   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:156.690   Data:  41 C2 0F 41
T82D4 000:156.721   Debug reg: CPUID
T82D4 000:156.746 - 0.399ms returns 1 (0x1)
T82D4 000:156.766 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:156.784   Value=0x00000000
T82D4 000:156.808 - 0.050ms returns 0
T82D4 000:156.826 JLINK_HasError()
T82D4 000:156.846 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:156.863 - 0.026ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:156.884 JLINK_Reset()
T82D4 000:156.909   CPU is running
T82D4 000:156.938   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:157.270   CPU is running
T82D4 000:157.301   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:157.638   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:158.199   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:158.240   CPU is running
T82D4 000:158.267   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:211.640   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:212.452   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:213.316   CPU is running
T82D4 000:213.526   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:214.197   CPU is running
T82D4 000:214.618   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:220.742   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:224.791   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:225.245   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:225.753   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:226.150 - 69.286ms
T82D4 000:226.195 JLINK_HasError()
T82D4 000:226.215 JLINK_ReadReg(R15 (PC))
T82D4 000:226.242 - 0.036ms returns 0x080001C4
T82D4 000:226.262 JLINK_ReadReg(XPSR)
T82D4 000:226.281 - 0.027ms returns 0x01000000
T82D4 000:226.300 JLINK_Halt()
T82D4 000:226.319 - 0.026ms returns 0x00
T82D4 000:226.337 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:226.360   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:226.764   Data:  03 00 03 00
T82D4 000:226.848   Debug reg: DHCSR
T82D4 000:226.873 - 0.544ms returns 1 (0x1)
T82D4 000:226.893 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:226.916   Debug reg: DHCSR
T82D4 000:227.274   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:227.709 - 0.835ms returns 0 (0x00000000)
T82D4 000:227.741 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:227.760   Debug reg: DEMCR
T82D4 000:227.796   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:228.131 - 0.403ms returns 0 (0x00000000)
T82D4 000:228.189 JLINK_GetHWStatus(...)
T82D4 000:228.307 - 0.130ms returns 0
T82D4 000:228.344 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:228.363 - 0.027ms returns 0x06
T82D4 000:228.381 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:228.400 - 0.026ms returns 0x2000
T82D4 000:228.417 JLINK_GetNumWPUnits()
T82D4 000:228.435 - 0.026ms returns 4
T82D4 000:228.462 JLINK_GetSpeed()
T82D4 000:228.481 - 0.027ms returns 2000
T82D4 000:228.505 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:228.527   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:228.837   Data:  02 00 00 00
T82D4 000:228.868 - 0.371ms returns 1 (0x1)
T82D4 000:228.888 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:228.909   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:229.234   Data:  02 00 00 00
T82D4 000:229.263 - 0.383ms returns 1 (0x1)
T82D4 000:229.282 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:229.301   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:229.330   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:229.875 - 0.663ms returns 0x1C
T82D4 000:230.113 JLINK_HasError()
T82D4 000:230.147 JLINK_ReadReg(R15 (PC))
T82D4 000:230.166 - 0.028ms returns 0x080001C4
T82D4 000:230.185 JLINK_ReadReg(XPSR)
T82D4 000:230.204 - 0.027ms returns 0x01000000
T82D4 000:232.551 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:232.582   Data:  00 00 00 00
T82D4 000:232.608   Debug reg: DWT_CYCCNT
T82D4 000:232.633 - 0.089ms returns 4 (0x4)
T82D4 000:314.545 JLINK_HasError()
T82D4 000:314.615 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:314.635 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:314.654 JLINK_Reset()
T82D4 000:314.685   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:315.091   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:315.461   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:315.844   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:315.879   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:369.261   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:369.716   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:370.143   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:370.581   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:376.207   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:380.029   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:380.447   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:380.837   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:381.201 - 66.559ms
T82D4 000:381.298 JLINK_HasError()
T82D4 000:381.319 JLINK_ReadReg(R15 (PC))
T82D4 000:381.339 - 0.029ms returns 0x080001C4
T82D4 000:381.358 JLINK_ReadReg(XPSR)
T82D4 000:381.375 - 0.025ms returns 0x01000000
T82D4 000:381.451 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:381.477   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:382.286    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:382.319    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:382.346   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:382.371 - 0.928ms returns 60 (0x3C)
T82D4 000:382.390 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:382.410    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:382.434   Data:  06 48
T82D4 000:382.459 - 0.078ms returns 2 (0x2)
T82D4 000:382.517 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:382.537    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:382.562   Data:  80 47
T82D4 000:382.586 - 0.077ms returns 2 (0x2)
T82D4 000:382.620 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:382.640    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:382.665   Data:  80 47
T82D4 000:382.691 - 0.079ms returns 2 (0x2)
T82D4 000:382.708 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:382.728   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:383.535    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:383.565    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:383.591   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:383.617 - 0.917ms returns 60 (0x3C)
T82D4 000:383.635 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.654    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:383.679   Data:  06 48
T82D4 000:383.703 - 0.077ms returns 2 (0x2)
T82D4 000:383.729 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:383.748    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:383.774   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:383.799 - 0.077ms returns 60 (0x3C)
T82D4 000:383.816 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.835    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:383.860   Data:  06 48
T82D4 000:383.884 - 0.076ms returns 2 (0x2)
T82D4 000:383.903 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:383.920    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:383.946   Data:  00 47
T82D4 000:383.971 - 0.076ms returns 2 (0x2)
T82D4 000:383.994 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:384.014    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:384.038   Data:  00 47
T82D4 000:384.062 - 0.077ms returns 2 (0x2)
T82D4 000:384.081 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:384.098    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:384.124   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:384.149 - 0.076ms returns 60 (0x3C)
T82D4 000:384.166 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:384.185    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:384.210   Data:  FE E7
T82D4 000:384.234 - 0.076ms returns 2 (0x2)
T82D4 000:384.258 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:384.278    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:384.304   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:384.328 - 0.078ms returns 60 (0x3C)
T82D4 000:384.345 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:384.364    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:384.389   Data:  FE E7
T82D4 000:384.413 - 0.077ms returns 2 (0x2)
T82D4 000:384.432 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:384.481    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:384.522   Data:  FE E7
T82D4 000:384.547 - 0.122ms returns 2 (0x2)
T82D4 001:458.207 JLINK_ReadMemEx(0x080165C0, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:458.250   CPU_ReadMem(64 bytes @ 0x080165C0)
T82D4 001:459.050    -- Updating C cache (64 bytes @ 0x080165C0)
T82D4 001:459.110    -- Read from C cache (60 bytes @ 0x080165C0)
T82D4 001:459.138   Data:  90 B0 00 20 0F 90 01 90 05 E0 0F 98 40 1C 0F 90 ...
T82D4 001:459.163 - 1.019ms returns 60 (0x3C)
T82D4 001:459.243 JLINK_ReadMemEx(0x080165C0, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:459.271    -- Read from C cache (2 bytes @ 0x080165C0)
T82D4 001:459.305   Data:  90 B0
T82D4 001:459.339 - 0.109ms returns 2 (0x2)
T82D4 001:459.467 JLINK_ReadMemEx(0x080165C2, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:459.512    -- Read from C cache (2 bytes @ 0x080165C2)
T82D4 001:459.539   Data:  00 20
T82D4 001:459.576 - 0.153ms returns 2 (0x2)
T82D4 001:626.869 JLINK_HasError()
T82D4 001:626.933 JLINK_ReadReg(R0)
T82D4 001:627.318 - 0.426ms returns 0x00000003
T82D4 001:627.377 JLINK_ReadReg(R1)
T82D4 001:627.401 - 0.039ms returns 0x20022918
T82D4 001:627.428 JLINK_ReadReg(R2)
T82D4 001:627.449 - 0.030ms returns 0x0801587D
T82D4 001:627.470 JLINK_ReadReg(R3)
T82D4 001:627.489 - 0.027ms returns 0x0801587D
T82D4 001:627.508 JLINK_ReadReg(R4)
T82D4 001:627.529 - 0.030ms returns 0x0000003D
T82D4 001:627.548 JLINK_ReadReg(R5)
T82D4 001:627.569 - 0.030ms returns 0x00000000
T82D4 001:627.589 JLINK_ReadReg(R6)
T82D4 001:627.608 - 0.029ms returns 0x08016688
T82D4 001:627.630 JLINK_ReadReg(R7)
T82D4 001:627.648 - 0.028ms returns 0x00000000
T82D4 001:627.669 JLINK_ReadReg(R8)
T82D4 001:627.749 - 0.091ms returns 0x200229BC
T82D4 001:627.771 JLINK_ReadReg(R9)
T82D4 001:627.793 - 0.033ms returns 0x20000348
T82D4 001:627.815 JLINK_ReadReg(R10)
T82D4 001:627.835 - 0.033ms returns 0x0801587D
T82D4 001:627.859 JLINK_ReadReg(R11)
T82D4 001:627.878 - 0.028ms returns 0x00000000
T82D4 001:627.899 JLINK_ReadReg(R12)
T82D4 001:627.921 - 0.032ms returns 0x00000000
T82D4 001:627.942 JLINK_ReadReg(R13 (SP))
T82D4 001:627.964 - 0.032ms returns 0x20008038
T82D4 001:627.985 JLINK_ReadReg(R14)
T82D4 001:628.005 - 0.031ms returns 0xFFFFFFFF
T82D4 001:628.027 JLINK_ReadReg(R15 (PC))
T82D4 001:628.049 - 0.031ms returns 0x080001C4
T82D4 001:628.071 JLINK_ReadReg(XPSR)
T82D4 001:628.092 - 0.029ms returns 0x01000000
T82D4 001:628.112 JLINK_ReadReg(MSP)
T82D4 001:628.132 - 0.030ms returns 0x20008038
T82D4 001:628.152 JLINK_ReadReg(PSP)
T82D4 001:628.172 - 0.031ms returns 0x20001000
T82D4 001:628.193 JLINK_ReadReg(CFBP)
T82D4 001:628.213 - 0.032ms returns 0x00000000
T82D4 001:628.238 JLINK_ReadReg(FPSCR)
T82D4 001:633.750 - 5.536ms returns 0x00000000
T82D4 001:633.787 JLINK_ReadReg(FPS0)
T82D4 001:633.809 - 0.031ms returns 0x00000000
T82D4 001:633.829 JLINK_ReadReg(FPS1)
T82D4 001:633.848 - 0.028ms returns 0x3FF00000
T82D4 001:633.868 JLINK_ReadReg(FPS2)
T82D4 001:633.886 - 0.027ms returns 0x48164000
T82D4 001:633.906 JLINK_ReadReg(FPS3)
T82D4 001:633.925 - 0.029ms returns 0x48020358
T82D4 001:633.944 JLINK_ReadReg(FPS4)
T82D4 001:633.964 - 0.029ms returns 0x001E0018
T82D4 001:633.983 JLINK_ReadReg(FPS5)
T82D4 001:634.003 - 0.028ms returns 0x58C90680
T82D4 001:634.022 JLINK_ReadReg(FPS6)
T82D4 001:634.040 - 0.027ms returns 0x10867400
T82D4 001:634.060 JLINK_ReadReg(FPS7)
T82D4 001:634.080 - 0.029ms returns 0x62464681
T82D4 001:634.099 JLINK_ReadReg(FPS8)
T82D4 001:634.119 - 0.029ms returns 0x183008E3
T82D4 001:634.138 JLINK_ReadReg(FPS9)
T82D4 001:634.157 - 0.028ms returns 0x4CE0F726
T82D4 001:634.176 JLINK_ReadReg(FPS10)
T82D4 001:634.195 - 0.027ms returns 0x490D9850
T82D4 001:634.215 JLINK_ReadReg(FPS11)
T82D4 001:634.234 - 0.029ms returns 0x0301B320
T82D4 001:634.254 JLINK_ReadReg(FPS12)
T82D4 001:634.273 - 0.028ms returns 0x8C6080C8
T82D4 001:634.292 JLINK_ReadReg(FPS13)
T82D4 001:634.311 - 0.028ms returns 0x5D944171
T82D4 001:634.331 JLINK_ReadReg(FPS14)
T82D4 001:634.349 - 0.027ms returns 0x4501C880
T82D4 001:634.369 JLINK_ReadReg(FPS15)
T82D4 001:634.388 - 0.029ms returns 0x185C0000
T82D4 001:634.407 JLINK_ReadReg(FPS16)
T82D4 001:634.427 - 0.034ms returns 0x80A80000
T82D4 001:634.480 JLINK_ReadReg(FPS17)
T82D4 001:634.501 - 0.029ms returns 0x58092430
T82D4 001:634.520 JLINK_ReadReg(FPS18)
T82D4 001:634.539 - 0.028ms returns 0xEF80024E
T82D4 001:634.557 JLINK_ReadReg(FPS19)
T82D4 001:634.576 - 0.028ms returns 0x4820B903
T82D4 001:634.595 JLINK_ReadReg(FPS20)
T82D4 001:634.616 - 0.033ms returns 0x02052200
T82D4 001:634.639 JLINK_ReadReg(FPS21)
T82D4 001:634.657 - 0.027ms returns 0x2804C838
T82D4 001:634.676 JLINK_ReadReg(FPS22)
T82D4 001:634.695 - 0.028ms returns 0x58016426
T82D4 001:634.713 JLINK_ReadReg(FPS23)
T82D4 001:634.733 - 0.028ms returns 0x89303CF8
T82D4 001:634.751 JLINK_ReadReg(FPS24)
T82D4 001:634.769 - 0.027ms returns 0x8604020A
T82D4 001:634.789 JLINK_ReadReg(FPS25)
T82D4 001:634.807 - 0.027ms returns 0xC0190403
T82D4 001:634.826 JLINK_ReadReg(FPS26)
T82D4 001:634.845 - 0.027ms returns 0x202A04C0
T82D4 001:634.863 JLINK_ReadReg(FPS27)
T82D4 001:634.883 - 0.028ms returns 0x150C25A2
T82D4 001:634.901 JLINK_ReadReg(FPS28)
T82D4 001:634.919 - 0.027ms returns 0x614840F3
T82D4 001:634.940 JLINK_ReadReg(FPS29)
T82D4 001:634.958 - 0.027ms returns 0x01487A60
T82D4 001:634.977 JLINK_ReadReg(FPS30)
T82D4 001:634.996 - 0.027ms returns 0x0247A962
T82D4 001:635.014 JLINK_ReadReg(FPS31)
T82D4 001:635.034 - 0.028ms returns 0x00CC00A3
T82D4 001:690.981 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:691.053   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 001:691.994    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 001:692.117    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:692.143   Data:  00 00
T82D4 001:692.170 - 1.198ms returns 2 (0x2)
T82D4 001:692.245 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:692.271    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:692.321   Data:  00 00
T82D4 001:692.360 - 0.126ms returns 2 (0x2)
T82D4 001:692.410 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:692.440    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:692.470   Data:  00 00
T82D4 001:692.496 - 0.094ms returns 2 (0x2)
T82D4 001:698.997 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:699.058    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:699.086   Data:  00 00 00 00
T82D4 001:699.113 - 0.125ms returns 4 (0x4)
T82D4 001:699.146 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:699.170    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:699.196   Data:  00 00 00 00
T82D4 001:699.222 - 0.085ms returns 4 (0x4)
T82D4 001:699.256 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:699.278    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:699.306   Data:  00 00 00 00
T82D4 001:699.333 - 0.085ms returns 4 (0x4)
T82D4 001:713.479 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:713.544    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:713.573   Data:  00 00 00 00
T82D4 001:713.599 - 0.129ms returns 4 (0x4)
T82D4 001:713.631 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:713.655    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:713.681   Data:  00 00 00 00
T82D4 001:713.708 - 0.086ms returns 4 (0x4)
T82D4 001:713.741 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:713.762    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:713.790   Data:  00 00 00 00
T82D4 001:713.815 - 0.082ms returns 4 (0x4)
T82D4 001:728.783 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:728.895    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:728.939   Data:  00 00 00 00
T82D4 001:728.965 - 0.191ms returns 4 (0x4)
T82D4 001:729.002 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:729.025    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:729.051   Data:  00 00 00 00
T82D4 001:729.093 - 0.099ms returns 4 (0x4)
T82D4 001:729.126 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:729.148    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:729.174   Data:  00 00 00 00
T82D4 001:729.221 - 0.104ms returns 4 (0x4)
T82D4 001:736.488 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:736.536    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:736.564   Data:  00 00 00 00
T82D4 001:736.589 - 0.110ms returns 4 (0x4)
T82D4 001:736.623 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:736.648    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:736.674   Data:  00 00 00 00
T82D4 001:736.703 - 0.092ms returns 4 (0x4)
T82D4 001:736.737 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:736.757    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:736.785   Data:  00 00 00 00
T82D4 001:736.810 - 0.082ms returns 4 (0x4)
T82D4 001:746.332 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:746.395    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:746.422   Data:  00 00 00 00
T82D4 001:746.447 - 0.124ms returns 4 (0x4)
T82D4 001:746.479 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:746.501    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:746.528   Data:  00 00 00 00
T82D4 001:746.553 - 0.082ms returns 4 (0x4)
T82D4 001:746.586 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:746.609    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:746.635   Data:  00 00 00 00
T82D4 001:746.659 - 0.082ms returns 4 (0x4)
T82D4 001:799.969 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:800.044   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 001:800.940    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 001:801.077    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:801.103   Data:  00 00 00 00
T82D4 001:801.129 - 1.189ms returns 4 (0x4)
T82D4 001:801.315 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:801.341    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:801.367   Data:  00 00 00 00
T82D4 001:801.393 - 0.087ms returns 4 (0x4)
T82D4 001:801.429 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:801.450    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:801.478   Data:  00 00 00 00
T82D4 001:801.518 - 0.096ms returns 4 (0x4)
T82D4 001:812.576 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:812.639    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:812.665   Data:  00 00 00 00
T82D4 001:812.690 - 0.123ms returns 4 (0x4)
T82D4 001:812.726 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:812.748    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:812.773   Data:  00 00 00 00
T82D4 001:812.799 - 0.082ms returns 4 (0x4)
T82D4 001:812.832 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:812.853    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:812.880   Data:  00 00 00 00
T82D4 001:812.905 - 0.081ms returns 4 (0x4)
T82D4 001:822.480 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:822.542   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 001:823.399    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 001:823.526    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:823.551   Data:  00 00 00 00
T82D4 001:823.578 - 1.106ms returns 4 (0x4)
T82D4 001:823.618 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:823.642    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:823.668   Data:  00 00 00 00
T82D4 001:823.694 - 0.085ms returns 4 (0x4)
T82D4 001:823.727 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:823.748    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:823.775   Data:  00 00 00 00
T82D4 001:823.801 - 0.082ms returns 4 (0x4)
T82D4 001:834.119 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:834.185   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 001:835.056    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 001:835.174    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:835.201   Data:  00 00 00 00
T82D4 001:835.227 - 1.118ms returns 4 (0x4)
T82D4 001:835.272 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:835.297    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:835.323   Data:  00 00 00 00
T82D4 001:835.348 - 0.084ms returns 4 (0x4)
T82D4 001:835.382 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:835.408    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:835.438   Data:  00 00 00 00
T82D4 001:835.463 - 0.089ms returns 4 (0x4)
T82D4 001:845.344 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:845.401    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:845.430   Data:  00 00 00 00
T82D4 001:845.457 - 0.122ms returns 4 (0x4)
T82D4 001:845.493 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:845.516    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:845.544   Data:  00 00 00 00
T82D4 001:845.570 - 0.085ms returns 4 (0x4)
T82D4 001:845.606 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:845.630    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:845.657   Data:  00 00 00 00
T82D4 001:845.685 - 0.087ms returns 4 (0x4)
T82D4 001:856.166 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:856.229   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 001:857.109    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 001:857.151    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:857.176   Data:  00
T82D4 001:857.202 - 1.045ms returns 1 (0x1)
T82D4 001:857.246 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:857.270    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:857.297   Data:  00
T82D4 001:857.322 - 0.083ms returns 1 (0x1)
T82D4 001:857.355 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:857.377    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:857.403   Data:  00
T82D4 001:857.429 - 0.082ms returns 1 (0x1)
T82D4 001:869.512 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 001:869.591   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 001:870.392    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 001:870.434    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 001:870.462   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 001:870.487 - 0.984ms returns 32 (0x20)
T82D4 001:946.679 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:946.743    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:946.770   Data:  00 00 00 00
T82D4 001:946.795 - 0.125ms returns 4 (0x4)
T82D4 001:946.827 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:946.851    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:946.876   Data:  00 00 00 00
T82D4 001:946.901 - 0.082ms returns 4 (0x4)
T82D4 001:946.934 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:946.955    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:946.980   Data:  00 00 00 00
T82D4 001:947.006 - 0.080ms returns 4 (0x4)
T82D4 001:958.640 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:958.697    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:958.724   Data:  00 00 00 00
T82D4 001:958.749 - 0.116ms returns 4 (0x4)
T82D4 001:958.781 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:958.856    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:958.888   Data:  00 00 00 00
T82D4 001:958.913 - 0.139ms returns 4 (0x4)
T82D4 001:958.947 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:958.970    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:958.997   Data:  00 00 00 00
T82D4 001:959.023 - 0.083ms returns 4 (0x4)
T82D4 001:970.779 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:970.840    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:970.867   Data:  00 00 00 00
T82D4 001:970.893 - 0.122ms returns 4 (0x4)
T82D4 001:970.925 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:970.947    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:970.972   Data:  00 00 00 00
T82D4 001:970.996 - 0.079ms returns 4 (0x4)
T82D4 001:971.027 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:971.048    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:971.073   Data:  00 00 00 00
T82D4 001:971.099 - 0.080ms returns 4 (0x4)
T82D4 002:032.444 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:032.507    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:032.534   Data:  00
T82D4 002:032.559 - 0.124ms returns 1 (0x1)
T82D4 002:032.596 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:032.618    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:032.645   Data:  00
T82D4 002:032.670 - 0.082ms returns 1 (0x1)
T82D4 002:032.701 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:032.724    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:032.750   Data:  00
T82D4 002:032.775 - 0.083ms returns 1 (0x1)
T82D4 002:123.365 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:123.421    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:123.448   Data:  00 00 00 00
T82D4 002:123.473 - 0.117ms returns 4 (0x4)
T82D4 002:123.505 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:123.526    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:123.552   Data:  00 00 00 00
T82D4 002:123.578 - 0.080ms returns 4 (0x4)
T82D4 002:123.609 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:123.631    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:123.656   Data:  00 00 00 00
T82D4 002:123.680 - 0.081ms returns 4 (0x4)
T82D4 002:136.500 JLINK_ReadMemEx(0x08018DB8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:136.558   CPU_ReadMem(64 bytes @ 0x08018D80)
T82D4 002:137.596    -- Updating C cache (64 bytes @ 0x08018D80)
T82D4 002:137.634    -- Read from C cache (8 bytes @ 0x08018DB8)
T82D4 002:137.660   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:137.684 - 1.192ms returns 8 (0x8)
T82D4 002:137.720 JLINK_ReadMemEx(0x08018DB8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:137.742    -- Read from C cache (8 bytes @ 0x08018DB8)
T82D4 002:137.768   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:137.829 - 0.117ms returns 8 (0x8)
T82D4 002:137.861 JLINK_ReadMemEx(0x08018DB8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:137.882    -- Read from C cache (8 bytes @ 0x08018DB8)
T82D4 002:137.908   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:137.932 - 0.080ms returns 8 (0x8)
T82D4 002:164.039 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:164.101   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:165.087    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:165.127    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:165.153   Data:  00 00 00 00
T82D4 002:165.178 - 1.147ms returns 4 (0x4)
T82D4 002:165.215 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:165.238    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:165.264   Data:  00 00 00 00
T82D4 002:165.289 - 0.083ms returns 4 (0x4)
T82D4 002:165.323 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:165.343    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:165.369   Data:  00 00 00 00
T82D4 002:165.393 - 0.078ms returns 4 (0x4)
T82D4 002:236.273 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:236.337    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:236.365   Data:  00 00 00 00
T82D4 002:236.396 - 0.131ms returns 4 (0x4)
T82D4 002:236.424 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:236.448    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:236.473   Data:  00 00 00 00
T82D4 002:236.500 - 0.084ms returns 4 (0x4)
T82D4 002:236.530 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:236.551    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:236.578   Data:  00 00 00 00
T82D4 002:236.604 - 0.082ms returns 4 (0x4)
T8598 002:279.050 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T8598 002:279.123    -- Read from C cache (2 bytes @ 0x080001C4)
T8598 002:279.151   Data:  06 48
T8598 002:279.177 - 0.135ms returns 2 (0x2)
T8598 002:279.197 JLINK_HasError()
T8598 002:279.219 JLINK_SetBPEx(Addr = 0x080165C0, Type = 0xFFFFFFF2)
T8598 002:279.241 - 0.030ms returns 0x00000001
T8598 002:279.261 JLINK_HasError()
T8598 002:279.283 JLINK_HasError()
T8598 002:279.305 JLINK_Go()
T8598 002:279.683   CPU_WriteMem(4 bytes @ 0xE0002000)
T8598 002:280.091   CPU_ReadMem(4 bytes @ 0xE0001000)
T8598 002:280.405   CPU_WriteMem(4 bytes @ 0xE0002008)
T8598 002:280.438   CPU_WriteMem(4 bytes @ 0xE000200C)
T8598 002:280.465   CPU_WriteMem(4 bytes @ 0xE0002010)
T8598 002:280.491   CPU_WriteMem(4 bytes @ 0xE0002014)
T8598 002:280.516   CPU_WriteMem(4 bytes @ 0xE0002018)
T8598 002:280.543   CPU_WriteMem(4 bytes @ 0xE000201C)
T8598 002:282.026   CPU_WriteMem(4 bytes @ 0xE0001004)
T8598 002:282.836 - 3.548ms
T8598 002:383.517 JLINK_HasError()
T8598 002:383.580 JLINK_IsHalted()
T8598 002:384.056 - 0.496ms returns FALSE
T8598 002:484.745 JLINK_HasError()
T8598 002:484.818 JLINK_IsHalted()
T8598 002:488.226 - 3.441ms returns TRUE
T8598 002:488.273 JLINK_HasError()
T8598 002:488.294 JLINK_Halt()
T8598 002:488.313 - 0.027ms returns 0x00
T8598 002:488.333 JLINK_IsHalted()
T8598 002:488.351 - 0.027ms returns TRUE
T8598 002:488.370 JLINK_IsHalted()
T8598 002:488.389 - 0.028ms returns TRUE
T8598 002:488.408 JLINK_IsHalted()
T8598 002:488.427 - 0.028ms returns TRUE
T8598 002:488.447 JLINK_HasError()
T8598 002:488.467 JLINK_ReadReg(R15 (PC))
T8598 002:488.490 - 0.032ms returns 0x080165C0
T8598 002:488.509 JLINK_ReadReg(XPSR)
T8598 002:488.529 - 0.029ms returns 0x61000000
T8598 002:488.552 JLINK_HasError()
T8598 002:488.572 JLINK_ClrBPEx(BPHandle = 0x00000001)
T8598 002:488.592 - 0.030ms returns 0x00
T8598 002:488.613 JLINK_HasError()
T8598 002:488.632 JLINK_HasError()
T8598 002:488.652 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T8598 002:488.678   CPU_ReadMem(4 bytes @ 0xE000ED30)
T8598 002:489.077   Data:  02 00 00 00
T8598 002:489.117 - 0.475ms returns 1 (0x1)
T8598 002:489.139 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T8598 002:489.164   CPU_ReadMem(4 bytes @ 0xE0001028)
T8598 002:489.485   Data:  00 00 00 00
T8598 002:489.541   Debug reg: DWT_FUNC[0]
T8598 002:489.570 - 0.440ms returns 1 (0x1)
T8598 002:489.591 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T8598 002:489.616   CPU_ReadMem(4 bytes @ 0xE0001038)
T8598 002:489.942   Data:  00 02 00 00
T8598 002:489.979   Debug reg: DWT_FUNC[1]
T8598 002:490.022 - 0.461ms returns 1 (0x1)
T8598 002:490.064 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T8598 002:490.087   CPU_ReadMem(4 bytes @ 0xE0001048)
T8598 002:490.399   Data:  00 00 00 00
T8598 002:490.432   Debug reg: DWT_FUNC[2]
T8598 002:490.460 - 0.404ms returns 1 (0x1)
T8598 002:490.480 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T8598 002:490.503   CPU_ReadMem(4 bytes @ 0xE0001058)
T8598 002:490.837   Data:  00 00 00 00
T8598 002:490.870   Debug reg: DWT_FUNC[3]
T8598 002:490.897 - 0.425ms returns 1 (0x1)
T8598 002:491.012 JLINK_HasError()
T8598 002:491.036 JLINK_ReadReg(R0)
T8598 002:491.057 - 0.031ms returns 0x080165C1
T8598 002:491.077 JLINK_ReadReg(R1)
T8598 002:491.096 - 0.028ms returns 0x20022A08
T8598 002:491.116 JLINK_ReadReg(R2)
T8598 002:491.136 - 0.029ms returns 0x00000000
T8598 002:491.155 JLINK_ReadReg(R3)
T8598 002:491.176 - 0.029ms returns 0x08012CE9
T8598 002:491.195 JLINK_ReadReg(R4)
T8598 002:491.215 - 0.029ms returns 0x08019328
T8598 002:491.235 JLINK_ReadReg(R5)
T8598 002:491.254 - 0.027ms returns 0x08019328
T8598 002:491.274 JLINK_ReadReg(R6)
T8598 002:491.293 - 0.028ms returns 0x08016688
T8598 002:491.313 JLINK_ReadReg(R7)
T8598 002:491.333 - 0.029ms returns 0x00000000
T8598 002:491.352 JLINK_ReadReg(R8)
T8598 002:491.372 - 0.029ms returns 0x200229BC
T8598 002:491.392 JLINK_ReadReg(R9)
T8598 002:491.411 - 0.028ms returns 0x20000348
T8598 002:491.431 JLINK_ReadReg(R10)
T8598 002:491.451 - 0.029ms returns 0x0801587D
T8598 002:491.470 JLINK_ReadReg(R11)
T8598 002:491.490 - 0.029ms returns 0x00000000
T8598 002:491.510 JLINK_ReadReg(R12)
T8598 002:491.529 - 0.029ms returns 0x00000000
T8598 002:491.549 JLINK_ReadReg(R13 (SP))
T8598 002:491.568 - 0.028ms returns 0x20022A08
T8598 002:491.588 JLINK_ReadReg(R14)
T8598 002:491.608 - 0.028ms returns 0x08004E6D
T8598 002:491.633 JLINK_ReadReg(R15 (PC))
T8598 002:491.656 - 0.033ms returns 0x080165C0
T8598 002:491.676 JLINK_ReadReg(XPSR)
T8598 002:491.696 - 0.030ms returns 0x61000000
T8598 002:491.789 JLINK_ReadReg(MSP)
T8598 002:491.813 - 0.033ms returns 0x20022A08
T8598 002:491.833 JLINK_ReadReg(PSP)
T8598 002:491.852 - 0.029ms returns 0x20001000
T8598 002:491.873 JLINK_ReadReg(CFBP)
T8598 002:491.891 - 0.028ms returns 0x00000000
T8598 002:491.912 JLINK_ReadReg(FPSCR)
T8598 002:497.342 - 5.466ms returns 0x00000000
T8598 002:497.393 JLINK_ReadReg(FPS0)
T8598 002:497.416 - 0.033ms returns 0x00000000
T8598 002:497.436 JLINK_ReadReg(FPS1)
T8598 002:497.456 - 0.030ms returns 0x3FF00000
T8598 002:497.476 JLINK_ReadReg(FPS2)
T8598 002:497.495 - 0.028ms returns 0x48164000
T8598 002:497.515 JLINK_ReadReg(FPS3)
T8598 002:497.534 - 0.028ms returns 0x48020358
T8598 002:497.553 JLINK_ReadReg(FPS4)
T8598 002:497.573 - 0.029ms returns 0x001E0018
T8598 002:497.593 JLINK_ReadReg(FPS5)
T8598 002:497.612 - 0.029ms returns 0x58C90680
T8598 002:497.632 JLINK_ReadReg(FPS6)
T8598 002:497.650 - 0.027ms returns 0x10867400
T8598 002:497.670 JLINK_ReadReg(FPS7)
T8598 002:497.689 - 0.036ms returns 0x62464681
T8598 002:497.717 JLINK_ReadReg(FPS8)
T8598 002:497.738 - 0.030ms returns 0x183008E3
T8598 002:497.761 JLINK_ReadReg(FPS9)
T8598 002:497.791 - 0.043ms returns 0x4CE0F726
T8598 002:497.818 JLINK_ReadReg(FPS10)
T8598 002:497.844 - 0.040ms returns 0x490D9850
T8598 002:497.872 JLINK_ReadReg(FPS11)
T8598 002:497.899 - 0.039ms returns 0x0301B320
T8598 002:497.925 JLINK_ReadReg(FPS12)
T8598 002:497.956 - 0.046ms returns 0x8C6080C8
T8598 002:497.986 JLINK_ReadReg(FPS13)
T8598 002:498.013 - 0.040ms returns 0x5D944171
T8598 002:498.041 JLINK_ReadReg(FPS14)
T8598 002:498.067 - 0.040ms returns 0x4501C880
T8598 002:498.095 JLINK_ReadReg(FPS15)
T8598 002:498.120 - 0.038ms returns 0x185C0000
T8598 002:498.147 JLINK_ReadReg(FPS16)
T8598 002:498.175 - 0.038ms returns 0x80A80000
T8598 002:498.196 JLINK_ReadReg(FPS17)
T8598 002:498.216 - 0.029ms returns 0x58092430
T8598 002:498.235 JLINK_ReadReg(FPS18)
T8598 002:498.253 - 0.028ms returns 0xEF80024E
T8598 002:498.274 JLINK_ReadReg(FPS19)
T8598 002:498.293 - 0.028ms returns 0x4820B903
T8598 002:498.312 JLINK_ReadReg(FPS20)
T8598 002:498.332 - 0.029ms returns 0x02052200
T8598 002:498.351 JLINK_ReadReg(FPS21)
T8598 002:498.370 - 0.029ms returns 0x2804C838
T8598 002:498.390 JLINK_ReadReg(FPS22)
T8598 002:498.408 - 0.028ms returns 0x58016426
T8598 002:498.429 JLINK_ReadReg(FPS23)
T8598 002:498.448 - 0.028ms returns 0x89303CF8
T8598 002:498.466 JLINK_ReadReg(FPS24)
T8598 002:498.486 - 0.029ms returns 0x8604020A
T8598 002:498.506 JLINK_ReadReg(FPS25)
T8598 002:498.526 - 0.029ms returns 0xC0190403
T8598 002:498.545 JLINK_ReadReg(FPS26)
T8598 002:498.563 - 0.028ms returns 0x202A04C0
T8598 002:498.585 JLINK_ReadReg(FPS27)
T8598 002:498.604 - 0.028ms returns 0x150C25A2
T8598 002:498.623 JLINK_ReadReg(FPS28)
T8598 002:498.643 - 0.028ms returns 0x614840F3
T8598 002:498.662 JLINK_ReadReg(FPS29)
T8598 002:498.682 - 0.035ms returns 0x01487A60
T8598 002:498.708 JLINK_ReadReg(FPS30)
T8598 002:498.728 - 0.029ms returns 0x0247A962
T8598 002:498.748 JLINK_ReadReg(FPS31)
T8598 002:498.766 - 0.027ms returns 0x00CC00A3
T82D4 002:499.007 JLINK_ReadMemEx(0x20022A04, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:499.051   CPU_ReadMem(64 bytes @ 0x20022A00)
T82D4 002:499.890    -- Updating C cache (64 bytes @ 0x20022A00)
T82D4 002:499.930    -- Read from C cache (4 bytes @ 0x20022A04)
T82D4 002:499.959   Data:  00 00 00 00
T82D4 002:499.985 - 0.987ms returns 4 (0x4)
T82D4 002:500.066 JLINK_ReadMemEx(0x200229CC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:500.094   CPU_ReadMem(64 bytes @ 0x200229C0)
T82D4 002:500.895    -- Updating C cache (64 bytes @ 0x200229C0)
T82D4 002:500.955    -- Read from C cache (4 bytes @ 0x200229CC)
T82D4 002:500.991   Data:  00 00 00 00
T82D4 002:501.029 - 0.975ms returns 4 (0x4)
T82D4 002:501.120 JLINK_ReadMemEx(0x200229D0, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:501.161    -- Read from C cache (32 bytes @ 0x200229D0)
T82D4 002:501.204   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:501.240 - 0.133ms returns 32 (0x20)
T82D4 002:502.427 JLINK_ReadMemEx(0x20022A04, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:502.469    -- Read from C cache (4 bytes @ 0x20022A04)
T82D4 002:502.498   Data:  00 00 00 00
T82D4 002:502.524 - 0.105ms returns 4 (0x4)
T82D4 002:502.544 JLINK_ReadMemEx(0x200229CC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:502.565    -- Read from C cache (4 bytes @ 0x200229CC)
T82D4 002:502.591   Data:  00 00 00 00
T82D4 002:502.618 - 0.083ms returns 4 (0x4)
T82D4 002:502.669 JLINK_ReadMemEx(0x200229D0, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:502.692    -- Read from C cache (32 bytes @ 0x200229D0)
T82D4 002:502.720   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:502.745 - 0.084ms returns 32 (0x20)
T82D4 002:502.810 JLINK_HasError()
T82D4 002:502.836 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:502.877   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:503.302   Data:  0E 37 23 00
T82D4 002:503.357   Debug reg: DWT_CYCCNT
T82D4 002:503.419 - 0.592ms returns 1 (0x1)
T82D4 002:506.303 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:506.381   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 002:507.198    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 002:507.243    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 002:507.270   Data:  00 00
T82D4 002:507.296 - 1.002ms returns 2 (0x2)
T82D4 002:507.330 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:507.355    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:507.381   Data:  00 00 00 00
T82D4 002:507.408 - 0.087ms returns 4 (0x4)
T82D4 002:507.993 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:508.027    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:508.055   Data:  00 00 00 00
T82D4 002:508.080 - 0.096ms returns 4 (0x4)
T82D4 002:508.128 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:508.151    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:508.178   Data:  00 00 00 00
T82D4 002:508.204 - 0.084ms returns 4 (0x4)
T82D4 002:508.228 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:508.250    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:508.276   Data:  00 00 00 00
T82D4 002:508.301 - 0.082ms returns 4 (0x4)
T82D4 002:508.325 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:508.346    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:508.372   Data:  00 00 00 00
T82D4 002:508.399 - 0.082ms returns 4 (0x4)
T82D4 002:508.445 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:508.472   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:509.331    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:509.399    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:509.441   Data:  00 00 00 00
T82D4 002:509.469 - 1.033ms returns 4 (0x4)
T82D4 002:509.501 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:509.524    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:509.552   Data:  00 00 00 00
T82D4 002:509.578 - 0.085ms returns 4 (0x4)
T82D4 002:509.601 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:509.625   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:510.412    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:510.450    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:510.476   Data:  00 00 00 00
T82D4 002:510.526 - 0.934ms returns 4 (0x4)
T82D4 002:510.570 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:510.595   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:511.411    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:511.445    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 002:511.472   Data:  00 00 00 00
T82D4 002:511.498 - 0.936ms returns 4 (0x4)
T82D4 002:511.524 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:511.547    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:511.581   Data:  00 00 00 00
T82D4 002:511.608 - 0.093ms returns 4 (0x4)
T82D4 002:511.632 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:511.653   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:512.535    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:512.577    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:512.604   Data:  00
T82D4 002:512.631 - 1.008ms returns 1 (0x1)
T82D4 002:512.675 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:512.701   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 002:513.534    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 002:513.567    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 002:513.594   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:513.622 - 0.955ms returns 32 (0x20)
T82D4 002:526.462 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:526.593    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:526.644   Data:  00 00 00 00
T82D4 002:526.671 - 0.218ms returns 4 (0x4)
T82D4 002:526.713 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:526.736    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:526.833   Data:  00 00 00 00
T82D4 002:526.862 - 0.160ms returns 4 (0x4)
T82D4 002:526.903 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:526.927    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:526.956   Data:  00 00 00 00
T82D4 002:526.983 - 0.088ms returns 4 (0x4)
T82D4 002:527.048 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:527.072    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:527.099   Data:  00
T82D4 002:527.125 - 0.087ms returns 1 (0x1)
T82D4 002:527.257 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:527.280    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:527.312   Data:  00 00 00 00
T82D4 002:527.340 - 0.092ms returns 4 (0x4)
T82D4 002:527.367 JLINK_ReadMemEx(0x08018DB8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:527.417   CPU_ReadMem(64 bytes @ 0x08018D80)
T82D4 002:528.401    -- Updating C cache (64 bytes @ 0x08018D80)
T82D4 002:528.445    -- Read from C cache (8 bytes @ 0x08018DB8)
T82D4 002:528.472   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:528.499 - 1.140ms returns 8 (0x8)
T82D4 002:528.732 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:528.762   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:529.559    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:529.619    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:529.645   Data:  00 00 00 00
T82D4 002:529.673 - 0.949ms returns 4 (0x4)
