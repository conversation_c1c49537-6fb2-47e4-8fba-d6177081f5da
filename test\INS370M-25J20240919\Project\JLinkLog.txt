T82D4 000:012.568   SEGGER J-Link V7.22b Log File
T82D4 000:012.832   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.871   Logging started @ 2025-06-11 06:40
T82D4 000:012.895 - 12.906ms
T82D4 000:012.924 JLINK_SetWarnOutHandler(...)
T82D4 000:012.950 - 0.036ms
T82D4 000:012.974 JLINK_OpenEx(...)
T82D4 000:014.484   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.859   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:015.018   Decompressing FW timestamp took 110 us
T82D4 000:020.980   Hardware: V9.40
T82D4 000:021.026   S/N: 59406895
T82D4 000:021.058   OEM: SEGGER
T82D4 000:021.088   Feature(s): R<PERSON>, GDB, FlashDL, <PERSON>B<PERSON>, J<PERSON><PERSON>
T82D4 000:021.877   TELNET listener socket opened on port 19021
T82D4 000:022.046   WEBSRV Starting webserver
T82D4 000:022.201   WEBSRV Webserver running on local port 19080
T82D4 000:022.243 - 9.279ms returns "O.K."
T82D4 000:022.273 JLINK_GetEmuCaps()
T82D4 000:022.293 - 0.030ms returns 0xB9FF7BBF
T82D4 000:022.317 JLINK_TIF_GetAvailable(...)
T82D4 000:022.457 - 0.157ms
T82D4 000:022.488 JLINK_SetErrorOutHandler(...)
T82D4 000:022.509 - 0.029ms
T82D4 000:022.545 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:033.002   Device "GD32F450II" selected.
T82D4 000:033.654 - 11.126ms returns 0x00
T82D4 000:033.701 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:034.450   Device "GD32F450II" selected.
T82D4 000:035.009 - 1.302ms returns 0x00
T82D4 000:035.044 JLINK_GetHardwareVersion()
T82D4 000:035.065 - 0.030ms returns 94000
T82D4 000:035.085 JLINK_GetDLLVersion()
T82D4 000:035.103 - 0.027ms returns 72202
T82D4 000:035.122 JLINK_GetOEMString(...)
T82D4 000:035.143 JLINK_GetFirmwareString(...)
T82D4 000:035.168 - 0.033ms
T82D4 000:035.200 JLINK_GetDLLVersion()
T82D4 000:035.221 - 0.030ms returns 72202
T82D4 000:035.240 JLINK_GetCompileDateTime()
T82D4 000:035.260 - 0.029ms
T82D4 000:035.285 JLINK_GetFirmwareString(...)
T82D4 000:035.306 - 0.029ms
T82D4 000:035.328 JLINK_GetHardwareVersion()
T82D4 000:035.348 - 0.029ms returns 94000
T82D4 000:035.372 JLINK_GetSN()
T82D4 000:035.393 - 0.030ms returns 59406895
T82D4 000:035.416 JLINK_GetOEMString(...)
T82D4 000:035.451 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.863 - 0.446ms returns 0x00
T82D4 000:035.910 JLINK_HasError()
T82D4 000:035.944 JLINK_SetSpeed(2000)
T82D4 000:036.066 - 0.161ms
T82D4 000:036.140 JLINK_GetId()
T82D4 000:037.130   Found SW-DP with ID 0x2BA01477
T82D4 000:039.670   DPIDR: 0x2BA01477
T82D4 000:039.734   Scanning AP map to find all available APs
T82D4 000:040.222   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:040.260   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:040.292   Iterating through AP map to find AHB-AP to use
T82D4 000:041.086   AP[0]: Core found
T82D4 000:041.131   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:041.587   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:041.625   Found Cortex-M4 r0p1, Little endian.
T82D4 000:142.628   -- Max. mem block: 0x00010E60
T82D4 000:143.204   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:143.910   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:144.607   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:144.969   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:145.004   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:145.314   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:145.641   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:145.947   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:146.311   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:146.684   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:147.096   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:147.527   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:147.894   CoreSight components:
T82D4 000:147.937   ROMTbl[0] @ E00FF000
T82D4 000:147.993   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:148.764   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:149.397   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:149.505   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:150.101   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:150.134   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:150.721   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:150.844   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:151.432   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:151.512   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:152.099   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:152.218   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:152.761   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:152.985 - 116.866ms returns 0x2BA01477
T82D4 000:153.025 JLINK_GetDLLVersion()
T82D4 000:153.043 - 0.027ms returns 72202
T82D4 000:153.064 JLINK_CORE_GetFound()
T82D4 000:153.083 - 0.027ms returns 0xE0000FF
T82D4 000:153.102 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:153.122   Value=0xE00FF000
T82D4 000:153.148 - 0.054ms returns 0
T82D4 000:153.243 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:153.271   Value=0xE00FF000
T82D4 000:153.297 - 0.064ms returns 0
T82D4 000:153.318 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:153.336   Value=0x00000000
T82D4 000:153.362 - 0.054ms returns 0
T82D4 000:153.391 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:153.428   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:153.938   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:153.970 - 0.587ms returns 16 (0x10)
T82D4 000:153.989 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:154.009   Value=0x00000000
T82D4 000:154.034 - 0.053ms returns 0
T82D4 000:154.052 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:154.071   Value=0x********
T82D4 000:154.097 - 0.053ms returns 0
T82D4 000:154.116 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:154.134   Value=0x********
T82D4 000:154.159 - 0.053ms returns 0
T82D4 000:154.179 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:154.197   Value=0xE0001000
T82D4 000:154.223 - 0.052ms returns 0
T82D4 000:154.241 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:154.258   Value=0xE0002000
T82D4 000:154.285 - 0.053ms returns 0
T82D4 000:154.304 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:154.321   Value=0xE000E000
T82D4 000:154.348 - 0.052ms returns 0
T82D4 000:154.366 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:154.384   Value=0xE000EDF0
T82D4 000:154.410 - 0.053ms returns 0
T82D4 000:154.428 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:154.447   Value=0x00000001
T82D4 000:154.472 - 0.052ms returns 0
T82D4 000:154.490 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:154.513   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:154.818   Data:  41 C2 0F 41
T82D4 000:154.852   Debug reg: CPUID
T82D4 000:154.877 - 0.394ms returns 1 (0x1)
T82D4 000:154.896 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:154.916   Value=0x00000000
T82D4 000:154.941 - 0.055ms returns 0
T82D4 000:154.961 JLINK_HasError()
T82D4 000:154.980 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:154.999 - 0.027ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:155.018 JLINK_Reset()
T82D4 000:155.045   CPU is running
T82D4 000:155.076   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:155.457   CPU is running
T82D4 000:155.489   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:155.835   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:156.215   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:156.249   CPU is running
T82D4 000:156.275   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:209.359   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.746   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:210.084   CPU is running
T82D4 000:210.115   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:210.459   CPU is running
T82D4 000:210.579   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:216.048   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:219.819   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:220.250   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:220.621   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:221.006 - 66.008ms
T82D4 000:221.128 JLINK_HasError()
T82D4 000:221.160 JLINK_ReadReg(R15 (PC))
T82D4 000:221.190 - 0.039ms returns 0x080001C4
T82D4 000:221.209 JLINK_ReadReg(XPSR)
T82D4 000:221.226 - 0.026ms returns 0x01000000
T82D4 000:221.246 JLINK_Halt()
T82D4 000:221.263 - 0.024ms returns 0x00
T82D4 000:221.280 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:221.304   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:221.609   Data:  03 00 03 00
T82D4 000:221.644   Debug reg: DHCSR
T82D4 000:221.668 - 0.396ms returns 1 (0x1)
T82D4 000:221.687 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:221.709   Debug reg: DHCSR
T82D4 000:221.933   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:222.333 - 0.659ms returns 0 (0x00000000)
T82D4 000:222.357 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:222.375   Debug reg: DEMCR
T82D4 000:222.406   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:222.756 - 0.411ms returns 0 (0x00000000)
T82D4 000:222.813 JLINK_GetHWStatus(...)
T82D4 000:222.961 - 0.161ms returns 0
T82D4 000:223.002 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:223.023 - 0.029ms returns 0x06
T82D4 000:223.041 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:223.059 - 0.027ms returns 0x2000
T82D4 000:223.077 JLINK_GetNumWPUnits()
T82D4 000:223.095 - 0.026ms returns 4
T82D4 000:223.121 JLINK_GetSpeed()
T82D4 000:223.140 - 0.028ms returns 2000
T82D4 000:223.163 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.186   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.524   Data:  02 00 00 00
T82D4 000:223.554 - 0.400ms returns 1 (0x1)
T82D4 000:223.573 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.593   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.953   Data:  02 00 00 00
T82D4 000:223.984 - 0.481ms returns 1 (0x1)
T82D4 000:224.071 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:224.098   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:224.142   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:224.658 - 0.604ms returns 0x1C
T82D4 000:224.695 JLINK_HasError()
T82D4 000:224.715 JLINK_ReadReg(R15 (PC))
T82D4 000:224.735 - 0.028ms returns 0x080001C4
T82D4 000:224.753 JLINK_ReadReg(XPSR)
T82D4 000:224.772 - 0.027ms returns 0x01000000
T82D4 000:228.314 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:228.352   Data:  00 00 00 00
T82D4 000:228.454   Debug reg: DWT_CYCCNT
T82D4 000:228.500 - 0.194ms returns 4 (0x4)
T82D4 000:307.843 JLINK_HasError()
T82D4 000:307.897 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:307.917 - 0.029ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:307.937 JLINK_Reset()
T82D4 000:307.965   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:308.318   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:308.685   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:309.252   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:309.307   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:362.597   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:363.021   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:363.533   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:363.958   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:369.331   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:373.192   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:373.590   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:373.916   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:374.236 - 66.312ms
T82D4 000:374.340 JLINK_HasError()
T82D4 000:374.361 JLINK_ReadReg(R15 (PC))
T82D4 000:374.381 - 0.029ms returns 0x080001C4
T82D4 000:374.400 JLINK_ReadReg(XPSR)
T82D4 000:374.419 - 0.028ms returns 0x01000000
T82D4 000:374.497 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:374.523   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:375.333    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:375.479    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:375.505   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:375.531 - 1.042ms returns 60 (0x3C)
T82D4 000:375.549 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:375.570    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:375.595   Data:  06 48
T82D4 000:375.619 - 0.078ms returns 2 (0x2)
T82D4 000:375.678 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:375.700    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:375.725   Data:  80 47
T82D4 000:375.749 - 0.080ms returns 2 (0x2)
T82D4 000:375.795 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:375.815    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:375.840   Data:  80 47
T82D4 000:375.867 - 0.080ms returns 2 (0x2)
T82D4 000:375.885 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:375.904   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:376.704    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:376.846    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:376.871   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:376.896 - 1.019ms returns 60 (0x3C)
T82D4 000:376.915 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:376.934    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:376.960   Data:  06 48
T82D4 000:376.985 - 0.077ms returns 2 (0x2)
T82D4 000:377.011 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:377.032    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:377.057   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:377.081 - 0.079ms returns 60 (0x3C)
T82D4 000:377.100 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.118    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:377.144   Data:  06 48
T82D4 000:377.169 - 0.076ms returns 2 (0x2)
T82D4 000:377.186 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.206    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:377.231   Data:  00 47
T82D4 000:377.255 - 0.078ms returns 2 (0x2)
T82D4 000:377.280 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.300    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:377.325   Data:  00 47
T82D4 000:377.351 - 0.078ms returns 2 (0x2)
T82D4 000:377.368 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:377.387    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:377.413   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:377.437 - 0.078ms returns 60 (0x3C)
T82D4 000:377.456 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.474    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:377.499   Data:  FE E7
T82D4 000:377.525 - 0.076ms returns 2 (0x2)
T82D4 000:377.547 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:377.568    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:377.594   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:377.618 - 0.079ms returns 60 (0x3C)
T82D4 000:377.637 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.654    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:377.680   Data:  FE E7
T82D4 000:377.706 - 0.078ms returns 2 (0x2)
T82D4 000:377.724 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.743    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:377.767   Data:  FE E7
T82D4 000:377.792 - 0.077ms returns 2 (0x2)
T82D4 000:377.818 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:377.837    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:377.863   Data:  FE E7
T82D4 000:377.888 - 0.078ms returns 2 (0x2)
T82D4 000:377.905 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:377.924    -- Read from C cache (60 bytes @ 0x080001D0)
T82D4 000:378.012   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:378.037 - 0.141ms returns 60 (0x3C)
T82D4 000:378.058 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:378.082    -- Read from C cache (2 bytes @ 0x080001D0)
T82D4 000:378.115   Data:  FE E7
T82D4 000:378.149 - 0.101ms returns 2 (0x2)
T82D4 001:760.323 JLINK_HasError()
T82D4 001:760.387 JLINK_ReadReg(R0)
T82D4 001:760.776 - 0.410ms returns 0x0000061E
T82D4 001:760.811 JLINK_ReadReg(R1)
T82D4 001:760.831 - 0.029ms returns 0x000186A0
T82D4 001:760.850 JLINK_ReadReg(R2)
T82D4 001:760.869 - 0.028ms returns 0x200229E1
T82D4 001:760.889 JLINK_ReadReg(R3)
T82D4 001:760.907 - 0.027ms returns 0x200229E0
T82D4 001:760.927 JLINK_ReadReg(R4)
T82D4 001:760.945 - 0.027ms returns 0x0000061E
T82D4 001:760.964 JLINK_ReadReg(R5)
T82D4 001:760.983 - 0.028ms returns 0x00000000
T82D4 001:761.003 JLINK_ReadReg(R6)
T82D4 001:761.021 - 0.028ms returns 0x00000000
T82D4 001:761.041 JLINK_ReadReg(R7)
T82D4 001:761.061 - 0.030ms returns 0x00000000
T82D4 001:761.082 JLINK_ReadReg(R8)
T82D4 001:761.115 - 0.042ms returns 0x00000000
T82D4 001:761.135 JLINK_ReadReg(R9)
T82D4 001:761.154 - 0.028ms returns 0x20000348
T82D4 001:761.173 JLINK_ReadReg(R10)
T82D4 001:761.192 - 0.028ms returns 0x00000000
T82D4 001:761.211 JLINK_ReadReg(R11)
T82D4 001:761.230 - 0.028ms returns 0x00000000
T82D4 001:761.250 JLINK_ReadReg(R12)
T82D4 001:761.268 - 0.027ms returns 0x000001E0
T82D4 001:761.288 JLINK_ReadReg(R13 (SP))
T82D4 001:761.307 - 0.028ms returns 0x20008038
T82D4 001:761.326 JLINK_ReadReg(R14)
T82D4 001:761.346 - 0.028ms returns 0xFFFFFFFF
T82D4 001:761.364 JLINK_ReadReg(R15 (PC))
T82D4 001:761.383 - 0.029ms returns 0x080001C4
T82D4 001:761.404 JLINK_ReadReg(XPSR)
T82D4 001:761.422 - 0.027ms returns 0x01000000
T82D4 001:761.442 JLINK_ReadReg(MSP)
T82D4 001:761.461 - 0.028ms returns 0x20008038
T82D4 001:761.480 JLINK_ReadReg(PSP)
T82D4 001:761.499 - 0.028ms returns 0x20001000
T82D4 001:761.518 JLINK_ReadReg(CFBP)
T82D4 001:761.536 - 0.028ms returns 0x00000000
T82D4 001:761.557 JLINK_ReadReg(FPSCR)
T82D4 001:767.084 - 5.588ms returns 0x00000000
T82D4 001:767.160 JLINK_ReadReg(FPS0)
T82D4 001:767.201 - 0.050ms returns 0x00000000
T82D4 001:767.222 JLINK_ReadReg(FPS1)
T82D4 001:767.240 - 0.028ms returns 0x3FF00000
T82D4 001:767.262 JLINK_ReadReg(FPS2)
T82D4 001:767.280 - 0.027ms returns 0x40174000
T82D4 001:767.301 JLINK_ReadReg(FPS3)
T82D4 001:767.321 - 0.028ms returns 0x6A610350
T82D4 001:767.340 JLINK_ReadReg(FPS4)
T82D4 001:767.360 - 0.029ms returns 0x004D402C
T82D4 001:767.380 JLINK_ReadReg(FPS5)
T82D4 001:767.401 - 0.029ms returns 0x77490280
T82D4 001:767.420 JLINK_ReadReg(FPS6)
T82D4 001:767.441 - 0.029ms returns 0xD8863040
T82D4 001:767.461 JLINK_ReadReg(FPS7)
T82D4 001:767.481 - 0.029ms returns 0x6F4222A1
T82D4 001:767.501 JLINK_ReadReg(FPS8)
T82D4 001:767.520 - 0.029ms returns 0x1A100861
T82D4 001:767.541 JLINK_ReadReg(FPS9)
T82D4 001:767.560 - 0.029ms returns 0x4C607626
T82D4 001:767.581 JLINK_ReadReg(FPS10)
T82D4 001:767.600 - 0.027ms returns 0x790C9808
T82D4 001:767.621 JLINK_ReadReg(FPS11)
T82D4 001:767.640 - 0.028ms returns 0x432099A0
T82D4 001:767.660 JLINK_ReadReg(FPS12)
T82D4 001:767.680 - 0.029ms returns 0x8A2080C8
T82D4 001:767.700 JLINK_ReadReg(FPS13)
T82D4 001:767.720 - 0.029ms returns 0x1B944171
T82D4 001:767.740 JLINK_ReadReg(FPS14)
T82D4 001:767.760 - 0.029ms returns 0x4502A8C8
T82D4 001:767.780 JLINK_ReadReg(FPS15)
T82D4 001:767.800 - 0.029ms returns 0x192E0105
T82D4 001:767.819 JLINK_ReadReg(FPS16)
T82D4 001:767.839 - 0.028ms returns 0x80A80080
T82D4 001:767.859 JLINK_ReadReg(FPS17)
T82D4 001:767.877 - 0.028ms returns 0x01012210
T82D4 001:767.899 JLINK_ReadReg(FPS18)
T82D4 001:767.918 - 0.028ms returns 0x6FC0024F
T82D4 001:767.938 JLINK_ReadReg(FPS19)
T82D4 001:767.958 - 0.028ms returns 0x0A109945
T82D4 001:767.977 JLINK_ReadReg(FPS20)
T82D4 001:767.997 - 0.029ms returns 0x02052300
T82D4 001:768.017 JLINK_ReadReg(FPS21)
T82D4 001:768.037 - 0.031ms returns 0x2806C828
T82D4 001:768.062 JLINK_ReadReg(FPS22)
T82D4 001:768.106 - 0.070ms returns 0x10036492
T82D4 001:768.143 JLINK_ReadReg(FPS23)
T82D4 001:768.163 - 0.029ms returns 0x89B0B474
T82D4 001:768.183 JLINK_ReadReg(FPS24)
T82D4 001:768.203 - 0.029ms returns 0x8624820A
T82D4 001:768.222 JLINK_ReadReg(FPS25)
T82D4 001:768.242 - 0.029ms returns 0xC00F1403
T82D4 001:768.262 JLINK_ReadReg(FPS26)
T82D4 001:768.281 - 0.028ms returns 0x202A04C0
T82D4 001:768.302 JLINK_ReadReg(FPS27)
T82D4 001:768.321 - 0.027ms returns 0x1D2712AA
T82D4 001:768.341 JLINK_ReadReg(FPS28)
T82D4 001:768.361 - 0.028ms returns 0x544840BB
T82D4 001:768.380 JLINK_ReadReg(FPS29)
T82D4 001:768.400 - 0.029ms returns 0x01482220
T82D4 001:768.420 JLINK_ReadReg(FPS30)
T82D4 001:768.440 - 0.029ms returns 0x0247A931
T82D4 001:768.460 JLINK_ReadReg(FPS31)
T82D4 001:768.503 - 0.052ms returns 0x608C08A3
T82D4 001:826.518 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:826.582   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 001:827.391    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 001:827.431    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:827.458   Data:  00 00
T82D4 001:827.485 - 0.977ms returns 2 (0x2)
T82D4 001:827.568 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:827.595    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:827.622   Data:  00 00
T82D4 001:827.648 - 0.090ms returns 2 (0x2)
T82D4 001:827.685 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:827.708    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 001:827.736   Data:  00 00
T82D4 001:827.763 - 0.087ms returns 2 (0x2)
T82D4 001:833.569 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:833.620    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:833.648   Data:  00 00 00 00
T82D4 001:833.673 - 0.112ms returns 4 (0x4)
T82D4 001:833.704 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:833.727    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:833.753   Data:  00 00 00 00
T82D4 001:833.780 - 0.084ms returns 4 (0x4)
T82D4 001:833.810 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:833.832    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:833.860   Data:  00 00 00 00
T82D4 001:833.885 - 0.083ms returns 4 (0x4)
T82D4 001:848.383 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.445    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:848.473   Data:  00 00 00 00
T82D4 001:848.499 - 0.125ms returns 4 (0x4)
T82D4 001:848.530 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.551    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:848.579   Data:  00 00 00 00
T82D4 001:848.605 - 0.084ms returns 4 (0x4)
T82D4 001:848.635 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:848.657    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 001:848.683   Data:  00 00 00 00
T82D4 001:848.710 - 0.084ms returns 4 (0x4)
T82D4 001:865.608 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:865.674    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:865.703   Data:  00 00 00 00
T82D4 001:865.730 - 0.130ms returns 4 (0x4)
T82D4 001:865.763 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:865.786    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:865.813   Data:  00 00 00 00
T82D4 001:865.840 - 0.087ms returns 4 (0x4)
T82D4 001:865.884 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:865.923    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 001:865.953   Data:  00 00 00 00
T82D4 001:865.979 - 0.104ms returns 4 (0x4)
T82D4 001:873.889 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:873.949    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:873.979   Data:  00 00 00 00
T82D4 001:874.006 - 0.126ms returns 4 (0x4)
T82D4 001:874.040 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:874.064    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:874.097   Data:  00 00 00 00
T82D4 001:874.123 - 0.093ms returns 4 (0x4)
T82D4 001:874.160 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:874.182    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 001:874.210   Data:  00 00 00 00
T82D4 001:874.237 - 0.086ms returns 4 (0x4)
T82D4 001:883.056 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:883.124    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:883.152   Data:  00 00 00 00
T82D4 001:883.180 - 0.132ms returns 4 (0x4)
T82D4 001:883.214 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:883.237    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:883.264   Data:  00 00 00 00
T82D4 001:883.290 - 0.084ms returns 4 (0x4)
T82D4 001:883.325 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:883.348    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:883.375   Data:  00 00 00 00
T82D4 001:883.402 - 0.086ms returns 4 (0x4)
T82D4 001:944.883 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:944.954   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 001:945.922    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 001:946.041    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:946.068   Data:  00 00 00 00
T82D4 001:946.094 - 1.219ms returns 4 (0x4)
T82D4 001:946.178 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:946.204    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:946.232   Data:  00 00 00 00
T82D4 001:946.258 - 0.089ms returns 4 (0x4)
T82D4 001:946.292 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:946.316    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:946.343   Data:  00 00 00 00
T82D4 001:946.369 - 0.087ms returns 4 (0x4)
T82D4 001:957.620 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:957.686    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:957.714   Data:  00 00 00 00
T82D4 001:957.741 - 0.130ms returns 4 (0x4)
T82D4 001:957.777 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:957.799    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:957.827   Data:  00 00 00 00
T82D4 001:957.853 - 0.084ms returns 4 (0x4)
T82D4 001:957.886 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:957.909    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:957.936   Data:  00 00 00 00
T82D4 001:957.963 - 0.086ms returns 4 (0x4)
T82D4 001:968.178 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:968.246   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 001:969.093    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 001:969.157    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:969.183   Data:  00 00 00 00
T82D4 001:969.211 - 1.042ms returns 4 (0x4)
T82D4 001:969.270 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:969.297    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:969.325   Data:  00 00 00 00
T82D4 001:969.350 - 0.089ms returns 4 (0x4)
T82D4 001:969.427 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:969.451    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:969.498   Data:  00 00 00 00
T82D4 001:969.524 - 0.105ms returns 4 (0x4)
T82D4 001:980.084 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:980.160   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 001:981.259    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 001:981.340    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:981.368   Data:  00 00 00 00
T82D4 001:981.396 - 1.322ms returns 4 (0x4)
T82D4 001:981.451 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:981.537    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:981.619   Data:  00 00 00 00
T82D4 001:981.708 - 0.280ms returns 4 (0x4)
T82D4 001:981.801 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:981.823    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 001:981.850   Data:  00 00 00 00
T82D4 001:981.884 - 0.114ms returns 4 (0x4)
T82D4 001:996.019 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:996.089    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:996.119   Data:  00 00 00 00
T82D4 001:996.269 - 0.262ms returns 4 (0x4)
T82D4 001:996.366 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:996.399    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:996.427   Data:  00 00 00 00
T82D4 001:996.456 - 0.099ms returns 4 (0x4)
T82D4 001:996.509 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:996.534    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:996.562   Data:  00 00 00 00
T82D4 001:996.590 - 0.090ms returns 4 (0x4)
T82D4 002:008.926 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:008.999   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:009.822    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:009.867    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:009.895   Data:  71
T82D4 002:009.921 - 1.004ms returns 1 (0x1)
T82D4 002:009.972 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:009.997    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:010.024   Data:  71
T82D4 002:010.052 - 0.089ms returns 1 (0x1)
T82D4 002:010.087 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:010.109    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:010.140   Data:  71
T82D4 002:010.165 - 0.086ms returns 1 (0x1)
T82D4 002:024.208 JLINK_ReadMemEx(0x20019D64, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:024.281   CPU_ReadMem(128 bytes @ 0x20019D40)
T82D4 002:025.588    -- Updating C cache (128 bytes @ 0x20019D40)
T82D4 002:025.633    -- Read from C cache (32 bytes @ 0x20019D64)
T82D4 002:025.661   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:025.687 - 1.488ms returns 32 (0x20)
T82D4 002:107.562 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:107.621    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:107.649   Data:  00 00 00 00
T82D4 002:107.697 - 0.145ms returns 4 (0x4)
T82D4 002:107.737 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:107.762    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:107.789   Data:  00 00 00 00
T82D4 002:107.816 - 0.088ms returns 4 (0x4)
T82D4 002:107.854 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:107.877    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:107.904   Data:  00 00 00 00
T82D4 002:107.930 - 0.084ms returns 4 (0x4)
T82D4 002:120.238 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:120.491    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:120.531   Data:  00 00 00 00
T82D4 002:120.564 - 0.336ms returns 4 (0x4)
T82D4 002:120.660 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:120.691    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:120.724   Data:  00 00 00 00
T82D4 002:120.752 - 0.102ms returns 4 (0x4)
T82D4 002:120.834 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:120.860    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:120.892   Data:  00 00 00 00
T82D4 002:121.026 - 0.204ms returns 4 (0x4)
T82D4 002:134.152 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:134.206    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:134.234   Data:  00 00 00 00
T82D4 002:134.260 - 0.117ms returns 4 (0x4)
T82D4 002:134.292 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:134.314    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:134.342   Data:  00 00 00 00
T82D4 002:134.368 - 0.084ms returns 4 (0x4)
T82D4 002:134.401 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:134.424    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:134.450   Data:  00 00 00 00
T82D4 002:134.477 - 0.085ms returns 4 (0x4)
T82D4 002:200.945 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:201.012    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:201.131   Data:  71
T82D4 002:201.159 - 0.223ms returns 1 (0x1)
T82D4 002:201.219 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:201.249    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:201.286   Data:  71
T82D4 002:201.328 - 0.120ms returns 1 (0x1)
T82D4 002:201.369 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:201.393    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:201.421   Data:  71
T82D4 002:201.448 - 0.259ms returns 1 (0x1)
T82D4 002:307.552 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:307.644    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:307.673   Data:  00 00 00 00
T82D4 002:307.703 - 0.161ms returns 4 (0x4)
T82D4 002:307.745 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:307.769    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:307.798   Data:  00 00 00 00
T82D4 002:307.825 - 0.088ms returns 4 (0x4)
T82D4 002:307.861 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:307.885    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:307.913   Data:  00 00 00 00
T82D4 002:307.942 - 0.089ms returns 4 (0x4)
T82D4 002:321.491 JLINK_ReadMemEx(0x08018360, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:321.561   CPU_ReadMem(64 bytes @ 0x08018340)
T82D4 002:322.369    -- Updating C cache (64 bytes @ 0x08018340)
T82D4 002:322.414    -- Read from C cache (8 bytes @ 0x08018360)
T82D4 002:322.443   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:322.469 - 0.988ms returns 8 (0x8)
T82D4 002:322.553 JLINK_ReadMemEx(0x08018360, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:322.579    -- Read from C cache (8 bytes @ 0x08018360)
T82D4 002:322.608   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:322.637 - 0.092ms returns 8 (0x8)
T82D4 002:322.673 JLINK_ReadMemEx(0x08018360, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:322.697    -- Read from C cache (8 bytes @ 0x08018360)
T82D4 002:322.725   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:322.752 - 0.088ms returns 8 (0x8)
T82D4 002:348.638 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:348.711   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:349.520    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:349.564    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:349.590   Data:  00 00 00 00
T82D4 002:349.618 - 0.989ms returns 4 (0x4)
T82D4 002:349.661 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:349.687    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:349.716   Data:  00 00 00 00
T82D4 002:349.743 - 0.092ms returns 4 (0x4)
T82D4 002:349.780 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:349.803    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:349.831   Data:  00 00 00 00
T82D4 002:349.859 - 0.088ms returns 4 (0x4)
T82D4 002:417.482 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:417.547    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:417.577   Data:  00 00 00 00
T82D4 002:417.604 - 0.131ms returns 4 (0x4)
T82D4 002:417.643 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:417.667    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:417.694   Data:  00 00 00 00
T82D4 002:417.724 - 0.090ms returns 4 (0x4)
T82D4 002:417.762 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:417.787    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:417.815   Data:  00 00 00 00
T82D4 002:417.842 - 0.089ms returns 4 (0x4)
T82D4 002:438.163 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:438.222    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:438.251   Data:  00 00 00 00
T82D4 002:438.278 - 0.125ms returns 4 (0x4)
T82D4 002:438.312 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:438.335    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:438.363   Data:  00 00 00 00
T82D4 002:438.391 - 0.087ms returns 4 (0x4)
T82D4 002:438.419 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:438.447    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:438.478   Data:  00 00 00 00
T82D4 002:438.505 - 0.095ms returns 4 (0x4)
T89AC 002:476.120 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T89AC 002:476.208    -- Read from C cache (2 bytes @ 0x080001C4)
T89AC 002:476.237   Data:  06 48
T89AC 002:476.263 - 0.152ms returns 2 (0x2)
T89AC 002:476.284 JLINK_HasError()
T89AC 002:476.306 JLINK_SetBPEx(Addr = 0x08016200, Type = 0xFFFFFFF2)
T89AC 002:476.337 - 0.040ms returns 0x00000001
T89AC 002:476.357 JLINK_HasError()
T89AC 002:476.376 JLINK_HasError()
T89AC 002:476.397 JLINK_Go()
T89AC 002:476.779   CPU_WriteMem(4 bytes @ 0xE0002000)
T89AC 002:477.223   CPU_ReadMem(4 bytes @ 0xE0001000)
T89AC 002:477.556   CPU_WriteMem(4 bytes @ 0xE0002008)
T89AC 002:477.591   CPU_WriteMem(4 bytes @ 0xE000200C)
T89AC 002:477.620   CPU_WriteMem(4 bytes @ 0xE0002010)
T89AC 002:477.648   CPU_WriteMem(4 bytes @ 0xE0002014)
T89AC 002:477.675   CPU_WriteMem(4 bytes @ 0xE0002018)
T89AC 002:477.702   CPU_WriteMem(4 bytes @ 0xE000201C)
T89AC 002:479.232   CPU_WriteMem(4 bytes @ 0xE0001004)
T89AC 002:480.046 - 3.668ms
T89AC 002:580.947 JLINK_HasError()
T89AC 002:581.010 JLINK_IsHalted()
T89AC 002:581.800 - 0.835ms returns FALSE
T89AC 002:682.206 JLINK_HasError()
T89AC 002:682.254 JLINK_IsHalted()
T89AC 002:685.750 - 3.516ms returns TRUE
T89AC 002:685.783 JLINK_HasError()
T89AC 002:685.802 JLINK_Halt()
T89AC 002:685.819 - 0.026ms returns 0x00
T89AC 002:685.838 JLINK_IsHalted()
T89AC 002:685.855 - 0.025ms returns TRUE
T89AC 002:685.873 JLINK_IsHalted()
T89AC 002:685.891 - 0.027ms returns TRUE
T89AC 002:685.909 JLINK_IsHalted()
T89AC 002:685.927 - 0.026ms returns TRUE
T89AC 002:685.945 JLINK_HasError()
T89AC 002:685.963 JLINK_ReadReg(R15 (PC))
T89AC 002:685.985 - 0.031ms returns 0x08016200
T89AC 002:686.004 JLINK_ReadReg(XPSR)
T89AC 002:686.023 - 0.027ms returns 0x61000000
T89AC 002:686.043 JLINK_HasError()
T89AC 002:686.062 JLINK_ClrBPEx(BPHandle = 0x00000001)
T89AC 002:686.080 - 0.027ms returns 0x00
T89AC 002:686.098 JLINK_HasError()
T89AC 002:686.117 JLINK_HasError()
T89AC 002:686.136 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T89AC 002:686.160   CPU_ReadMem(4 bytes @ 0xE000ED30)
T89AC 002:686.544   Data:  02 00 00 00
T89AC 002:686.583 - 0.457ms returns 1 (0x1)
T89AC 002:686.604 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T89AC 002:686.626   CPU_ReadMem(4 bytes @ 0xE0001028)
T89AC 002:687.004   Data:  00 00 00 00
T89AC 002:687.047   Debug reg: DWT_FUNC[0]
T89AC 002:687.074 - 0.479ms returns 1 (0x1)
T89AC 002:687.093 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T89AC 002:687.115   CPU_ReadMem(4 bytes @ 0xE0001038)
T89AC 002:687.526   Data:  00 02 00 00
T89AC 002:687.562   Debug reg: DWT_FUNC[1]
T89AC 002:687.586 - 0.502ms returns 1 (0x1)
T89AC 002:687.606 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T89AC 002:687.627   CPU_ReadMem(4 bytes @ 0xE0001048)
T89AC 002:687.940   Data:  00 00 00 00
T89AC 002:687.979   Debug reg: DWT_FUNC[2]
T89AC 002:688.005 - 0.407ms returns 1 (0x1)
T89AC 002:688.024 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T89AC 002:688.048   CPU_ReadMem(4 bytes @ 0xE0001058)
T89AC 002:688.461   Data:  00 00 00 00
T89AC 002:688.492   Debug reg: DWT_FUNC[3]
T89AC 002:688.516 - 0.500ms returns 1 (0x1)
T89AC 002:688.605 JLINK_HasError()
T89AC 002:688.626 JLINK_ReadReg(R0)
T89AC 002:688.646 - 0.029ms returns 0x08016201
T89AC 002:688.665 JLINK_ReadReg(R1)
T89AC 002:688.683 - 0.027ms returns 0x20022A10
T89AC 002:688.703 JLINK_ReadReg(R2)
T89AC 002:688.720 - 0.026ms returns 0x00000000
T89AC 002:688.738 JLINK_ReadReg(R3)
T89AC 002:688.757 - 0.027ms returns 0x080129F9
T89AC 002:688.775 JLINK_ReadReg(R4)
T89AC 002:688.793 - 0.027ms returns 0x080188D0
T89AC 002:688.812 JLINK_ReadReg(R5)
T89AC 002:688.829 - 0.026ms returns 0x080188D0
T89AC 002:688.849 JLINK_ReadReg(R6)
T89AC 002:688.866 - 0.026ms returns 0x00000000
T89AC 002:688.884 JLINK_ReadReg(R7)
T89AC 002:688.902 - 0.027ms returns 0x00000000
T89AC 002:688.920 JLINK_ReadReg(R8)
T89AC 002:688.942 - 0.033ms returns 0x00000000
T89AC 002:688.963 JLINK_ReadReg(R9)
T89AC 002:688.980 - 0.027ms returns 0x20000348
T89AC 002:689.001 JLINK_ReadReg(R10)
T89AC 002:689.019 - 0.026ms returns 0x00000000
T89AC 002:689.037 JLINK_ReadReg(R11)
T89AC 002:689.056 - 0.027ms returns 0x00000000
T89AC 002:689.074 JLINK_ReadReg(R12)
T89AC 002:689.092 - 0.027ms returns 0x000001E0
T89AC 002:689.111 JLINK_ReadReg(R13 (SP))
T89AC 002:689.129 - 0.027ms returns 0x20022A10
T89AC 002:689.148 JLINK_ReadReg(R14)
T89AC 002:689.165 - 0.025ms returns 0x08004E6D
T89AC 002:689.184 JLINK_ReadReg(R15 (PC))
T89AC 002:689.202 - 0.026ms returns 0x08016200
T89AC 002:689.220 JLINK_ReadReg(XPSR)
T89AC 002:689.238 - 0.028ms returns 0x61000000
T89AC 002:689.257 JLINK_ReadReg(MSP)
T89AC 002:689.276 - 0.027ms returns 0x20022A10
T89AC 002:689.295 JLINK_ReadReg(PSP)
T89AC 002:689.312 - 0.025ms returns 0x20001000
T89AC 002:689.330 JLINK_ReadReg(CFBP)
T89AC 002:689.348 - 0.026ms returns 0x00000000
T89AC 002:689.366 JLINK_ReadReg(FPSCR)
T89AC 002:694.805 - 5.494ms returns 0x00000000
T89AC 002:694.875 JLINK_ReadReg(FPS0)
T89AC 002:694.910 - 0.068ms returns 0x00000000
T89AC 002:694.971 JLINK_ReadReg(FPS1)
T89AC 002:694.990 - 0.045ms returns 0x3FF00000
T89AC 002:695.141 JLINK_ReadReg(FPS2)
T89AC 002:695.189 - 0.057ms returns 0x40174000
T89AC 002:695.208 JLINK_ReadReg(FPS3)
T89AC 002:695.228 - 0.028ms returns 0x6A610350
T89AC 002:695.246 JLINK_ReadReg(FPS4)
T89AC 002:695.264 - 0.027ms returns 0x004D402C
T89AC 002:695.283 JLINK_ReadReg(FPS5)
T89AC 002:695.322 - 0.047ms returns 0x77490280
T89AC 002:695.341 JLINK_ReadReg(FPS6)
T89AC 002:695.360 - 0.029ms returns 0xD8863040
T89AC 002:695.380 JLINK_ReadReg(FPS7)
T89AC 002:695.434 - 0.064ms returns 0x6F4222A1
T89AC 002:695.453 JLINK_ReadReg(FPS8)
T89AC 002:695.485 - 0.042ms returns 0x1A100861
T89AC 002:695.505 JLINK_ReadReg(FPS9)
T89AC 002:695.543 - 0.047ms returns 0x4C607626
T89AC 002:695.562 JLINK_ReadReg(FPS10)
T89AC 002:695.620 - 0.082ms returns 0x790C9808
T89AC 002:695.691 JLINK_ReadReg(FPS11)
T89AC 002:695.733 - 0.065ms returns 0x432099A0
T89AC 002:695.766 JLINK_ReadReg(FPS12)
T89AC 002:695.784 - 0.028ms returns 0x8A2080C8
T89AC 002:695.824 JLINK_ReadReg(FPS13)
T89AC 002:695.843 - 0.027ms returns 0x1B944171
T89AC 002:695.863 JLINK_ReadReg(FPS14)
T89AC 002:695.882 - 0.044ms returns 0x4502A8C8
T89AC 002:695.937 JLINK_ReadReg(FPS15)
T89AC 002:695.957 - 0.028ms returns 0x192E0105
T89AC 002:695.975 JLINK_ReadReg(FPS16)
T89AC 002:695.994 - 0.028ms returns 0x80A80080
T89AC 002:696.014 JLINK_ReadReg(FPS17)
T89AC 002:696.031 - 0.026ms returns 0x01012210
T89AC 002:696.051 JLINK_ReadReg(FPS18)
T89AC 002:696.070 - 0.028ms returns 0x6FC0024F
T89AC 002:696.089 JLINK_ReadReg(FPS19)
T89AC 002:696.108 - 0.028ms returns 0x0A109945
T89AC 002:696.127 JLINK_ReadReg(FPS20)
T89AC 002:696.146 - 0.028ms returns 0x02052300
T89AC 002:696.165 JLINK_ReadReg(FPS21)
T89AC 002:696.183 - 0.026ms returns 0x2806C828
T89AC 002:696.203 JLINK_ReadReg(FPS22)
T89AC 002:696.222 - 0.028ms returns 0x10036492
T89AC 002:696.240 JLINK_ReadReg(FPS23)
T89AC 002:696.260 - 0.028ms returns 0x89B0B474
T89AC 002:696.279 JLINK_ReadReg(FPS24)
T89AC 002:696.297 - 0.028ms returns 0x8624820A
T89AC 002:696.317 JLINK_ReadReg(FPS25)
T89AC 002:696.335 - 0.027ms returns 0xC00F1403
T89AC 002:696.354 JLINK_ReadReg(FPS26)
T89AC 002:696.374 - 0.028ms returns 0x202A04C0
T89AC 002:696.393 JLINK_ReadReg(FPS27)
T89AC 002:696.413 - 0.029ms returns 0x1D2712AA
T89AC 002:696.432 JLINK_ReadReg(FPS28)
T89AC 002:696.451 - 0.029ms returns 0x544840BB
T89AC 002:696.471 JLINK_ReadReg(FPS29)
T89AC 002:696.489 - 0.027ms returns 0x01482220
T89AC 002:696.508 JLINK_ReadReg(FPS30)
T89AC 002:696.528 - 0.029ms returns 0x0247A931
T89AC 002:696.547 JLINK_ReadReg(FPS31)
T89AC 002:696.568 - 0.052ms returns 0x608C08A3
T82D4 002:698.300 JLINK_HasError()
T82D4 002:698.332 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:698.359   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:698.677   Data:  DC F4 13 00
T82D4 002:698.712   Debug reg: DWT_CYCCNT
T82D4 002:698.741 - 0.417ms returns 1 (0x1)
T82D4 002:700.416 JLINK_ReadMemEx(0x20019C66, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:700.453   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 002:701.314    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 002:701.445    -- Read from C cache (2 bytes @ 0x20019C66)
T82D4 002:701.494   Data:  00 00
T82D4 002:701.523 - 1.115ms returns 2 (0x2)
T82D4 002:701.590 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:701.633    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:701.682   Data:  00 00 00 00
T82D4 002:701.757 - 0.177ms returns 4 (0x4)
T82D4 002:702.366 JLINK_ReadMemEx(0x20019C62, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.422    -- Read from C cache (4 bytes @ 0x20019C62)
T82D4 002:702.448   Data:  00 00 00 00
T82D4 002:702.492 - 0.135ms returns 4 (0x4)
T82D4 002:702.529 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.575    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:702.618   Data:  00 00 00 00
T82D4 002:702.644 - 0.123ms returns 4 (0x4)
T82D4 002:702.667 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.687    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:702.713   Data:  00 00 00 00
T82D4 002:702.737 - 0.079ms returns 4 (0x4)
T82D4 002:702.760 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.805    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:702.831   Data:  00 00 00 00
T82D4 002:702.876 - 0.123ms returns 4 (0x4)
T82D4 002:702.917 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:702.940   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:703.806    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:703.949    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:703.974   Data:  00 00 00 00
T82D4 002:704.000 - 1.091ms returns 4 (0x4)
T82D4 002:704.025 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:704.047    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:704.073   Data:  00 00 00 00
T82D4 002:704.097 - 0.081ms returns 4 (0x4)
T82D4 002:704.121 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:704.142   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:704.953    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:705.097    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:705.122   Data:  00 00 00 00
T82D4 002:705.147 - 1.035ms returns 4 (0x4)
T82D4 002:705.173 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:705.195   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:705.991    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:706.098    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:706.123   Data:  00 00 00 00
T82D4 002:706.148 - 0.984ms returns 4 (0x4)
T82D4 002:706.174 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:706.194    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:706.222   Data:  00 00 00 00
T82D4 002:706.247 - 0.081ms returns 4 (0x4)
T82D4 002:706.269 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:706.291   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:707.081    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:707.137    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:707.180   Data:  00
T82D4 002:707.205 - 0.945ms returns 1 (0x1)
T82D4 002:707.424 JLINK_ReadMemEx(0x20019D64, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:707.469   CPU_ReadMem(128 bytes @ 0x20019D40)
T82D4 002:708.770    -- Updating C cache (128 bytes @ 0x20019D40)
T82D4 002:708.910    -- Read from C cache (32 bytes @ 0x20019D64)
T82D4 002:708.938   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:708.963 - 1.548ms returns 32 (0x20)
T82D4 002:709.082 JLINK_ReadMemEx(0x20019C74, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:709.107    -- Read from C cache (4 bytes @ 0x20019C74)
T82D4 002:709.132   Data:  00 00 00 00
T82D4 002:709.157 - 0.084ms returns 4 (0x4)
T82D4 002:709.183 JLINK_ReadMemEx(0x20019C70, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:709.207    -- Read from C cache (4 bytes @ 0x20019C70)
T82D4 002:709.235   Data:  00 00 00 00
T82D4 002:709.260 - 0.085ms returns 4 (0x4)
T82D4 002:709.283 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:709.305    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:709.330   Data:  00 00 00 00
T82D4 002:709.354 - 0.080ms returns 4 (0x4)
T82D4 002:709.387 JLINK_ReadMemEx(0x20000C70, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:709.408    -- Read from C cache (1 bytes @ 0x20000C70)
T82D4 002:709.434   Data:  00
T82D4 002:709.459 - 0.080ms returns 1 (0x1)
T82D4 002:709.509 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:709.531    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:709.556   Data:  00 00 00 00
T82D4 002:709.582 - 0.082ms returns 4 (0x4)
T82D4 002:709.605 JLINK_ReadMemEx(0x08018360, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:709.626   CPU_ReadMem(64 bytes @ 0x08018340)
T82D4 002:710.390    -- Updating C cache (64 bytes @ 0x08018340)
T82D4 002:710.429    -- Read from C cache (8 bytes @ 0x08018360)
T82D4 002:710.454   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:710.478 - 0.882ms returns 8 (0x8)
T82D4 002:710.635 JLINK_ReadMemEx(0x2001A35A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:710.661   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:711.495    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:711.624    -- Read from C cache (4 bytes @ 0x2001A35A)
T82D4 002:711.649   Data:  00 00 00 00
T82D4 002:711.675 - 1.049ms returns 4 (0x4)
T82D4 002:711.909 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:711.935    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:711.961   Data:  00 00 00 00
T82D4 002:711.987 - 0.085ms returns 4 (0x4)
T82D4 002:714.841 JLINK_ReadMemEx(0x08016100, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:714.911   CPU_ReadMem(64 bytes @ 0x08016100)
T82D4 002:715.696    -- Updating C cache (64 bytes @ 0x08016100)
T82D4 002:715.775    -- Read from C cache (60 bytes @ 0x08016100)
T82D4 002:715.840   Data:  01 F0 27 FE B0 EE 40 8A F0 EE 60 8A 9F ED 0E 0B ...
T82D4 002:715.866 - 1.049ms returns 60 (0x3C)
T82D4 002:715.923 JLINK_ReadMemEx(0x08016100, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:715.945    -- Read from C cache (2 bytes @ 0x08016100)
T82D4 002:716.008   Data:  01 F0
T82D4 002:716.052 - 0.157ms returns 2 (0x2)
T82D4 002:716.091 JLINK_ReadMemEx(0x08016102, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.111    -- Read from C cache (2 bytes @ 0x08016102)
T82D4 002:716.139   Data:  27 FE
T82D4 002:716.165 - 0.083ms returns 2 (0x2)
T82D4 002:716.188 JLINK_ReadMemEx(0x08016104, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:716.209    -- Read from C cache (60 bytes @ 0x08016104)
T82D4 002:716.238   Data:  B0 EE 40 8A F0 EE 60 8A 9F ED 0E 0B 53 EC 10 2B ...
T82D4 002:716.264 - 0.084ms returns 60 (0x3C)
T82D4 002:716.283 JLINK_ReadMemEx(0x08016104, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.304    -- Read from C cache (2 bytes @ 0x08016104)
T82D4 002:716.330   Data:  B0 EE
T82D4 002:716.357 - 0.084ms returns 2 (0x2)
T82D4 002:716.382 JLINK_ReadMemEx(0x08016106, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:716.401    -- Read from C cache (2 bytes @ 0x08016106)
T82D4 002:716.429   Data:  40 8A
T82D4 002:716.456 - 0.082ms returns 2 (0x2)
T82D4 002:716.475 JLINK_ReadMemEx(0x08016108, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:716.498   CPU_ReadMem(64 bytes @ 0x08016140)
T82D4 002:717.261    -- Updating C cache (64 bytes @ 0x08016140)
T82D4 002:717.302    -- Read from C cache (60 bytes @ 0x08016108)
T82D4 002:717.330   Data:  F0 EE 60 8A 9F ED 0E 0B 53 EC 10 2B 51 EC 18 0B ...
T82D4 002:717.357 - 0.891ms returns 60 (0x3C)
T82D4 002:717.378 JLINK_ReadMemEx(0x08016108, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.398    -- Read from C cache (2 bytes @ 0x08016108)
T82D4 002:717.426   Data:  F0 EE
T82D4 002:717.453 - 0.083ms returns 2 (0x2)
T82D4 002:717.472 JLINK_ReadMemEx(0x0801610A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.492    -- Read from C cache (2 bytes @ 0x0801610A)
T82D4 002:717.524   Data:  60 8A
T82D4 002:717.553 - 0.089ms returns 2 (0x2)
T82D4 002:717.573 JLINK_ReadMemEx(0x0801610C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.592    -- Read from C cache (60 bytes @ 0x0801610C)
T82D4 002:717.621   Data:  9F ED 0E 0B 53 EC 10 2B 51 EC 18 0B EE F7 60 FC ...
T82D4 002:717.647 - 0.082ms returns 60 (0x3C)
T82D4 002:717.665 JLINK_ReadMemEx(0x0801610C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.686    -- Read from C cache (2 bytes @ 0x0801610C)
T82D4 002:717.712   Data:  9F ED
T82D4 002:717.740 - 0.083ms returns 2 (0x2)
T82D4 002:717.760 JLINK_ReadMemEx(0x0801610E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.778    -- Read from C cache (2 bytes @ 0x0801610E)
T82D4 002:717.807   Data:  0E 0B
T82D4 002:717.833 - 0.082ms returns 2 (0x2)
T82D4 002:717.852 JLINK_ReadMemEx(0x08016110, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:717.873    -- Read from C cache (60 bytes @ 0x08016110)
T82D4 002:717.900   Data:  53 EC 10 2B 51 EC 18 0B EE F7 60 FC 09 D8 08 A8 ...
T82D4 002:717.928 - 0.085ms returns 60 (0x3C)
T82D4 002:717.947 JLINK_ReadMemEx(0x08016110, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:717.966    -- Read from C cache (2 bytes @ 0x08016110)
T82D4 002:717.994   Data:  53 EC
T82D4 002:718.020 - 0.081ms returns 2 (0x2)
T82D4 002:718.039 JLINK_ReadMemEx(0x08016112, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.059    -- Read from C cache (2 bytes @ 0x08016112)
T82D4 002:718.086   Data:  10 2B
T82D4 002:718.114 - 0.084ms returns 2 (0x2)
T82D4 002:718.134 JLINK_ReadMemEx(0x08016114, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.153    -- Read from C cache (60 bytes @ 0x08016114)
T82D4 002:718.183   Data:  51 EC 18 0B EE F7 60 FC 09 D8 08 A8 0F C8 85 E8 ...
T82D4 002:718.209 - 0.084ms returns 60 (0x3C)
T82D4 002:718.229 JLINK_ReadMemEx(0x08016114, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.249    -- Read from C cache (2 bytes @ 0x08016114)
T82D4 002:718.276   Data:  51 EC
T82D4 002:718.304 - 0.083ms returns 2 (0x2)
T82D4 002:718.323 JLINK_ReadMemEx(0x08016116, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.343    -- Read from C cache (2 bytes @ 0x08016116)
T82D4 002:718.371   Data:  18 0B
T82D4 002:718.398 - 0.084ms returns 2 (0x2)
T82D4 002:718.418 JLINK_ReadMemEx(0x08016118, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.440    -- Read from C cache (60 bytes @ 0x08016118)
T82D4 002:718.468   Data:  EE F7 60 FC 09 D8 08 A8 0F C8 85 E8 0F 00 0D B0 ...
T82D4 002:718.496 - 0.088ms returns 60 (0x3C)
T82D4 002:718.516 JLINK_ReadMemEx(0x08016118, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.537    -- Read from C cache (2 bytes @ 0x08016118)
T82D4 002:718.565   Data:  EE F7
T82D4 002:718.591 - 0.084ms returns 2 (0x2)
T82D4 002:718.611 JLINK_ReadMemEx(0x0801611A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.631    -- Read from C cache (2 bytes @ 0x0801611A)
T82D4 002:718.659   Data:  60 FC
T82D4 002:718.687 - 0.085ms returns 2 (0x2)
T82D4 002:718.708 JLINK_ReadMemEx(0x0801611C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:718.727    -- Read from C cache (60 bytes @ 0x0801611C)
T82D4 002:718.756   Data:  09 D8 08 A8 0F C8 85 E8 0F 00 0D B0 BD EC 02 8B ...
T82D4 002:718.782 - 0.083ms returns 60 (0x3C)
T82D4 002:718.802 JLINK_ReadMemEx(0x0801611C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.823    -- Read from C cache (2 bytes @ 0x0801611C)
T82D4 002:718.854   Data:  09 D8
T82D4 002:718.885 - 0.093ms returns 2 (0x2)
T82D4 002:718.906 JLINK_ReadMemEx(0x0801611E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:718.927    -- Read from C cache (2 bytes @ 0x0801611E)
T82D4 002:718.955   Data:  08 A8
T82D4 002:718.981 - 0.083ms returns 2 (0x2)
T82D4 002:719.002 JLINK_ReadMemEx(0x0801611E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.022    -- Read from C cache (2 bytes @ 0x0801611E)
T82D4 002:719.048   Data:  08 A8
T82D4 002:719.077 - 0.085ms returns 2 (0x2)
T82D4 002:719.097 JLINK_ReadMemEx(0x08016120, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.117    -- Read from C cache (60 bytes @ 0x08016120)
T82D4 002:719.145   Data:  0F C8 85 E8 0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 ...
T82D4 002:719.182 - 0.095ms returns 60 (0x3C)
T82D4 002:719.203 JLINK_ReadMemEx(0x08016120, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.223    -- Read from C cache (2 bytes @ 0x08016120)
T82D4 002:719.251   Data:  0F C8
T82D4 002:719.278 - 0.083ms returns 2 (0x2)
T82D4 002:719.297 JLINK_ReadMemEx(0x08016120, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.318    -- Read from C cache (60 bytes @ 0x08016120)
T82D4 002:719.346   Data:  0F C8 85 E8 0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 ...
T82D4 002:719.372 - 0.085ms returns 60 (0x3C)
T82D4 002:719.394 JLINK_ReadMemEx(0x08016120, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.414    -- Read from C cache (2 bytes @ 0x08016120)
T82D4 002:719.441   Data:  0F C8
T82D4 002:719.469 - 0.083ms returns 2 (0x2)
T82D4 002:719.488 JLINK_ReadMemEx(0x08016122, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.509    -- Read from C cache (2 bytes @ 0x08016122)
T82D4 002:719.536   Data:  85 E8
T82D4 002:719.562 - 0.083ms returns 2 (0x2)
T82D4 002:719.584 JLINK_ReadMemEx(0x08016122, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.603    -- Read from C cache (2 bytes @ 0x08016122)
T82D4 002:719.630   Data:  85 E8
T82D4 002:719.657 - 0.082ms returns 2 (0x2)
T82D4 002:719.678 JLINK_ReadMemEx(0x08016124, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.698    -- Read from C cache (60 bytes @ 0x08016124)
T82D4 002:719.728   Data:  0F 00 0D B0 BD EC 02 8B 30 BC 5D F8 14 FB 64 1C ...
T82D4 002:719.754 - 0.086ms returns 60 (0x3C)
T82D4 002:719.775 JLINK_ReadMemEx(0x08016124, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.794    -- Read from C cache (2 bytes @ 0x08016124)
T82D4 002:719.822   Data:  0F 00
T82D4 002:719.849 - 0.083ms returns 2 (0x2)
T82D4 002:719.870 JLINK_ReadMemEx(0x08016126, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:719.890    -- Read from C cache (2 bytes @ 0x08016126)
T82D4 002:719.916   Data:  0D B0
T82D4 002:719.942 - 0.082ms returns 2 (0x2)
T82D4 002:719.963 JLINK_ReadMemEx(0x08016128, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:719.981    -- Read from C cache (60 bytes @ 0x08016128)
T82D4 002:720.009   Data:  BD EC 02 8B 30 BC 5D F8 14 FB 64 1C 12 2C BB DB ...
T82D4 002:720.037 - 0.082ms returns 60 (0x3C)
T82D4 002:720.055 JLINK_ReadMemEx(0x08016128, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.076    -- Read from C cache (2 bytes @ 0x08016128)
T82D4 002:720.102   Data:  BD EC
T82D4 002:720.128 - 0.082ms returns 2 (0x2)
T82D4 002:720.149 JLINK_ReadMemEx(0x08016128, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.169    -- Read from C cache (60 bytes @ 0x08016128)
T82D4 002:720.199   Data:  BD EC 02 8B 30 BC 5D F8 14 FB 64 1C 12 2C BB DB ...
T82D4 002:720.225 - 0.084ms returns 60 (0x3C)
T82D4 002:720.246 JLINK_ReadMemEx(0x08016128, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.266    -- Read from C cache (2 bytes @ 0x08016128)
T82D4 002:720.293   Data:  BD EC
T82D4 002:720.321 - 0.084ms returns 2 (0x2)
T82D4 002:720.342 JLINK_ReadMemEx(0x0801612A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.361    -- Read from C cache (2 bytes @ 0x0801612A)
T82D4 002:720.389   Data:  02 8B
T82D4 002:720.414 - 0.081ms returns 2 (0x2)
T82D4 002:720.435 JLINK_ReadMemEx(0x0801612C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.456    -- Read from C cache (60 bytes @ 0x0801612C)
T82D4 002:720.484   Data:  30 BC 5D F8 14 FB 64 1C 12 2C BB DB 14 A8 0F C8 ...
T82D4 002:720.511 - 0.084ms returns 60 (0x3C)
T82D4 002:720.530 JLINK_ReadMemEx(0x0801612C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.551    -- Read from C cache (2 bytes @ 0x0801612C)
T82D4 002:720.578   Data:  30 BC
T82D4 002:720.605 - 0.084ms returns 2 (0x2)
T82D4 002:720.626 JLINK_ReadMemEx(0x0801612E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.645    -- Read from C cache (2 bytes @ 0x0801612E)
T82D4 002:720.673   Data:  5D F8
T82D4 002:720.699 - 0.081ms returns 2 (0x2)
T82D4 002:720.719 JLINK_ReadMemEx(0x0801612E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.739    -- Read from C cache (2 bytes @ 0x0801612E)
T82D4 002:720.765   Data:  5D F8
T82D4 002:720.796 - 0.086ms returns 2 (0x2)
T82D4 002:720.816 JLINK_ReadMemEx(0x08016130, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:720.837    -- Read from C cache (60 bytes @ 0x08016130)
T82D4 002:720.864   Data:  14 FB 64 1C 12 2C BB DB 14 A8 0F C8 85 E8 0F 00 ...
T82D4 002:720.891 - 0.084ms returns 60 (0x3C)
T82D4 002:720.912 JLINK_ReadMemEx(0x08016130, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:720.931    -- Read from C cache (2 bytes @ 0x08016130)
T82D4 002:720.960   Data:  14 FB
T82D4 002:720.986 - 0.082ms returns 2 (0x2)
T82D4 002:721.006 JLINK_ReadMemEx(0x08016132, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.027    -- Read from C cache (2 bytes @ 0x08016132)
T82D4 002:721.054   Data:  64 1C
T82D4 002:721.082 - 0.084ms returns 2 (0x2)
T82D4 002:721.102 JLINK_ReadMemEx(0x08016134, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:721.121    -- Read from C cache (60 bytes @ 0x08016134)
T82D4 002:721.149   Data:  12 2C BB DB 14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 ...
T82D4 002:721.175 - 0.083ms returns 60 (0x3C)
T82D4 002:721.197 JLINK_ReadMemEx(0x08016134, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.216    -- Read from C cache (2 bytes @ 0x08016134)
T82D4 002:721.244   Data:  12 2C
T82D4 002:721.270 - 0.082ms returns 2 (0x2)
T82D4 002:721.290 JLINK_ReadMemEx(0x08016134, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:721.311    -- Read from C cache (60 bytes @ 0x08016134)
T82D4 002:721.339   Data:  12 2C BB DB 14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 ...
T82D4 002:721.367 - 0.085ms returns 60 (0x3C)
T82D4 002:721.386 JLINK_ReadMemEx(0x08016134, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.406    -- Read from C cache (2 bytes @ 0x08016134)
T82D4 002:721.434   Data:  12 2C
T82D4 002:721.460 - 0.082ms returns 2 (0x2)
T82D4 002:721.481 JLINK_ReadMemEx(0x08016136, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.501    -- Read from C cache (2 bytes @ 0x08016136)
T82D4 002:721.528   Data:  BB DB
T82D4 002:721.556 - 0.083ms returns 2 (0x2)
T82D4 002:721.575 JLINK_ReadMemEx(0x08016136, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.596    -- Read from C cache (2 bytes @ 0x08016136)
T82D4 002:721.622   Data:  BB DB
T82D4 002:721.649 - 0.083ms returns 2 (0x2)
T82D4 002:721.670 JLINK_ReadMemEx(0x08016138, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:721.688    -- Read from C cache (60 bytes @ 0x08016138)
T82D4 002:721.718   Data:  14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 B0 00 00 20 ...
T82D4 002:721.743 - 0.082ms returns 60 (0x3C)
T82D4 002:721.763 JLINK_ReadMemEx(0x08016138, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.783    -- Read from C cache (2 bytes @ 0x08016138)
T82D4 002:721.811   Data:  14 A8
T82D4 002:721.838 - 0.084ms returns 2 (0x2)
T82D4 002:721.858 JLINK_ReadMemEx(0x08016138, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:721.879    -- Read from C cache (60 bytes @ 0x08016138)
T82D4 002:721.907   Data:  14 A8 0F C8 85 E8 0F 00 00 BF F0 E7 B0 00 00 20 ...
T82D4 002:721.933 - 0.084ms returns 60 (0x3C)
T82D4 002:721.954 JLINK_ReadMemEx(0x08016138, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:721.974    -- Read from C cache (2 bytes @ 0x08016138)
T82D4 002:722.007   Data:  14 A8
T82D4 002:722.033 - 0.087ms returns 2 (0x2)
T82D4 002:722.053 JLINK_ReadMemEx(0x0801613A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.073    -- Read from C cache (2 bytes @ 0x0801613A)
T82D4 002:722.100   Data:  0F C8
T82D4 002:722.129 - 0.085ms returns 2 (0x2)
T82D4 002:722.149 JLINK_ReadMemEx(0x0801613A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.170    -- Read from C cache (2 bytes @ 0x0801613A)
T82D4 002:722.197   Data:  0F C8
T82D4 002:722.223 - 0.084ms returns 2 (0x2)
T82D4 002:722.245 JLINK_ReadMemEx(0x0801613C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.264    -- Read from C cache (60 bytes @ 0x0801613C)
T82D4 002:722.293   Data:  85 E8 0F 00 00 BF F0 E7 B0 00 00 20 00 00 00 00 ...
T82D4 002:722.319 - 0.083ms returns 60 (0x3C)
T82D4 002:722.339 JLINK_ReadMemEx(0x0801613C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.359    -- Read from C cache (2 bytes @ 0x0801613C)
T82D4 002:722.387   Data:  85 E8
T82D4 002:722.474 - 0.148ms returns 2 (0x2)
T82D4 002:722.501 JLINK_ReadMemEx(0x0801613C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.527    -- Read from C cache (60 bytes @ 0x0801613C)
T82D4 002:722.562   Data:  85 E8 0F 00 00 BF F0 E7 B0 00 00 20 00 00 00 00 ...
T82D4 002:722.596 - 0.106ms returns 60 (0x3C)
T82D4 002:722.621 JLINK_ReadMemEx(0x0801613C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.649    -- Read from C cache (2 bytes @ 0x0801613C)
T82D4 002:722.682   Data:  85 E8
T82D4 002:722.710 - 0.097ms returns 2 (0x2)
T82D4 002:722.730 JLINK_ReadMemEx(0x0801613E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:722.751    -- Read from C cache (2 bytes @ 0x0801613E)
T82D4 002:722.806   Data:  0F 00
T82D4 002:722.835 - 0.114ms returns 2 (0x2)
T82D4 002:722.856 JLINK_ReadMemEx(0x08016140, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:722.913    -- Read from C cache (60 bytes @ 0x08016140)
T82D4 002:722.941   Data:  00 BF F0 E7 B0 00 00 20 00 00 00 00 00 00 00 00 ...
T82D4 002:722.967 - 0.120ms returns 60 (0x3C)
T82D4 002:722.988 JLINK_ReadMemEx(0x08016140, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.008    -- Read from C cache (2 bytes @ 0x08016140)
T82D4 002:723.074   Data:  00 BF
T82D4 002:723.100 - 0.134ms returns 2 (0x2)
T82D4 002:723.133 JLINK_ReadMemEx(0x08016142, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.153    -- Read from C cache (2 bytes @ 0x08016142)
T82D4 002:723.179   Data:  F0 E7
T82D4 002:723.206 - 0.081ms returns 2 (0x2)
T82D4 002:723.225 JLINK_ReadMemEx(0x08016142, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.245    -- Read from C cache (2 bytes @ 0x08016142)
T82D4 002:723.270   Data:  F0 E7
T82D4 002:723.295 - 0.079ms returns 2 (0x2)
T82D4 002:723.316 JLINK_ReadMemEx(0x08016144, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:723.334    -- Read from C cache (60 bytes @ 0x08016144)
T82D4 002:723.361   Data:  B0 00 00 20 00 00 00 00 00 00 00 00 70 B5 8A 21 ...
T82D4 002:723.386 - 0.079ms returns 60 (0x3C)
T82D4 002:723.405 JLINK_ReadMemEx(0x08016144, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:723.424    -- Read from C cache (2 bytes @ 0x08016144)
T82D4 002:723.450   Data:  B0 00
T82D4 002:723.476 - 0.080ms returns 2 (0x2)
T82D4 002:723.496 JLINK_ReadMemEx(0x08016150, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:723.518   CPU_ReadMem(64 bytes @ 0x08016180)
T82D4 002:724.349    -- Updating C cache (64 bytes @ 0x08016180)
T82D4 002:724.387    -- Read from C cache (60 bytes @ 0x08016150)
T82D4 002:724.413   Data:  70 B5 8A 21 18 48 EE F7 60 F8 94 21 17 48 EE F7 ...
T82D4 002:724.440 - 0.952ms returns 60 (0x3C)
T82D4 002:724.461 JLINK_ReadMemEx(0x08016150, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.482    -- Read from C cache (2 bytes @ 0x08016150)
T82D4 002:724.508   Data:  70 B5
T82D4 002:724.533 - 0.082ms returns 2 (0x2)
T82D4 002:724.554 JLINK_ReadMemEx(0x08016152, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.573    -- Read from C cache (2 bytes @ 0x08016152)
T82D4 002:724.600   Data:  8A 21
T82D4 002:724.624 - 0.078ms returns 2 (0x2)
T82D4 002:724.644 JLINK_ReadMemEx(0x08016152, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.664    -- Read from C cache (2 bytes @ 0x08016152)
T82D4 002:724.689   Data:  8A 21
T82D4 002:724.716 - 0.080ms returns 2 (0x2)
T82D4 002:724.735 JLINK_ReadMemEx(0x08016154, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.755    -- Read from C cache (60 bytes @ 0x08016154)
T82D4 002:724.780   Data:  18 48 EE F7 60 F8 94 21 17 48 EE F7 5C F8 15 4C ...
T82D4 002:724.805 - 0.079ms returns 60 (0x3C)
T82D4 002:724.825 JLINK_ReadMemEx(0x08016154, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:724.843    -- Read from C cache (2 bytes @ 0x08016154)
T82D4 002:724.870   Data:  18 48
T82D4 002:724.895 - 0.078ms returns 2 (0x2)
T82D4 002:724.915 JLINK_ReadMemEx(0x08016154, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:724.934    -- Read from C cache (60 bytes @ 0x08016154)
T82D4 002:724.960   Data:  18 48 EE F7 60 F8 94 21 17 48 EE F7 5C F8 15 4C ...
T82D4 002:724.987 - 0.081ms returns 60 (0x3C)
T82D4 002:725.006 JLINK_ReadMemEx(0x08016154, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.027    -- Read from C cache (2 bytes @ 0x08016154)
T82D4 002:725.055   Data:  18 48
T82D4 002:725.080 - 0.082ms returns 2 (0x2)
T82D4 002:725.100 JLINK_ReadMemEx(0x08016156, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.119    -- Read from C cache (2 bytes @ 0x08016156)
T82D4 002:725.145   Data:  EE F7
T82D4 002:725.195 - 0.106ms returns 2 (0x2)
T82D4 002:725.221 JLINK_ReadMemEx(0x08016156, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.248    -- Read from C cache (2 bytes @ 0x08016156)
T82D4 002:725.283   Data:  EE F7
T82D4 002:725.323 - 0.114ms returns 2 (0x2)
T82D4 002:725.347 JLINK_ReadMemEx(0x08016158, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:725.370    -- Read from C cache (60 bytes @ 0x08016158)
T82D4 002:725.401   Data:  60 F8 94 21 17 48 EE F7 5C F8 15 4C 00 25 16 A1 ...
T82D4 002:725.427 - 0.088ms returns 60 (0x3C)
T82D4 002:725.465 JLINK_ReadMemEx(0x08016158, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.506    -- Read from C cache (2 bytes @ 0x08016158)
T82D4 002:725.549   Data:  60 F8
T82D4 002:725.635 - 0.179ms returns 2 (0x2)
T82D4 002:725.655 JLINK_ReadMemEx(0x0801615A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:725.691    -- Read from C cache (2 bytes @ 0x0801615A)
T82D4 002:725.748   Data:  94 21
T82D4 002:725.782 - 0.152ms returns 2 (0x2)
T82D4 002:725.834 JLINK_ReadMemEx(0x0801615C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:725.896    -- Read from C cache (60 bytes @ 0x0801615C)
T82D4 002:725.926   Data:  17 48 EE F7 5C F8 15 4C 00 25 16 A1 A0 1D EE F7 ...
T82D4 002:726.000 - 0.196ms returns 60 (0x3C)
T82D4 002:726.045 JLINK_ReadMemEx(0x0801615C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.082    -- Read from C cache (2 bytes @ 0x0801615C)
T82D4 002:726.142   Data:  17 48
T82D4 002:726.170 - 0.133ms returns 2 (0x2)
T82D4 002:726.189 JLINK_ReadMemEx(0x0801615C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.209    -- Read from C cache (60 bytes @ 0x0801615C)
T82D4 002:726.234   Data:  17 48 EE F7 5C F8 15 4C 00 25 16 A1 A0 1D EE F7 ...
T82D4 002:726.260 - 0.079ms returns 60 (0x3C)
T82D4 002:726.279 JLINK_ReadMemEx(0x0801615C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.296    -- Read from C cache (2 bytes @ 0x0801615C)
T82D4 002:726.323   Data:  17 48
T82D4 002:726.386 - 0.130ms returns 2 (0x2)
T82D4 002:726.440 JLINK_ReadMemEx(0x0801615E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.459    -- Read from C cache (2 bytes @ 0x0801615E)
T82D4 002:726.521   Data:  EE F7
T82D4 002:726.548 - 0.116ms returns 2 (0x2)
T82D4 002:726.567 JLINK_ReadMemEx(0x0801615E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.586    -- Read from C cache (2 bytes @ 0x0801615E)
T82D4 002:726.612   Data:  EE F7
T82D4 002:726.636 - 0.078ms returns 2 (0x2)
T82D4 002:726.656 JLINK_ReadMemEx(0x08016160, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.675    -- Read from C cache (60 bytes @ 0x08016160)
T82D4 002:726.702   Data:  5C F8 15 4C 00 25 16 A1 A0 1D EE F7 74 F8 01 21 ...
T82D4 002:726.727 - 0.078ms returns 60 (0x3C)
T82D4 002:726.745 JLINK_ReadMemEx(0x08016160, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.764    -- Read from C cache (2 bytes @ 0x08016160)
T82D4 002:726.790   Data:  5C F8
T82D4 002:726.816 - 0.079ms returns 2 (0x2)
T82D4 002:726.835 JLINK_ReadMemEx(0x08016162, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:726.853    -- Read from C cache (2 bytes @ 0x08016162)
T82D4 002:726.879   Data:  15 4C
T82D4 002:726.904 - 0.077ms returns 2 (0x2)
T82D4 002:726.923 JLINK_ReadMemEx(0x08016164, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:726.942    -- Read from C cache (60 bytes @ 0x08016164)
T82D4 002:726.968   Data:  00 25 16 A1 A0 1D EE F7 74 F8 01 21 20 46 81 75 ...
T82D4 002:726.994 - 0.078ms returns 60 (0x3C)
T82D4 002:727.012 JLINK_ReadMemEx(0x08016164, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.032    -- Read from C cache (2 bytes @ 0x08016164)
T82D4 002:727.057   Data:  00 25
T82D4 002:727.082 - 0.078ms returns 2 (0x2)
T82D4 002:727.102 JLINK_ReadMemEx(0x08016164, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.120    -- Read from C cache (60 bytes @ 0x08016164)
T82D4 002:727.151   Data:  00 25 16 A1 A0 1D EE F7 74 F8 01 21 20 46 81 75 ...
T82D4 002:727.177 - 0.083ms returns 60 (0x3C)
T82D4 002:727.197 JLINK_ReadMemEx(0x08016164, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.216    -- Read from C cache (2 bytes @ 0x08016164)
T82D4 002:727.242   Data:  00 25
T82D4 002:727.268 - 0.079ms returns 2 (0x2)
T82D4 002:727.287 JLINK_ReadMemEx(0x08016166, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.306    -- Read from C cache (2 bytes @ 0x08016166)
T82D4 002:727.331   Data:  16 A1
T82D4 002:727.357 - 0.079ms returns 2 (0x2)
T82D4 002:727.377 JLINK_ReadMemEx(0x08016166, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.396    -- Read from C cache (2 bytes @ 0x08016166)
T82D4 002:727.445   Data:  16 A1
T82D4 002:727.487 - 0.118ms returns 2 (0x2)
T82D4 002:727.505 JLINK_ReadMemEx(0x08016168, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.524    -- Read from C cache (60 bytes @ 0x08016168)
T82D4 002:727.550   Data:  A0 1D EE F7 74 F8 01 21 20 46 81 75 C1 75 00 21 ...
T82D4 002:727.576 - 0.079ms returns 60 (0x3C)
T82D4 002:727.595 JLINK_ReadMemEx(0x08016168, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.614    -- Read from C cache (2 bytes @ 0x08016168)
T82D4 002:727.639   Data:  A0 1D
T82D4 002:727.664 - 0.078ms returns 2 (0x2)
T82D4 002:727.685 JLINK_ReadMemEx(0x08016168, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:727.703    -- Read from C cache (60 bytes @ 0x08016168)
T82D4 002:727.730   Data:  A0 1D EE F7 74 F8 01 21 20 46 81 75 C1 75 00 21 ...
T82D4 002:727.755 - 0.078ms returns 60 (0x3C)
T82D4 002:727.773 JLINK_ReadMemEx(0x08016168, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.793    -- Read from C cache (2 bytes @ 0x08016168)
T82D4 002:727.818   Data:  A0 1D
T82D4 002:727.844 - 0.079ms returns 2 (0x2)
T82D4 002:727.863 JLINK_ReadMemEx(0x0801616A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.882    -- Read from C cache (2 bytes @ 0x0801616A)
T82D4 002:727.907   Data:  EE F7
T82D4 002:727.932 - 0.076ms returns 2 (0x2)
T82D4 002:727.952 JLINK_ReadMemEx(0x0801616A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:727.970    -- Read from C cache (2 bytes @ 0x0801616A)
T82D4 002:727.996   Data:  EE F7
T82D4 002:728.022 - 0.078ms returns 2 (0x2)
T82D4 002:728.040 JLINK_ReadMemEx(0x0801616C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:728.060    -- Read from C cache (60 bytes @ 0x0801616C)
T82D4 002:728.086   Data:  74 F8 01 21 20 46 81 75 C1 75 00 21 81 61 C1 61 ...
T82D4 002:728.111 - 0.079ms returns 60 (0x3C)
T82D4 002:728.131 JLINK_ReadMemEx(0x0801616C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.149    -- Read from C cache (2 bytes @ 0x0801616C)
T82D4 002:728.199   Data:  74 F8
T82D4 002:728.241 - 0.119ms returns 2 (0x2)
T82D4 002:728.261 JLINK_ReadMemEx(0x0801616E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.280    -- Read from C cache (2 bytes @ 0x0801616E)
T82D4 002:728.306   Data:  01 21
T82D4 002:728.332 - 0.079ms returns 2 (0x2)
T82D4 002:728.355 JLINK_ReadMemEx(0x08016170, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:728.375    -- Read from C cache (60 bytes @ 0x08016170)
T82D4 002:728.400   Data:  20 46 81 75 C1 75 00 21 81 61 C1 61 01 62 41 62 ...
T82D4 002:728.425 - 0.079ms returns 60 (0x3C)
T82D4 002:728.446 JLINK_ReadMemEx(0x08016170, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.464    -- Read from C cache (2 bytes @ 0x08016170)
T82D4 002:728.491   Data:  20 46
T82D4 002:728.515 - 0.077ms returns 2 (0x2)
T82D4 002:728.534 JLINK_ReadMemEx(0x08016170, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:728.554    -- Read from C cache (60 bytes @ 0x08016170)
T82D4 002:728.580   Data:  20 46 81 75 C1 75 00 21 81 61 C1 61 01 62 41 62 ...
T82D4 002:728.605 - 0.079ms returns 60 (0x3C)
T82D4 002:728.624 JLINK_ReadMemEx(0x08016170, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.643    -- Read from C cache (2 bytes @ 0x08016170)
T82D4 002:728.669   Data:  20 46
T82D4 002:728.693 - 0.079ms returns 2 (0x2)
T82D4 002:728.714 JLINK_ReadMemEx(0x08016172, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.732    -- Read from C cache (2 bytes @ 0x08016172)
T82D4 002:728.761   Data:  81 75
T82D4 002:728.786 - 0.080ms returns 2 (0x2)
T82D4 002:728.805 JLINK_ReadMemEx(0x08016172, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:728.824    -- Read from C cache (2 bytes @ 0x08016172)
T82D4 002:728.849   Data:  81 75
T82D4 002:728.875 - 0.079ms returns 2 (0x2)
T82D4 002:728.895 JLINK_ReadMemEx(0x08016174, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:728.913    -- Read from C cache (60 bytes @ 0x08016174)
T82D4 002:728.940   Data:  C1 75 00 21 81 61 C1 61 01 62 41 62 81 62 C1 62 ...
T82D4 002:728.964 - 0.077ms returns 60 (0x3C)
T82D4 002:728.983 JLINK_ReadMemEx(0x08016174, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.002    -- Read from C cache (2 bytes @ 0x08016174)
T82D4 002:729.028   Data:  C1 75
T82D4 002:729.053 - 0.078ms returns 2 (0x2)
T82D4 002:729.072 JLINK_ReadMemEx(0x08016174, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.092    -- Read from C cache (60 bytes @ 0x08016174)
T82D4 002:729.118   Data:  C1 75 00 21 81 61 C1 61 01 62 41 62 81 62 C1 62 ...
T82D4 002:729.143 - 0.080ms returns 60 (0x3C)
T82D4 002:729.164 JLINK_ReadMemEx(0x08016174, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.182    -- Read from C cache (2 bytes @ 0x08016174)
T82D4 002:729.208   Data:  C1 75
T82D4 002:729.232 - 0.077ms returns 2 (0x2)
T82D4 002:729.252 JLINK_ReadMemEx(0x08016176, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.270    -- Read from C cache (2 bytes @ 0x08016176)
T82D4 002:729.296   Data:  00 21
T82D4 002:729.322 - 0.078ms returns 2 (0x2)
T82D4 002:729.341 JLINK_ReadMemEx(0x08016176, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.360    -- Read from C cache (2 bytes @ 0x08016176)
T82D4 002:729.385   Data:  00 21
T82D4 002:729.411 - 0.079ms returns 2 (0x2)
T82D4 002:729.430 JLINK_ReadMemEx(0x08016178, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.448    -- Read from C cache (60 bytes @ 0x08016178)
T82D4 002:729.476   Data:  81 61 C1 61 01 62 41 62 81 62 C1 62 01 63 41 63 ...
T82D4 002:729.500 - 0.078ms returns 60 (0x3C)
T82D4 002:729.519 JLINK_ReadMemEx(0x08016178, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.538    -- Read from C cache (2 bytes @ 0x08016178)
T82D4 002:729.563   Data:  81 61
T82D4 002:729.590 - 0.080ms returns 2 (0x2)
T82D4 002:729.609 JLINK_ReadMemEx(0x08016178, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.628    -- Read from C cache (60 bytes @ 0x08016178)
T82D4 002:729.654   Data:  81 61 C1 61 01 62 41 62 81 62 C1 62 01 63 41 63 ...
T82D4 002:729.678 - 0.078ms returns 60 (0x3C)
T82D4 002:729.698 JLINK_ReadMemEx(0x08016178, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.716    -- Read from C cache (2 bytes @ 0x08016178)
T82D4 002:729.742   Data:  81 61
T82D4 002:729.767 - 0.077ms returns 2 (0x2)
T82D4 002:729.786 JLINK_ReadMemEx(0x0801617A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.805    -- Read from C cache (2 bytes @ 0x0801617A)
T82D4 002:729.831   Data:  C1 61
T82D4 002:729.857 - 0.079ms returns 2 (0x2)
T82D4 002:729.876 JLINK_ReadMemEx(0x0801617A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:729.895    -- Read from C cache (2 bytes @ 0x0801617A)
T82D4 002:729.921   Data:  C1 61
T82D4 002:729.946 - 0.077ms returns 2 (0x2)
T82D4 002:729.965 JLINK_ReadMemEx(0x0801617C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:729.984    -- Read from C cache (60 bytes @ 0x0801617C)
T82D4 002:730.010   Data:  01 62 41 62 81 62 C1 62 01 63 41 63 10 49 41 64 ...
T82D4 002:730.036 - 0.078ms returns 60 (0x3C)
T82D4 002:730.054 JLINK_ReadMemEx(0x0801617C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.073    -- Read from C cache (2 bytes @ 0x0801617C)
T82D4 002:730.099   Data:  01 62
T82D4 002:730.124 - 0.078ms returns 2 (0x2)
T82D4 002:730.253 JLINK_ReadMemEx(0x0801617C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.285    -- Read from C cache (60 bytes @ 0x0801617C)
T82D4 002:730.312   Data:  01 62 41 62 81 62 C1 62 01 63 41 63 10 49 41 64 ...
T82D4 002:730.337 - 0.092ms returns 60 (0x3C)
T82D4 002:730.355 JLINK_ReadMemEx(0x0801617C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.374    -- Read from C cache (2 bytes @ 0x0801617C)
T82D4 002:730.402   Data:  01 62
T82D4 002:730.427 - 0.081ms returns 2 (0x2)
T82D4 002:730.447 JLINK_ReadMemEx(0x0801617E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.465    -- Read from C cache (2 bytes @ 0x0801617E)
T82D4 002:730.490   Data:  41 62
T82D4 002:730.515 - 0.076ms returns 2 (0x2)
T82D4 002:730.533 JLINK_ReadMemEx(0x0801617E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.552    -- Read from C cache (2 bytes @ 0x0801617E)
T82D4 002:730.577   Data:  41 62
T82D4 002:730.601 - 0.076ms returns 2 (0x2)
T82D4 002:730.620 JLINK_ReadMemEx(0x08016180, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.638    -- Read from C cache (60 bytes @ 0x08016180)
T82D4 002:730.663   Data:  81 62 C1 62 01 63 41 63 10 49 41 64 45 F2 AA 50 ...
T82D4 002:730.688 - 0.076ms returns 60 (0x3C)
T82D4 002:730.705 JLINK_ReadMemEx(0x08016180, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.724    -- Read from C cache (2 bytes @ 0x08016180)
T82D4 002:730.749   Data:  81 62
T82D4 002:730.773 - 0.077ms returns 2 (0x2)
T82D4 002:730.793 JLINK_ReadMemEx(0x08016180, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:730.811    -- Read from C cache (60 bytes @ 0x08016180)
T82D4 002:730.837   Data:  81 62 C1 62 01 63 41 63 10 49 41 64 45 F2 AA 50 ...
T82D4 002:730.863 - 0.078ms returns 60 (0x3C)
T82D4 002:730.880 JLINK_ReadMemEx(0x08016180, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.898    -- Read from C cache (2 bytes @ 0x08016180)
T82D4 002:730.923   Data:  81 62
T82D4 002:730.947 - 0.076ms returns 2 (0x2)
T82D4 002:730.966 JLINK_ReadMemEx(0x08016182, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:730.983    -- Read from C cache (2 bytes @ 0x08016182)
T82D4 002:731.008   Data:  C1 62
T82D4 002:731.034 - 0.076ms returns 2 (0x2)
T82D4 002:731.051 JLINK_ReadMemEx(0x08016182, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.070    -- Read from C cache (2 bytes @ 0x08016182)
T82D4 002:731.095   Data:  C1 62
T82D4 002:731.119 - 0.076ms returns 2 (0x2)
T82D4 002:731.138 JLINK_ReadMemEx(0x08016184, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.160    -- Read from C cache (60 bytes @ 0x08016184)
T82D4 002:731.186   Data:  01 63 41 63 10 49 41 64 45 F2 AA 50 21 46 08 80 ...
T82D4 002:731.211 - 0.080ms returns 60 (0x3C)
T82D4 002:731.228 JLINK_ReadMemEx(0x08016184, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.247    -- Read from C cache (2 bytes @ 0x08016184)
T82D4 002:731.271   Data:  01 63
T82D4 002:731.296 - 0.077ms returns 2 (0x2)
T82D4 002:731.315 JLINK_ReadMemEx(0x08016184, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.332    -- Read from C cache (60 bytes @ 0x08016184)
T82D4 002:731.359   Data:  01 63 41 63 10 49 41 64 45 F2 AA 50 21 46 08 80 ...
T82D4 002:731.383 - 0.076ms returns 60 (0x3C)
T82D4 002:731.400 JLINK_ReadMemEx(0x08016184, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.419    -- Read from C cache (2 bytes @ 0x08016184)
T82D4 002:731.444   Data:  01 63
T82D4 002:731.469 - 0.077ms returns 2 (0x2)
T82D4 002:731.488 JLINK_ReadMemEx(0x08016186, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.505    -- Read from C cache (2 bytes @ 0x08016186)
T82D4 002:731.531   Data:  41 63
T82D4 002:731.555 - 0.075ms returns 2 (0x2)
T82D4 002:731.573 JLINK_ReadMemEx(0x08016186, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:731.592    -- Read from C cache (2 bytes @ 0x08016186)
T82D4 002:731.617   Data:  41 63
T82D4 002:731.642 - 0.078ms returns 2 (0x2)
T82D4 002:731.660 JLINK_ReadMemEx(0x08016188, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:731.680   CPU_ReadMem(64 bytes @ 0x080161C0)
T82D4 002:732.750    -- Updating C cache (64 bytes @ 0x080161C0)
T82D4 002:732.789    -- Read from C cache (60 bytes @ 0x08016188)
T82D4 002:732.815   Data:  10 49 41 64 45 F2 AA 50 21 46 08 80 8A 20 48 80 ...
T82D4 002:732.839 - 1.188ms returns 60 (0x3C)
T82D4 002:732.860 JLINK_ReadMemEx(0x08016188, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:732.879    -- Read from C cache (2 bytes @ 0x08016188)
T82D4 002:732.905   Data:  10 49
T82D4 002:732.930 - 0.078ms returns 2 (0x2)
T82D4 002:732.949 JLINK_ReadMemEx(0x08016188, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:732.970    -- Read from C cache (60 bytes @ 0x08016188)
T82D4 002:732.998   Data:  10 49 41 64 45 F2 AA 50 21 46 08 80 8A 20 48 80 ...
T82D4 002:733.024 - 0.084ms returns 60 (0x3C)
T82D4 002:733.043 JLINK_ReadMemEx(0x08016188, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.061    -- Read from C cache (2 bytes @ 0x08016188)
T82D4 002:733.087   Data:  10 49
T82D4 002:733.112 - 0.077ms returns 2 (0x2)
T82D4 002:733.129 JLINK_ReadMemEx(0x0801618A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.148    -- Read from C cache (2 bytes @ 0x0801618A)
T82D4 002:733.173   Data:  41 64
T82D4 002:733.198 - 0.077ms returns 2 (0x2)
T82D4 002:733.216 JLINK_ReadMemEx(0x0801618A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.233    -- Read from C cache (2 bytes @ 0x0801618A)
T82D4 002:733.260   Data:  41 64
T82D4 002:733.285 - 0.076ms returns 2 (0x2)
T82D4 002:733.302 JLINK_ReadMemEx(0x0801618C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.321    -- Read from C cache (60 bytes @ 0x0801618C)
T82D4 002:733.346   Data:  45 F2 AA 50 21 46 08 80 8A 20 48 80 07 20 88 80 ...
T82D4 002:733.371 - 0.078ms returns 60 (0x3C)
T82D4 002:733.390 JLINK_ReadMemEx(0x0801618C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.407    -- Read from C cache (2 bytes @ 0x0801618C)
T82D4 002:733.433   Data:  45 F2
T82D4 002:733.458 - 0.076ms returns 2 (0x2)
T82D4 002:733.476 JLINK_ReadMemEx(0x0801618C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.495    -- Read from C cache (60 bytes @ 0x0801618C)
T82D4 002:733.520   Data:  45 F2 AA 50 21 46 08 80 8A 20 48 80 07 20 88 80 ...
T82D4 002:733.544 - 0.077ms returns 60 (0x3C)
T82D4 002:733.563 JLINK_ReadMemEx(0x0801618C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.581    -- Read from C cache (2 bytes @ 0x0801618C)
T82D4 002:733.606   Data:  45 F2
T82D4 002:733.631 - 0.076ms returns 2 (0x2)
T82D4 002:733.648 JLINK_ReadMemEx(0x0801618E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.667    -- Read from C cache (2 bytes @ 0x0801618E)
T82D4 002:733.692   Data:  AA 50
T82D4 002:733.717 - 0.078ms returns 2 (0x2)
T82D4 002:733.736 JLINK_ReadMemEx(0x08016190, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:733.772    -- Read from C cache (60 bytes @ 0x08016190)
T82D4 002:733.800   Data:  21 46 08 80 8A 20 48 80 07 20 88 80 00 20 04 E0 ...
T82D4 002:733.823 - 0.095ms returns 60 (0x3C)
T82D4 002:733.841 JLINK_ReadMemEx(0x08016190, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.860    -- Read from C cache (2 bytes @ 0x08016190)
T82D4 002:733.885   Data:  21 46
T82D4 002:733.911 - 0.078ms returns 2 (0x2)
T82D4 002:733.929 JLINK_ReadMemEx(0x08016192, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:733.946    -- Read from C cache (2 bytes @ 0x08016192)
T82D4 002:733.972   Data:  08 80
T82D4 002:733.996 - 0.076ms returns 2 (0x2)
T82D4 002:734.014 JLINK_ReadMemEx(0x08016192, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.033    -- Read from C cache (2 bytes @ 0x08016192)
T82D4 002:734.062   Data:  08 80
T82D4 002:734.088 - 0.082ms returns 2 (0x2)
T82D4 002:734.105 JLINK_ReadMemEx(0x08016194, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.122    -- Read from C cache (60 bytes @ 0x08016194)
T82D4 002:734.150   Data:  8A 20 48 80 07 20 88 80 00 20 04 E0 14 F8 01 2B ...
T82D4 002:734.174 - 0.077ms returns 60 (0x3C)
T82D4 002:734.192 JLINK_ReadMemEx(0x08016194, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.210    -- Read from C cache (2 bytes @ 0x08016194)
T82D4 002:734.236   Data:  8A 20
T82D4 002:734.261 - 0.078ms returns 2 (0x2)
T82D4 002:734.279 JLINK_ReadMemEx(0x08016194, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.296    -- Read from C cache (60 bytes @ 0x08016194)
T82D4 002:734.324   Data:  8A 20 48 80 07 20 88 80 00 20 04 E0 14 F8 01 2B ...
T82D4 002:734.347 - 0.076ms returns 60 (0x3C)
T82D4 002:734.365 JLINK_ReadMemEx(0x08016194, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.383    -- Read from C cache (2 bytes @ 0x08016194)
T82D4 002:734.408   Data:  8A 20
T82D4 002:734.433 - 0.077ms returns 2 (0x2)
T82D4 002:734.451 JLINK_ReadMemEx(0x08016196, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.470    -- Read from C cache (2 bytes @ 0x08016196)
T82D4 002:734.497   Data:  48 80
T82D4 002:734.521 - 0.078ms returns 2 (0x2)
T82D4 002:734.539 JLINK_ReadMemEx(0x08016196, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.558    -- Read from C cache (2 bytes @ 0x08016196)
T82D4 002:734.583   Data:  48 80
T82D4 002:734.608 - 0.078ms returns 2 (0x2)
T82D4 002:734.626 JLINK_ReadMemEx(0x08016198, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.644    -- Read from C cache (60 bytes @ 0x08016198)
T82D4 002:734.670   Data:  07 20 88 80 00 20 04 E0 14 F8 01 2B 51 19 8D B2 ...
T82D4 002:734.714 - 0.096ms returns 60 (0x3C)
T82D4 002:734.732 JLINK_ReadMemEx(0x08016198, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.758    -- Read from C cache (2 bytes @ 0x08016198)
T82D4 002:734.784   Data:  07 20
T82D4 002:734.824 - 0.128ms returns 2 (0x2)
T82D4 002:734.870 JLINK_ReadMemEx(0x08016198, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:734.890    -- Read from C cache (60 bytes @ 0x08016198)
T82D4 002:734.932   Data:  07 20 88 80 00 20 04 E0 14 F8 01 2B 51 19 8D B2 ...
T82D4 002:734.956 - 0.094ms returns 60 (0x3C)
T82D4 002:734.975 JLINK_ReadMemEx(0x08016198, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:734.993    -- Read from C cache (2 bytes @ 0x08016198)
T82D4 002:735.018   Data:  07 20
T82D4 002:735.043 - 0.076ms returns 2 (0x2)
T82D4 002:735.060 JLINK_ReadMemEx(0x0801619A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.079    -- Read from C cache (2 bytes @ 0x0801619A)
T82D4 002:735.105   Data:  88 80
T82D4 002:735.129 - 0.110ms returns 2 (0x2)
T82D4 002:735.183 JLINK_ReadMemEx(0x0801619A, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.217    -- Read from C cache (2 bytes @ 0x0801619A)
T82D4 002:735.262   Data:  88 80
T82D4 002:735.288 - 0.113ms returns 2 (0x2)
T82D4 002:735.305 JLINK_ReadMemEx(0x0801619C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.346    -- Read from C cache (60 bytes @ 0x0801619C)
T82D4 002:735.371   Data:  00 20 04 E0 14 F8 01 2B 51 19 8D B2 40 1C 88 28 ...
T82D4 002:735.396 - 0.100ms returns 60 (0x3C)
T82D4 002:735.415 JLINK_ReadMemEx(0x0801619C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.447    -- Read from C cache (2 bytes @ 0x0801619C)
T82D4 002:735.474   Data:  00 20
T82D4 002:735.500 - 0.092ms returns 2 (0x2)
T82D4 002:735.518 JLINK_ReadMemEx(0x0801619C, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.537    -- Read from C cache (60 bytes @ 0x0801619C)
T82D4 002:735.563   Data:  00 20 04 E0 14 F8 01 2B 51 19 8D B2 40 1C 88 28 ...
T82D4 002:735.587 - 0.077ms returns 60 (0x3C)
T82D4 002:735.605 JLINK_ReadMemEx(0x0801619C, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.624    -- Read from C cache (2 bytes @ 0x0801619C)
T82D4 002:735.650   Data:  00 20
T82D4 002:735.675 - 0.078ms returns 2 (0x2)
T82D4 002:735.693 JLINK_ReadMemEx(0x0801619E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.712    -- Read from C cache (2 bytes @ 0x0801619E)
T82D4 002:735.748   Data:  04 E0
T82D4 002:735.808 - 0.124ms returns 2 (0x2)
T82D4 002:735.830 JLINK_ReadMemEx(0x0801619E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:735.850    -- Read from C cache (2 bytes @ 0x0801619E)
T82D4 002:735.896   Data:  04 E0
T82D4 002:735.921 - 0.099ms returns 2 (0x2)
T82D4 002:735.939 JLINK_ReadMemEx(0x080161A0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:735.962    -- Read from C cache (60 bytes @ 0x080161A0)
T82D4 002:735.989   Data:  14 F8 01 2B 51 19 8D B2 40 1C 88 28 F8 D3 02 48 ...
T82D4 002:736.014 - 0.084ms returns 60 (0x3C)
T82D4 002:736.033 JLINK_ReadMemEx(0x080161A0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.051    -- Read from C cache (2 bytes @ 0x080161A0)
T82D4 002:736.112   Data:  14 F8
T82D4 002:736.161 - 0.138ms returns 2 (0x2)
T82D4 002:736.184 JLINK_ReadMemEx(0x080161A0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:736.207    -- Read from C cache (60 bytes @ 0x080161A0)
T82D4 002:736.279   Data:  14 F8 01 2B 51 19 8D B2 40 1C 88 28 F8 D3 02 48 ...
T82D4 002:736.307 - 0.153ms returns 60 (0x3C)
T82D4 002:736.371 JLINK_ReadMemEx(0x080161A0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.396    -- Read from C cache (2 bytes @ 0x080161A0)
T82D4 002:736.432   Data:  14 F8
T82D4 002:736.499 - 0.137ms returns 2 (0x2)
T82D4 002:736.517 JLINK_ReadMemEx(0x080161A2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.540    -- Read from C cache (2 bytes @ 0x080161A2)
T82D4 002:736.569   Data:  01 2B
T82D4 002:736.601 - 0.092ms returns 2 (0x2)
T82D4 002:736.622 JLINK_ReadMemEx(0x080161A4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:736.642    -- Read from C cache (60 bytes @ 0x080161A4)
T82D4 002:736.674   Data:  51 19 8D B2 40 1C 88 28 F8 D3 02 48 A0 F8 88 50 ...
T82D4 002:736.700 - 0.106ms returns 60 (0x3C)
T82D4 002:736.745 JLINK_ReadMemEx(0x080161A4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.764    -- Read from C cache (2 bytes @ 0x080161A4)
T82D4 002:736.792   Data:  51 19
T82D4 002:736.818 - 0.083ms returns 2 (0x2)
T82D4 002:736.838 JLINK_ReadMemEx(0x080161A6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.856    -- Read from C cache (2 bytes @ 0x080161A6)
T82D4 002:736.883   Data:  8D B2
T82D4 002:736.909 - 0.079ms returns 2 (0x2)
T82D4 002:736.927 JLINK_ReadMemEx(0x080161A6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:736.972    -- Read from C cache (2 bytes @ 0x080161A6)
T82D4 002:736.998   Data:  8D B2
T82D4 002:737.025 - 0.106ms returns 2 (0x2)
T82D4 002:737.061 JLINK_ReadMemEx(0x080161A8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.079    -- Read from C cache (60 bytes @ 0x080161A8)
T82D4 002:737.129   Data:  40 1C 88 28 F8 D3 02 48 A0 F8 88 50 00 20 70 BD ...
T82D4 002:737.154 - 0.100ms returns 60 (0x3C)
T82D4 002:737.172 JLINK_ReadMemEx(0x080161A8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.217    -- Read from C cache (2 bytes @ 0x080161A8)
T82D4 002:737.280   Data:  40 1C
T82D4 002:737.306 - 0.142ms returns 2 (0x2)
T82D4 002:737.324 JLINK_ReadMemEx(0x080161A8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.342    -- Read from C cache (60 bytes @ 0x080161A8)
T82D4 002:737.369   Data:  40 1C 88 28 F8 D3 02 48 A0 F8 88 50 00 20 70 BD ...
T82D4 002:737.393 - 0.076ms returns 60 (0x3C)
T82D4 002:737.410 JLINK_ReadMemEx(0x080161A8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.429    -- Read from C cache (2 bytes @ 0x080161A8)
T82D4 002:737.474   Data:  40 1C
T82D4 002:737.516 - 0.114ms returns 2 (0x2)
T82D4 002:737.534 JLINK_ReadMemEx(0x080161AA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.551    -- Read from C cache (2 bytes @ 0x080161AA)
T82D4 002:737.577   Data:  88 28
T82D4 002:737.602 - 0.075ms returns 2 (0x2)
T82D4 002:737.619 JLINK_ReadMemEx(0x080161AA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.638    -- Read from C cache (2 bytes @ 0x080161AA)
T82D4 002:737.663   Data:  88 28
T82D4 002:737.689 - 0.078ms returns 2 (0x2)
T82D4 002:737.707 JLINK_ReadMemEx(0x080161AC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.724    -- Read from C cache (60 bytes @ 0x080161AC)
T82D4 002:737.753   Data:  F8 D3 02 48 A0 F8 88 50 00 20 70 BD FA 7C 01 20 ...
T82D4 002:737.777 - 0.078ms returns 60 (0x3C)
T82D4 002:737.795 JLINK_ReadMemEx(0x080161AC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.813    -- Read from C cache (2 bytes @ 0x080161AC)
T82D4 002:737.838   Data:  F8 D3
T82D4 002:737.864 - 0.077ms returns 2 (0x2)
T82D4 002:737.882 JLINK_ReadMemEx(0x080161AC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:737.900    -- Read from C cache (60 bytes @ 0x080161AC)
T82D4 002:737.926   Data:  F8 D3 02 48 A0 F8 88 50 00 20 70 BD FA 7C 01 20 ...
T82D4 002:737.950 - 0.076ms returns 60 (0x3C)
T82D4 002:737.968 JLINK_ReadMemEx(0x080161AC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:737.986    -- Read from C cache (2 bytes @ 0x080161AC)
T82D4 002:738.011   Data:  F8 D3
T82D4 002:738.037 - 0.076ms returns 2 (0x2)
T82D4 002:738.054 JLINK_ReadMemEx(0x080161AE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.072    -- Read from C cache (2 bytes @ 0x080161AE)
T82D4 002:738.098   Data:  02 48
T82D4 002:738.122 - 0.076ms returns 2 (0x2)
T82D4 002:738.141 JLINK_ReadMemEx(0x080161AE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.159    -- Read from C cache (2 bytes @ 0x080161AE)
T82D4 002:738.186   Data:  02 48
T82D4 002:738.212 - 0.080ms returns 2 (0x2)
T82D4 002:738.230 JLINK_ReadMemEx(0x080161B0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.248    -- Read from C cache (60 bytes @ 0x080161B0)
T82D4 002:738.274   Data:  A0 F8 88 50 00 20 70 BD FA 7C 01 20 62 95 01 20 ...
T82D4 002:738.298 - 0.076ms returns 60 (0x3C)
T82D4 002:738.316 JLINK_ReadMemEx(0x080161B0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.334    -- Read from C cache (2 bytes @ 0x080161B0)
T82D4 002:738.358   Data:  A0 F8
T82D4 002:738.385 - 0.076ms returns 2 (0x2)
T82D4 002:738.403 JLINK_ReadMemEx(0x080161B0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.421    -- Read from C cache (60 bytes @ 0x080161B0)
T82D4 002:738.447   Data:  A0 F8 88 50 00 20 70 BD FA 7C 01 20 62 95 01 20 ...
T82D4 002:738.471 - 0.076ms returns 60 (0x3C)
T82D4 002:738.489 JLINK_ReadMemEx(0x080161B0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.507    -- Read from C cache (2 bytes @ 0x080161B0)
T82D4 002:738.531   Data:  A0 F8
T82D4 002:738.557 - 0.077ms returns 2 (0x2)
T82D4 002:738.575 JLINK_ReadMemEx(0x080161B2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.593    -- Read from C cache (2 bytes @ 0x080161B2)
T82D4 002:738.618   Data:  88 50
T82D4 002:738.642 - 0.075ms returns 2 (0x2)
T82D4 002:738.661 JLINK_ReadMemEx(0x080161B4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:738.679    -- Read from C cache (60 bytes @ 0x080161B4)
T82D4 002:738.704   Data:  00 20 70 BD FA 7C 01 20 62 95 01 20 49 4E 53 36 ...
T82D4 002:738.814 - 0.184ms returns 60 (0x3C)
T82D4 002:738.857 JLINK_ReadMemEx(0x080161B4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.877    -- Read from C cache (2 bytes @ 0x080161B4)
T82D4 002:738.902   Data:  00 20
T82D4 002:738.928 - 0.079ms returns 2 (0x2)
T82D4 002:738.947 JLINK_ReadMemEx(0x080161B6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:738.965    -- Read from C cache (2 bytes @ 0x080161B6)
T82D4 002:738.991   Data:  70 BD
T82D4 002:739.015 - 0.076ms returns 2 (0x2)
T82D4 002:739.033 JLINK_ReadMemEx(0x080161B6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.053    -- Read from C cache (2 bytes @ 0x080161B6)
T82D4 002:739.078   Data:  70 BD
T82D4 002:739.103 - 0.078ms returns 2 (0x2)
T82D4 002:739.122 JLINK_ReadMemEx(0x080161B8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.140    -- Read from C cache (60 bytes @ 0x080161B8)
T82D4 002:739.166   Data:  FA 7C 01 20 62 95 01 20 49 4E 53 36 32 32 2D 32 ...
T82D4 002:739.191 - 0.077ms returns 60 (0x3C)
T82D4 002:739.208 JLINK_ReadMemEx(0x080161B8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:739.227    -- Read from C cache (2 bytes @ 0x080161B8)
T82D4 002:739.252   Data:  FA 7C
T82D4 002:739.277 - 0.077ms returns 2 (0x2)
T82D4 002:739.296 JLINK_ReadMemEx(0x080161D0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:739.316   CPU_ReadMem(64 bytes @ 0x08016200)
T82D4 002:740.284    -- Updating C cache (64 bytes @ 0x08016200)
T82D4 002:740.343    -- Read from C cache (60 bytes @ 0x080161D0)
T82D4 002:740.370   Data:  08 48 00 68 40 1C 07 49 08 60 08 46 00 68 06 49 ...
T82D4 002:740.394 - 1.107ms returns 60 (0x3C)
T82D4 002:740.414 JLINK_ReadMemEx(0x080161D0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.434    -- Read from C cache (2 bytes @ 0x080161D0)
T82D4 002:740.459   Data:  08 48
T82D4 002:740.484 - 0.078ms returns 2 (0x2)
T82D4 002:740.503 JLINK_ReadMemEx(0x080161D2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.521    -- Read from C cache (2 bytes @ 0x080161D2)
T82D4 002:740.547   Data:  00 68
T82D4 002:740.571 - 0.077ms returns 2 (0x2)
T82D4 002:740.590 JLINK_ReadMemEx(0x080161D2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.609    -- Read from C cache (2 bytes @ 0x080161D2)
T82D4 002:740.634   Data:  00 68
T82D4 002:740.660 - 0.078ms returns 2 (0x2)
T82D4 002:740.678 JLINK_ReadMemEx(0x080161D4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:740.696    -- Read from C cache (60 bytes @ 0x080161D4)
T82D4 002:740.722   Data:  40 1C 07 49 08 60 08 46 00 68 06 49 09 68 40 1A ...
T82D4 002:740.746 - 0.076ms returns 60 (0x3C)
T82D4 002:740.787 JLINK_ReadMemEx(0x080161D4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:740.854    -- Read from C cache (2 bytes @ 0x080161D4)
T82D4 002:740.897   Data:  40 1C
T82D4 002:740.943 - 0.167ms returns 2 (0x2)
T82D4 002:740.971 JLINK_ReadMemEx(0x080161D4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:740.992    -- Read from C cache (60 bytes @ 0x080161D4)
T82D4 002:741.050   Data:  40 1C 07 49 08 60 08 46 00 68 06 49 09 68 40 1A ...
T82D4 002:741.074 - 0.111ms returns 60 (0x3C)
T82D4 002:741.092 JLINK_ReadMemEx(0x080161D4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.110    -- Read from C cache (2 bytes @ 0x080161D4)
T82D4 002:741.135   Data:  40 1C
T82D4 002:741.161 - 0.077ms returns 2 (0x2)
T82D4 002:741.179 JLINK_ReadMemEx(0x080161D6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.197    -- Read from C cache (2 bytes @ 0x080161D6)
T82D4 002:741.223   Data:  07 49
T82D4 002:741.247 - 0.076ms returns 2 (0x2)
T82D4 002:741.265 JLINK_ReadMemEx(0x080161D6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.284    -- Read from C cache (2 bytes @ 0x080161D6)
T82D4 002:741.308   Data:  07 49
T82D4 002:741.334 - 0.077ms returns 2 (0x2)
T82D4 002:741.352 JLINK_ReadMemEx(0x080161D8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.370    -- Read from C cache (60 bytes @ 0x080161D8)
T82D4 002:741.397   Data:  08 60 08 46 00 68 06 49 09 68 40 1A 05 49 88 42 ...
T82D4 002:741.421 - 0.077ms returns 60 (0x3C)
T82D4 002:741.439 JLINK_ReadMemEx(0x080161D8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.457    -- Read from C cache (2 bytes @ 0x080161D8)
T82D4 002:741.481   Data:  08 60
T82D4 002:741.508 - 0.077ms returns 2 (0x2)
T82D4 002:741.525 JLINK_ReadMemEx(0x080161D8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.544    -- Read from C cache (60 bytes @ 0x080161D8)
T82D4 002:741.569   Data:  08 60 08 46 00 68 06 49 09 68 40 1A 05 49 88 42 ...
T82D4 002:741.593 - 0.075ms returns 60 (0x3C)
T82D4 002:741.611 JLINK_ReadMemEx(0x080161D8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.629    -- Read from C cache (2 bytes @ 0x080161D8)
T82D4 002:741.653   Data:  08 60
T82D4 002:741.680 - 0.077ms returns 2 (0x2)
T82D4 002:741.698 JLINK_ReadMemEx(0x080161DA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.716    -- Read from C cache (2 bytes @ 0x080161DA)
T82D4 002:741.742   Data:  08 46
T82D4 002:741.789 - 0.100ms returns 2 (0x2)
T82D4 002:741.824 JLINK_ReadMemEx(0x080161DA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:741.842    -- Read from C cache (2 bytes @ 0x080161DA)
T82D4 002:741.892   Data:  08 46
T82D4 002:741.918 - 0.117ms returns 2 (0x2)
T82D4 002:741.966 JLINK_ReadMemEx(0x080161DC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:741.985    -- Read from C cache (60 bytes @ 0x080161DC)
T82D4 002:742.011   Data:  00 68 06 49 09 68 40 1A 05 49 88 42 03 D3 02 48 ...
T82D4 002:742.079 - 0.123ms returns 60 (0x3C)
T82D4 002:742.114 JLINK_ReadMemEx(0x080161DC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.147    -- Read from C cache (2 bytes @ 0x080161DC)
T82D4 002:742.193   Data:  00 68
T82D4 002:742.235 - 0.129ms returns 2 (0x2)
T82D4 002:742.253 JLINK_ReadMemEx(0x080161DC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:742.272    -- Read from C cache (60 bytes @ 0x080161DC)
T82D4 002:742.297   Data:  00 68 06 49 09 68 40 1A 05 49 88 42 03 D3 02 48 ...
T82D4 002:742.321 - 0.077ms returns 60 (0x3C)
T82D4 002:742.340 JLINK_ReadMemEx(0x080161DC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.358    -- Read from C cache (2 bytes @ 0x080161DC)
T82D4 002:742.383   Data:  00 68
T82D4 002:742.408 - 0.076ms returns 2 (0x2)
T82D4 002:742.426 JLINK_ReadMemEx(0x080161DE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.445    -- Read from C cache (2 bytes @ 0x080161DE)
T82D4 002:742.470   Data:  06 49
T82D4 002:742.494 - 0.077ms returns 2 (0x2)
T82D4 002:742.513 JLINK_ReadMemEx(0x080161DE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.532    -- Read from C cache (2 bytes @ 0x080161DE)
T82D4 002:742.557   Data:  06 49
T82D4 002:742.582 - 0.076ms returns 2 (0x2)
T82D4 002:742.599 JLINK_ReadMemEx(0x080161E0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:742.620    -- Read from C cache (60 bytes @ 0x080161E0)
T82D4 002:742.646   Data:  09 68 40 1A 05 49 88 42 03 D3 02 48 00 68 02 49 ...
T82D4 002:742.670 - 0.079ms returns 60 (0x3C)
T82D4 002:742.688 JLINK_ReadMemEx(0x080161E0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.706    -- Read from C cache (2 bytes @ 0x080161E0)
T82D4 002:742.731   Data:  09 68
T82D4 002:742.778 - 0.098ms returns 2 (0x2)
T82D4 002:742.811 JLINK_ReadMemEx(0x080161E0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:742.830    -- Read from C cache (60 bytes @ 0x080161E0)
T82D4 002:742.855   Data:  09 68 40 1A 05 49 88 42 03 D3 02 48 00 68 02 49 ...
T82D4 002:742.880 - 0.078ms returns 60 (0x3C)
T82D4 002:742.898 JLINK_ReadMemEx(0x080161E0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:742.916    -- Read from C cache (2 bytes @ 0x080161E0)
T82D4 002:742.942   Data:  09 68
T82D4 002:742.967 - 0.076ms returns 2 (0x2)
T82D4 002:742.984 JLINK_ReadMemEx(0x080161E2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.003    -- Read from C cache (2 bytes @ 0x080161E2)
T82D4 002:743.028   Data:  40 1A
T82D4 002:743.053 - 0.078ms returns 2 (0x2)
T82D4 002:743.072 JLINK_ReadMemEx(0x080161E2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.090    -- Read from C cache (2 bytes @ 0x080161E2)
T82D4 002:743.115   Data:  40 1A
T82D4 002:743.140 - 0.076ms returns 2 (0x2)
T82D4 002:743.157 JLINK_ReadMemEx(0x080161E4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:743.176    -- Read from C cache (60 bytes @ 0x080161E4)
T82D4 002:743.202   Data:  05 49 88 42 03 D3 02 48 00 68 02 49 08 60 70 47 ...
T82D4 002:743.227 - 0.078ms returns 60 (0x3C)
T82D4 002:743.246 JLINK_ReadMemEx(0x080161E4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.263    -- Read from C cache (2 bytes @ 0x080161E4)
T82D4 002:743.288   Data:  05 49
T82D4 002:743.313 - 0.075ms returns 2 (0x2)
T82D4 002:743.330 JLINK_ReadMemEx(0x080161E4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:743.350    -- Read from C cache (60 bytes @ 0x080161E4)
T82D4 002:743.375   Data:  05 49 88 42 03 D3 02 48 00 68 02 49 08 60 70 47 ...
T82D4 002:743.400 - 0.078ms returns 60 (0x3C)
T82D4 002:743.418 JLINK_ReadMemEx(0x080161E4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.436    -- Read from C cache (2 bytes @ 0x080161E4)
T82D4 002:743.462   Data:  05 49
T82D4 002:743.486 - 0.076ms returns 2 (0x2)
T82D4 002:743.504 JLINK_ReadMemEx(0x080161E6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.523    -- Read from C cache (2 bytes @ 0x080161E6)
T82D4 002:743.548   Data:  88 42
T82D4 002:743.573 - 0.078ms returns 2 (0x2)
T82D4 002:743.592 JLINK_ReadMemEx(0x080161E6, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.609    -- Read from C cache (2 bytes @ 0x080161E6)
T82D4 002:743.636   Data:  88 42
T82D4 002:743.660 - 0.076ms returns 2 (0x2)
T82D4 002:743.677 JLINK_ReadMemEx(0x080161E8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:743.696    -- Read from C cache (60 bytes @ 0x080161E8)
T82D4 002:743.722   Data:  03 D3 02 48 00 68 02 49 08 60 70 47 FC 05 00 20 ...
T82D4 002:743.747 - 0.100ms returns 60 (0x3C)
T82D4 002:743.788 JLINK_ReadMemEx(0x080161E8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.821    -- Read from C cache (2 bytes @ 0x080161E8)
T82D4 002:743.848   Data:  03 D3
T82D4 002:743.872 - 0.091ms returns 2 (0x2)
T82D4 002:743.889 JLINK_ReadMemEx(0x080161E8, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:743.909    -- Read from C cache (60 bytes @ 0x080161E8)
T82D4 002:743.934   Data:  03 D3 02 48 00 68 02 49 08 60 70 47 FC 05 00 20 ...
T82D4 002:743.960 - 0.078ms returns 60 (0x3C)
T82D4 002:743.977 JLINK_ReadMemEx(0x080161E8, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:743.994    -- Read from C cache (2 bytes @ 0x080161E8)
T82D4 002:744.021   Data:  03 D3
T82D4 002:744.045 - 0.076ms returns 2 (0x2)
T82D4 002:744.062 JLINK_ReadMemEx(0x080161EA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.081    -- Read from C cache (2 bytes @ 0x080161EA)
T82D4 002:744.106   Data:  02 48
T82D4 002:744.132 - 0.078ms returns 2 (0x2)
T82D4 002:744.150 JLINK_ReadMemEx(0x080161EA, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.169    -- Read from C cache (2 bytes @ 0x080161EA)
T82D4 002:744.196   Data:  02 48
T82D4 002:744.220 - 0.078ms returns 2 (0x2)
T82D4 002:744.238 JLINK_ReadMemEx(0x080161EC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:744.256    -- Read from C cache (60 bytes @ 0x080161EC)
T82D4 002:744.281   Data:  00 68 02 49 08 60 70 47 FC 05 00 20 00 06 00 20 ...
T82D4 002:744.307 - 0.077ms returns 60 (0x3C)
T82D4 002:744.325 JLINK_ReadMemEx(0x080161EC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.342    -- Read from C cache (2 bytes @ 0x080161EC)
T82D4 002:744.369   Data:  00 68
T82D4 002:744.393 - 0.075ms returns 2 (0x2)
T82D4 002:744.411 JLINK_ReadMemEx(0x080161EC, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:744.430    -- Read from C cache (60 bytes @ 0x080161EC)
T82D4 002:744.455   Data:  00 68 02 49 08 60 70 47 FC 05 00 20 00 06 00 20 ...
T82D4 002:744.481 - 0.079ms returns 60 (0x3C)
T82D4 002:744.499 JLINK_ReadMemEx(0x080161EC, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.516    -- Read from C cache (2 bytes @ 0x080161EC)
T82D4 002:744.542   Data:  00 68
T82D4 002:744.566 - 0.075ms returns 2 (0x2)
T82D4 002:744.583 JLINK_ReadMemEx(0x080161EE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.602    -- Read from C cache (2 bytes @ 0x080161EE)
T82D4 002:744.627   Data:  02 49
T82D4 002:744.653 - 0.078ms returns 2 (0x2)
T82D4 002:744.671 JLINK_ReadMemEx(0x080161EE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.688    -- Read from C cache (2 bytes @ 0x080161EE)
T82D4 002:744.715   Data:  02 49
T82D4 002:744.739 - 0.098ms returns 2 (0x2)
T82D4 002:744.780 JLINK_ReadMemEx(0x080161F0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:744.813    -- Read from C cache (60 bytes @ 0x080161F0)
T82D4 002:744.838   Data:  08 60 70 47 FC 05 00 20 00 06 00 20 A0 86 01 00 ...
T82D4 002:744.864 - 0.092ms returns 60 (0x3C)
T82D4 002:744.881 JLINK_ReadMemEx(0x080161F0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:744.899    -- Read from C cache (2 bytes @ 0x080161F0)
T82D4 002:744.925   Data:  08 60
T82D4 002:744.949 - 0.076ms returns 2 (0x2)
T82D4 002:744.968 JLINK_ReadMemEx(0x080161F0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:744.987    -- Read from C cache (60 bytes @ 0x080161F0)
T82D4 002:745.012   Data:  08 60 70 47 FC 05 00 20 00 06 00 20 A0 86 01 00 ...
T82D4 002:745.037 - 0.077ms returns 60 (0x3C)
T82D4 002:745.055 JLINK_ReadMemEx(0x080161F0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.073    -- Read from C cache (2 bytes @ 0x080161F0)
T82D4 002:745.098   Data:  08 60
T82D4 002:745.122 - 0.075ms returns 2 (0x2)
T82D4 002:745.141 JLINK_ReadMemEx(0x080161F2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.158    -- Read from C cache (2 bytes @ 0x080161F2)
T82D4 002:745.183   Data:  70 47
T82D4 002:745.209 - 0.077ms returns 2 (0x2)
T82D4 002:745.227 JLINK_ReadMemEx(0x080161F2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.249    -- Read from C cache (2 bytes @ 0x080161F2)
T82D4 002:745.274   Data:  70 47
T82D4 002:745.298 - 0.079ms returns 2 (0x2)
T82D4 002:745.317 JLINK_ReadMemEx(0x080161F4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:745.336    -- Read from C cache (60 bytes @ 0x080161F4)
T82D4 002:745.361   Data:  FC 05 00 20 00 06 00 20 A0 86 01 00 00 25 00 24 ...
T82D4 002:745.387 - 0.078ms returns 60 (0x3C)
T82D4 002:745.404 JLINK_ReadMemEx(0x080161F4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.422    -- Read from C cache (2 bytes @ 0x080161F4)
T82D4 002:745.447   Data:  FC 05
T82D4 002:745.471 - 0.075ms returns 2 (0x2)
T82D4 002:745.497 JLINK_ReadMemEx(0x080161FE, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.515    -- Read from C cache (2 bytes @ 0x080161FE)
T82D4 002:745.540   Data:  01 00
T82D4 002:745.566 - 0.078ms returns 2 (0x2)
T82D4 002:745.584 JLINK_ReadMemEx(0x08016200, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:745.602    -- Read from C cache (60 bytes @ 0x08016200)
T82D4 002:745.628   Data:  00 25 00 24 00 26 F9 F7 EF FB F5 F7 9F FB 2F E0 ...
T82D4 002:745.652 - 0.076ms returns 60 (0x3C)
T82D4 002:745.670 JLINK_ReadMemEx(0x08016200, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.689    -- Read from C cache (2 bytes @ 0x08016200)
T82D4 002:745.715   Data:  00 25
T82D4 002:745.742 - 0.116ms returns 2 (0x2)
T82D4 002:745.827 JLINK_ReadMemEx(0x08016200, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:745.848    -- Read from C cache (60 bytes @ 0x08016200)
T82D4 002:745.874   Data:  00 25 00 24 00 26 F9 F7 EF FB F5 F7 9F FB 2F E0 ...
T82D4 002:745.899 - 0.080ms returns 60 (0x3C)
T82D4 002:745.917 JLINK_ReadMemEx(0x08016200, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:745.936    -- Read from C cache (2 bytes @ 0x08016200)
T82D4 002:745.962   Data:  00 25
T82D4 002:745.986 - 0.077ms returns 2 (0x2)
T82D4 002:746.005 JLINK_ReadMemEx(0x08016202, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:746.023    -- Read from C cache (2 bytes @ 0x08016202)
T82D4 002:746.048   Data:  00 24
T82D4 002:746.074 - 0.078ms returns 2 (0x2)
T82D4 002:746.096 JLINK_ReadMemEx(0x08016202, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:746.115    -- Read from C cache (2 bytes @ 0x08016202)
T82D4 002:746.140   Data:  00 24
T82D4 002:746.164 - 0.077ms returns 2 (0x2)
T82D4 002:746.183 JLINK_ReadMemEx(0x08016204, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:746.201    -- Read from C cache (60 bytes @ 0x08016204)
T82D4 002:746.226   Data:  00 26 F9 F7 EF FB F5 F7 9F FB 2F E0 64 1C 18 48 ...
T82D4 002:746.252 - 0.077ms returns 60 (0x3C)
T82D4 002:746.270 JLINK_ReadMemEx(0x08016204, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:746.288    -- Read from C cache (2 bytes @ 0x08016204)
T82D4 002:746.314   Data:  00 26
T82D4 002:746.338 - 0.076ms returns 2 (0x2)
