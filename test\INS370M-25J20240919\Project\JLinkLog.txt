T82D4 000:011.458   SEGGER J-Link V7.22b Log File
T82D4 000:011.894   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:011.977   Logging started @ 2025-06-11 02:32
T82D4 000:012.017 - 13.229ms
T82D4 000:013.253 JLINK_SetWarnOutHandler(...)
T82D4 000:013.275 - 0.031ms
T82D4 000:013.293 JLINK_OpenEx(...)
T82D4 000:014.974   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:015.311   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:015.710   Decompressing FW timestamp took 193 us
T82D4 000:021.588   Hardware: V9.40
T82D4 000:021.628   S/N: 59406895
T82D4 000:021.660   OEM: SEGGER
T82D4 000:021.689   Feature(s): R<PERSON>, GD<PERSON>, FlashDL, FlashB<PERSON>, J<PERSON><PERSON>
T82D4 000:022.496   TELNET listener socket opened on port 19021
T82D4 000:022.682   WEBSRV Starting webserver
T82D4 000:022.823   WEBSRV Webserver running on local port 19080
T82D4 000:022.858 - 9.573ms returns "O.K."
T82D4 000:022.885 JLINK_GetEmuCaps()
T82D4 000:022.906 - 0.031ms returns 0xB9FF7BBF
T82D4 000:022.930 JLINK_TIF_GetAvailable(...)
T82D4 000:023.058 - 0.143ms
T82D4 000:023.086 JLINK_SetErrorOutHandler(...)
T82D4 000:023.105 - 0.028ms
T82D4 000:023.141 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:033.522   Device "GD32F450II" selected.
T82D4 000:034.157 - 11.036ms returns 0x00
T82D4 000:034.215 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:034.997   Device "GD32F450II" selected.
T82D4 000:035.588 - 1.367ms returns 0x00
T82D4 000:035.623 JLINK_GetHardwareVersion()
T82D4 000:035.643 - 0.029ms returns 94000
T82D4 000:035.662 JLINK_GetDLLVersion()
T82D4 000:035.680 - 0.027ms returns 72202
T82D4 000:035.700 JLINK_GetOEMString(...)
T82D4 000:035.719 JLINK_GetFirmwareString(...)
T82D4 000:035.742 - 0.032ms
T82D4 000:035.773 JLINK_GetDLLVersion()
T82D4 000:035.793 - 0.029ms returns 72202
T82D4 000:035.813 JLINK_GetCompileDateTime()
T82D4 000:035.833 - 0.029ms
T82D4 000:035.857 JLINK_GetFirmwareString(...)
T82D4 000:035.881 - 0.033ms
T82D4 000:035.904 JLINK_GetHardwareVersion()
T82D4 000:035.922 - 0.028ms returns 94000
T82D4 000:035.946 JLINK_GetSN()
T82D4 000:035.965 - 0.028ms returns 59406895
T82D4 000:035.988 JLINK_GetOEMString(...)
T82D4 000:036.021 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:036.464 - 0.460ms returns 0x00
T82D4 000:036.493 JLINK_HasError()
T82D4 000:036.524 JLINK_SetSpeed(2000)
T82D4 000:036.597 - 0.086ms
T82D4 000:036.622 JLINK_GetId()
T82D4 000:037.214   Found SW-DP with ID 0x2BA01477
T82D4 000:039.592   DPIDR: 0x2BA01477
T82D4 000:039.639   Scanning AP map to find all available APs
T82D4 000:040.192   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:040.228   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:040.283   Iterating through AP map to find AHB-AP to use
T82D4 000:041.041   AP[0]: Core found
T82D4 000:041.078   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:041.511   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:041.548   Found Cortex-M4 r0p1, Little endian.
T82D4 000:142.670   -- Max. mem block: 0x00010E60
T82D4 000:143.246   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:144.010   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:144.715   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:145.527   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:145.751   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:146.420   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:147.150   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:147.956   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:148.743   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:149.480   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:150.009   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:150.375   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:150.751   CoreSight components:
T82D4 000:150.788   ROMTbl[0] @ E00FF000
T82D4 000:150.816   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:151.654   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:152.402   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:152.503   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:153.137   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:153.170   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:153.939   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:153.993   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:154.651   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:154.693   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:155.278   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:155.334   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:155.966   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:156.254 - 119.649ms returns 0x2BA01477
T82D4 000:156.288 JLINK_GetDLLVersion()
T82D4 000:156.307 - 0.027ms returns 72202
T82D4 000:156.327 JLINK_CORE_GetFound()
T82D4 000:156.346 - 0.027ms returns 0xE0000FF
T82D4 000:156.366 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:156.401   Value=0xE00FF000
T82D4 000:156.427 - 0.070ms returns 0
T82D4 000:156.511 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:156.533   Value=0xE00FF000
T82D4 000:156.558 - 0.056ms returns 0
T82D4 000:156.577 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:156.596   Value=0x00000000
T82D4 000:156.621 - 0.053ms returns 0
T82D4 000:156.646 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:156.682   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:157.189   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:157.222 - 0.585ms returns 16 (0x10)
T82D4 000:157.243 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:157.261   Value=0x00000000
T82D4 000:157.288 - 0.053ms returns 0
T82D4 000:157.306 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:157.323   Value=0x********
T82D4 000:157.350 - 0.053ms returns 0
T82D4 000:157.368 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:157.386   Value=0x********
T82D4 000:157.412 - 0.052ms returns 0
T82D4 000:157.430 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:157.449   Value=0xE0001000
T82D4 000:157.474 - 0.052ms returns 0
T82D4 000:157.491 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:157.511   Value=0xE0002000
T82D4 000:157.535 - 0.068ms returns 0
T82D4 000:157.569 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:157.588   Value=0xE000E000
T82D4 000:157.613 - 0.051ms returns 0
T82D4 000:157.631 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:157.649   Value=0xE000EDF0
T82D4 000:157.673 - 0.051ms returns 0
T82D4 000:157.693 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:157.711   Value=0x00000001
T82D4 000:157.760 - 0.076ms returns 0
T82D4 000:157.778 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:157.814   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:158.136   Data:  41 C2 0F 41
T82D4 000:158.168   Debug reg: CPUID
T82D4 000:158.193 - 0.424ms returns 1 (0x1)
T82D4 000:158.213 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:158.231   Value=0x00000000
T82D4 000:158.257 - 0.052ms returns 0
T82D4 000:158.275 JLINK_HasError()
T82D4 000:158.294 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:158.312 - 0.027ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:158.331 JLINK_Reset()
T82D4 000:158.359   CPU is running
T82D4 000:158.386   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:158.709   CPU is running
T82D4 000:158.741   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:159.064   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:159.373   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:159.404   CPU is running
T82D4 000:159.431   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:212.625   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:213.013   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:213.383   CPU is running
T82D4 000:213.414   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:213.744   CPU is running
T82D4 000:213.834   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:219.313   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:223.095   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:223.474   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:223.818   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:224.188 - 65.871ms
T82D4 000:224.225 JLINK_HasError()
T82D4 000:224.244 JLINK_ReadReg(R15 (PC))
T82D4 000:224.269 - 0.034ms returns 0x080001C4
T82D4 000:224.289 JLINK_ReadReg(XPSR)
T82D4 000:224.308 - 0.028ms returns 0x01000000
T82D4 000:224.326 JLINK_Halt()
T82D4 000:224.345 - 0.027ms returns 0x00
T82D4 000:224.364 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:224.386   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:224.718   Data:  03 00 03 00
T82D4 000:224.748   Debug reg: DHCSR
T82D4 000:224.772 - 0.417ms returns 1 (0x1)
T82D4 000:224.793 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:224.813   Debug reg: DHCSR
T82D4 000:225.043   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:225.413 - 0.634ms returns 0 (0x00000000)
T82D4 000:225.439 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:225.457   Debug reg: DEMCR
T82D4 000:225.488   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:225.881 - 0.455ms returns 0 (0x00000000)
T82D4 000:225.939 JLINK_GetHWStatus(...)
T82D4 000:226.075 - 0.150ms returns 0
T82D4 000:226.113 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:226.133 - 0.028ms returns 0x06
T82D4 000:226.151 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:226.170 - 0.026ms returns 0x2000
T82D4 000:226.187 JLINK_GetNumWPUnits()
T82D4 000:226.204 - 0.026ms returns 4
T82D4 000:226.231 JLINK_GetSpeed()
T82D4 000:226.250 - 0.026ms returns 2000
T82D4 000:226.325 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:226.350   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:226.648   Data:  02 00 00 00
T82D4 000:226.679 - 0.362ms returns 1 (0x1)
T82D4 000:226.698 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:226.718   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:227.036   Data:  02 00 00 00
T82D4 000:227.068 - 0.393ms returns 1 (0x1)
T82D4 000:227.101 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:227.120   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:227.149   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:227.672 - 0.644ms returns 0x1C
T82D4 000:227.799 JLINK_HasError()
T82D4 000:227.818 JLINK_ReadReg(R15 (PC))
T82D4 000:227.837 - 0.028ms returns 0x080001C4
T82D4 000:227.882 JLINK_ReadReg(XPSR)
T82D4 000:227.922 - 0.050ms returns 0x01000000
T82D4 000:231.572 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:231.614   Data:  00 00 00 00
T82D4 000:231.642   Debug reg: DWT_CYCCNT
T82D4 000:231.667 - 0.103ms returns 4 (0x4)
T82D4 000:304.169 JLINK_HasError()
T82D4 000:304.222 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:304.242 - 0.028ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:304.261 JLINK_Reset()
T82D4 000:304.288   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:304.667   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:305.050   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:305.466   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:305.500   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:358.460   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:358.896   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:359.319   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:359.735   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:365.785   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:369.615   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:370.066   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:370.417   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:370.744 - 66.496ms
T82D4 000:370.808 JLINK_HasError()
T82D4 000:370.830 JLINK_ReadReg(R15 (PC))
T82D4 000:370.850 - 0.029ms returns 0x080001C4
T82D4 000:370.868 JLINK_ReadReg(XPSR)
T82D4 000:370.887 - 0.027ms returns 0x01000000
T82D4 000:371.005 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:371.033   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:371.815    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:371.851    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:371.877   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:371.901 - 0.903ms returns 60 (0x3C)
T82D4 000:371.920 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:371.940    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:371.965   Data:  06 48
T82D4 000:371.991 - 0.079ms returns 2 (0x2)
T82D4 000:372.047 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:372.068    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:372.092   Data:  80 47
T82D4 000:372.118 - 0.080ms returns 2 (0x2)
T82D4 000:372.155 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:372.175    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:372.200   Data:  80 47
T82D4 000:372.224 - 0.077ms returns 2 (0x2)
T82D4 000:372.242 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:372.262   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:373.085    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:373.117    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:373.143   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:373.167 - 0.933ms returns 60 (0x3C)
T82D4 000:373.187 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.206    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:373.246   Data:  06 48
T82D4 000:373.271 - 0.092ms returns 2 (0x2)
T82D4 000:373.295 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:373.315    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:373.342   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:373.367 - 0.080ms returns 60 (0x3C)
T82D4 000:373.385 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.402    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:373.428   Data:  06 48
T82D4 000:373.452 - 0.075ms returns 2 (0x2)
T82D4 000:373.470 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.489    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:373.513   Data:  00 47
T82D4 000:373.538 - 0.077ms returns 2 (0x2)
T82D4 000:373.560 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.578    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:373.604   Data:  00 47
T82D4 000:373.629 - 0.076ms returns 2 (0x2)
T82D4 000:373.646 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:373.665    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:373.690   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:373.721 - 0.084ms returns 60 (0x3C)
T82D4 000:373.740 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.757    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:373.783   Data:  FE E7
T82D4 000:373.808 - 0.075ms returns 2 (0x2)
T82D4 000:373.829 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:373.848    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:373.873   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:373.898 - 0.078ms returns 60 (0x3C)
T82D4 000:373.916 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:373.933    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:373.959   Data:  FE E7
T82D4 000:373.984 - 0.076ms returns 2 (0x2)
T82D4 000:374.001 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:374.020    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:374.045   Data:  FE E7
T82D4 000:374.069 - 0.077ms returns 2 (0x2)
T82D4 001:454.044 JLINK_ReadMemEx(0x08007538, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:454.087   CPU_ReadMem(128 bytes @ 0x08007500)
T82D4 001:455.423    -- Updating C cache (128 bytes @ 0x08007500)
T82D4 001:455.466    -- Read from C cache (60 bytes @ 0x08007538)
T82D4 001:455.495   Data:  1C B5 00 20 01 90 FD F7 0D FE 00 20 00 90 05 E0 ...
T82D4 001:455.522 - 1.545ms returns 60 (0x3C)
T82D4 001:455.606 JLINK_ReadMemEx(0x08007538, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:455.635    -- Read from C cache (2 bytes @ 0x08007538)
T82D4 001:455.667   Data:  1C B5
T82D4 001:455.705 - 0.110ms returns 2 (0x2)
T82D4 001:455.729 JLINK_ReadMemEx(0x0800753A, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:455.753    -- Read from C cache (2 bytes @ 0x0800753A)
T82D4 001:455.788   Data:  00 20
T82D4 001:455.816 - 0.095ms returns 2 (0x2)
T82D4 001:638.516 JLINK_HasError()
T82D4 001:638.574 JLINK_ReadReg(R0)
T82D4 001:638.955 - 0.396ms returns 0x0031F610
T82D4 001:638.982 JLINK_ReadReg(R1)
T82D4 001:639.002 - 0.029ms returns 0x00989680
T82D4 001:639.021 JLINK_ReadReg(R2)
T82D4 001:639.039 - 0.026ms returns 0x00000000
T82D4 001:639.059 JLINK_ReadReg(R3)
T82D4 001:639.077 - 0.027ms returns 0x00000040
T82D4 001:639.096 JLINK_ReadReg(R4)
T82D4 001:639.115 - 0.028ms returns 0x08008DF0
T82D4 001:639.134 JLINK_ReadReg(R5)
T82D4 001:639.153 - 0.028ms returns 0x08008DF0
T82D4 001:639.173 JLINK_ReadReg(R6)
T82D4 001:639.192 - 0.028ms returns 0x00000000
T82D4 001:639.211 JLINK_ReadReg(R7)
T82D4 001:639.230 - 0.027ms returns 0x00000000
T82D4 001:639.248 JLINK_ReadReg(R8)
T82D4 001:639.281 - 0.042ms returns 0x00000000
T82D4 001:639.301 JLINK_ReadReg(R9)
T82D4 001:639.320 - 0.028ms returns 0x20000348
T82D4 001:639.338 JLINK_ReadReg(R10)
T82D4 001:639.356 - 0.027ms returns 0x00000000
T82D4 001:639.376 JLINK_ReadReg(R11)
T82D4 001:639.394 - 0.027ms returns 0x00000000
T82D4 001:639.413 JLINK_ReadReg(R12)
T82D4 001:639.432 - 0.028ms returns 0x000001E0
T82D4 001:639.451 JLINK_ReadReg(R13 (SP))
T82D4 001:639.470 - 0.028ms returns 0x20008038
T82D4 001:639.489 JLINK_ReadReg(R14)
T82D4 001:639.506 - 0.027ms returns 0xFFFFFFFF
T82D4 001:639.527 JLINK_ReadReg(R15 (PC))
T82D4 001:639.545 - 0.027ms returns 0x080001C4
T82D4 001:639.564 JLINK_ReadReg(XPSR)
T82D4 001:639.583 - 0.028ms returns 0x01000000
T82D4 001:639.602 JLINK_ReadReg(MSP)
T82D4 001:639.621 - 0.028ms returns 0x20008038
T82D4 001:639.640 JLINK_ReadReg(PSP)
T82D4 001:639.658 - 0.027ms returns 0x20001000
T82D4 001:639.678 JLINK_ReadReg(CFBP)
T82D4 001:639.696 - 0.027ms returns 0x00000000
T82D4 001:639.714 JLINK_ReadReg(FPSCR)
T82D4 001:645.297 - 5.619ms returns 0x00000000
T82D4 001:645.349 JLINK_ReadReg(FPS0)
T82D4 001:645.372 - 0.032ms returns 0xA3424000
T82D4 001:645.391 JLINK_ReadReg(FPS1)
T82D4 001:645.410 - 0.028ms returns 0xA0D31190
T82D4 001:645.430 JLINK_ReadReg(FPS2)
T82D4 001:645.448 - 0.026ms returns 0x40174000
T82D4 001:645.467 JLINK_ReadReg(FPS3)
T82D4 001:645.486 - 0.027ms returns 0x6A610350
T82D4 001:645.504 JLINK_ReadReg(FPS4)
T82D4 001:645.524 - 0.028ms returns 0x80CD402C
T82D4 001:645.543 JLINK_ReadReg(FPS5)
T82D4 001:645.562 - 0.030ms returns 0x35490280
T82D4 001:645.584 JLINK_ReadReg(FPS6)
T82D4 001:645.601 - 0.026ms returns 0xD8863040
T82D4 001:645.621 JLINK_ReadReg(FPS7)
T82D4 001:645.639 - 0.027ms returns 0x634222A1
T82D4 001:645.658 JLINK_ReadReg(FPS8)
T82D4 001:645.677 - 0.028ms returns 0x0A1008E1
T82D4 001:645.697 JLINK_ReadReg(FPS9)
T82D4 001:645.715 - 0.028ms returns 0x4F607726
T82D4 001:645.735 JLINK_ReadReg(FPS10)
T82D4 001:645.752 - 0.027ms returns 0x710E9008
T82D4 001:645.773 JLINK_ReadReg(FPS11)
T82D4 001:645.792 - 0.028ms returns 0x432091A0
T82D4 001:645.811 JLINK_ReadReg(FPS12)
T82D4 001:645.833 - 0.031ms returns 0x822280C8
T82D4 001:645.852 JLINK_ReadReg(FPS13)
T82D4 001:645.872 - 0.028ms returns 0x1B944171
T82D4 001:645.890 JLINK_ReadReg(FPS14)
T82D4 001:645.908 - 0.027ms returns 0x4502E888
T82D4 001:645.929 JLINK_ReadReg(FPS15)
T82D4 001:645.947 - 0.027ms returns 0x112E0105
T82D4 001:645.966 JLINK_ReadReg(FPS16)
T82D4 001:645.988 - 0.032ms returns 0x80880000
T82D4 001:646.008 JLINK_ReadReg(FPS17)
T82D4 001:646.027 - 0.028ms returns 0x01092210
T82D4 001:646.046 JLINK_ReadReg(FPS18)
T82D4 001:646.064 - 0.027ms returns 0x6FC00247
T82D4 001:646.084 JLINK_ReadReg(FPS19)
T82D4 001:646.102 - 0.027ms returns 0x42109945
T82D4 001:646.122 JLINK_ReadReg(FPS20)
T82D4 001:646.155 - 0.051ms returns 0x02052300
T82D4 001:646.183 JLINK_ReadReg(FPS21)
T82D4 001:646.202 - 0.028ms returns 0x2802CC28
T82D4 001:646.221 JLINK_ReadReg(FPS22)
T82D4 001:646.239 - 0.027ms returns 0x10034492
T82D4 001:646.259 JLINK_ReadReg(FPS23)
T82D4 001:646.278 - 0.028ms returns 0x8970B47C
T82D4 001:646.297 JLINK_ReadReg(FPS24)
T82D4 001:646.316 - 0.029ms returns 0x8624020A
T82D4 001:646.336 JLINK_ReadReg(FPS25)
T82D4 001:646.356 - 0.029ms returns 0xC00C1403
T82D4 001:646.375 JLINK_ReadReg(FPS26)
T82D4 001:646.392 - 0.027ms returns 0x202A04C0
T82D4 001:646.413 JLINK_ReadReg(FPS27)
T82D4 001:646.431 - 0.027ms returns 0x152712A2
T82D4 001:646.450 JLINK_ReadReg(FPS28)
T82D4 001:646.469 - 0.028ms returns 0x545840BB
T82D4 001:646.488 JLINK_ReadReg(FPS29)
T82D4 001:646.507 - 0.028ms returns 0x01482A20
T82D4 001:646.526 JLINK_ReadReg(FPS30)
T82D4 001:646.544 - 0.028ms returns 0x0247A931
T82D4 001:646.564 JLINK_ReadReg(FPS31)
T82D4 001:646.583 - 0.028ms returns 0x64CC89A5
T82D4 001:781.775 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:781.845   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 001:782.725    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 001:782.777    -- Read from C cache (4 bytes @ 0x2000057C)
T82D4 001:782.802   Data:  00 00 00 00
T82D4 001:782.828 - 1.061ms returns 4 (0x4)
T82D4 001:782.926 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:782.952    -- Read from C cache (4 bytes @ 0x2000057C)
T82D4 001:782.995   Data:  00 00 00 00
T82D4 001:783.021 - 0.104ms returns 4 (0x4)
T82D4 001:783.055 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:783.098    -- Read from C cache (4 bytes @ 0x2000057C)
T82D4 001:783.127   Data:  00 00 00 00
T82D4 001:783.168 - 0.122ms returns 4 (0x4)
T82D4 001:792.457 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:792.513   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 001:793.347    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 001:793.387    -- Read from C cache (4 bytes @ 0x20000580)
T82D4 001:793.414   Data:  00 00 00 00
T82D4 001:793.440 - 0.992ms returns 4 (0x4)
T82D4 001:793.476 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:793.500    -- Read from C cache (4 bytes @ 0x20000580)
T82D4 001:793.527   Data:  00 00 00 00
T82D4 001:793.554 - 0.086ms returns 4 (0x4)
T82D4 001:793.585 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:793.606    -- Read from C cache (4 bytes @ 0x20000580)
T82D4 001:793.633   Data:  00 00 00 00
T82D4 001:793.659 - 0.082ms returns 4 (0x4)
T82D4 001:805.274 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:805.365   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:805.716   Data:  38 80 00 20
T82D4 001:805.760 - 0.495ms returns 4 (0x4)
T82D4 001:805.807 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:805.834   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:806.161   Data:  38 80 00 20
T82D4 001:806.196 - 0.399ms returns 4 (0x4)
T82D4 001:806.237 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:806.261   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:806.584   Data:  38 80 00 20
T82D4 001:806.616 - 0.389ms returns 4 (0x4)
T82D4 001:828.107 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:828.348   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:828.795   Data:  38 80 00 20
T82D4 001:828.841 - 0.766ms returns 4 (0x4)
T82D4 001:828.942 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:828.972   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:829.310   Data:  38 80 00 20
T82D4 001:829.343 - 0.410ms returns 4 (0x4)
T82D4 001:829.380 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:829.403   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 001:829.707   Data:  38 80 00 20
T82D4 001:829.738 - 0.366ms returns 4 (0x4)
T5E80 002:237.162 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T5E80 002:237.243    -- Read from C cache (2 bytes @ 0x080001C4)
T5E80 002:237.278   Data:  06 48
T5E80 002:237.306 - 0.154ms returns 2 (0x2)
T5E80 002:237.326 JLINK_HasError()
T5E80 002:237.347 JLINK_SetBPEx(Addr = 0x08007538, Type = 0xFFFFFFF2)
T5E80 002:237.377 - 0.038ms returns 0x00000001
T5E80 002:237.396 JLINK_HasError()
T5E80 002:237.416 JLINK_HasError()
T5E80 002:237.436 JLINK_Go()
T5E80 002:237.801   CPU_WriteMem(4 bytes @ 0xE0002000)
T5E80 002:238.163   CPU_ReadMem(4 bytes @ 0xE0001000)
T5E80 002:238.474   CPU_WriteMem(4 bytes @ 0xE0002008)
T5E80 002:238.508   CPU_WriteMem(4 bytes @ 0xE000200C)
T5E80 002:238.535   CPU_WriteMem(4 bytes @ 0xE0002010)
T5E80 002:238.562   CPU_WriteMem(4 bytes @ 0xE0002014)
T5E80 002:238.589   CPU_WriteMem(4 bytes @ 0xE0002018)
T5E80 002:238.616   CPU_WriteMem(4 bytes @ 0xE000201C)
T5E80 002:240.099   CPU_WriteMem(4 bytes @ 0xE0001004)
T5E80 002:240.896 - 3.476ms
T5E80 002:341.148 JLINK_HasError()
T5E80 002:341.203 JLINK_IsHalted()
T5E80 002:341.572 - 0.382ms returns FALSE
T5E80 002:442.270 JLINK_HasError()
T5E80 002:442.321 JLINK_IsHalted()
T5E80 002:445.912 - 3.615ms returns TRUE
T5E80 002:445.949 JLINK_HasError()
T5E80 002:445.968 JLINK_Halt()
T5E80 002:445.986 - 0.025ms returns 0x00
T5E80 002:446.003 JLINK_IsHalted()
T5E80 002:446.021 - 0.026ms returns TRUE
T5E80 002:446.039 JLINK_IsHalted()
T5E80 002:446.056 - 0.026ms returns TRUE
T5E80 002:446.074 JLINK_IsHalted()
T5E80 002:446.091 - 0.024ms returns TRUE
T5E80 002:446.109 JLINK_HasError()
T5E80 002:446.128 JLINK_ReadReg(R15 (PC))
T5E80 002:446.148 - 0.028ms returns 0x08007538
T5E80 002:446.166 JLINK_ReadReg(XPSR)
T5E80 002:446.183 - 0.025ms returns 0x61000000
T5E80 002:446.204 JLINK_HasError()
T5E80 002:446.223 JLINK_ClrBPEx(BPHandle = 0x00000001)
T5E80 002:446.242 - 0.028ms returns 0x00
T5E80 002:446.260 JLINK_HasError()
T5E80 002:446.277 JLINK_HasError()
T5E80 002:446.296 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T5E80 002:446.319   CPU_ReadMem(4 bytes @ 0xE000ED30)
T5E80 002:446.720   Data:  02 00 00 00
T5E80 002:446.757 - 0.470ms returns 1 (0x1)
T5E80 002:446.777 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T5E80 002:446.800   CPU_ReadMem(4 bytes @ 0xE0001028)
T5E80 002:447.200   Data:  00 00 00 00
T5E80 002:447.251   Debug reg: DWT_FUNC[0]
T5E80 002:447.276 - 0.507ms returns 1 (0x1)
T5E80 002:447.295 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T5E80 002:447.317   CPU_ReadMem(4 bytes @ 0xE0001038)
T5E80 002:447.660   Data:  00 02 00 00
T5E80 002:447.728   Debug reg: DWT_FUNC[1]
T5E80 002:447.754 - 0.468ms returns 1 (0x1)
T5E80 002:447.774 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T5E80 002:447.796   CPU_ReadMem(4 bytes @ 0xE0001048)
T5E80 002:448.115   Data:  00 00 00 00
T5E80 002:448.146   Debug reg: DWT_FUNC[2]
T5E80 002:448.172 - 0.406ms returns 1 (0x1)
T5E80 002:448.190 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T5E80 002:448.211   CPU_ReadMem(4 bytes @ 0xE0001058)
T5E80 002:448.507   Data:  00 00 00 00
T5E80 002:448.538   Debug reg: DWT_FUNC[3]
T5E80 002:448.564 - 0.382ms returns 1 (0x1)
T5E80 002:448.626 JLINK_HasError()
T5E80 002:448.648 JLINK_ReadReg(R0)
T5E80 002:448.667 - 0.027ms returns 0x08007539
T5E80 002:448.685 JLINK_ReadReg(R1)
T5E80 002:448.731 - 0.055ms returns 0x20009658
T5E80 002:448.751 JLINK_ReadReg(R2)
T5E80 002:448.771 - 0.029ms returns 0x00000000
T5E80 002:448.790 JLINK_ReadReg(R3)
T5E80 002:448.825 - 0.065ms returns 0x080066A1
T5E80 002:448.865 JLINK_ReadReg(R4)
T5E80 002:448.898 - 0.063ms returns 0x08008DF0
T5E80 002:448.940 JLINK_ReadReg(R5)
T5E80 002:448.959 - 0.028ms returns 0x08008DF0
T5E80 002:448.992 JLINK_ReadReg(R6)
T5E80 002:449.034 - 0.051ms returns 0x00000000
T5E80 002:449.053 JLINK_ReadReg(R7)
T5E80 002:449.109 - 0.066ms returns 0x00000000
T5E80 002:449.129 JLINK_ReadReg(R8)
T5E80 002:449.163 - 0.064ms returns 0x00000000
T5E80 002:449.205 JLINK_ReadReg(R9)
T5E80 002:449.224 - 0.042ms returns 0x20000348
T5E80 002:449.257 JLINK_ReadReg(R10)
T5E80 002:449.276 - 0.048ms returns 0x00000000
T5E80 002:449.316 JLINK_ReadReg(R11)
T5E80 002:449.349 - 0.042ms returns 0x00000000
T5E80 002:449.394 JLINK_ReadReg(R12)
T5E80 002:449.414 - 0.043ms returns 0x000001E0
T5E80 002:449.448 JLINK_ReadReg(R13 (SP))
T5E80 002:449.487 - 0.048ms returns 0x20009658
T5E80 002:449.521 JLINK_ReadReg(R14)
T5E80 002:449.540 - 0.028ms returns 0x080049CD
T5E80 002:449.579 JLINK_ReadReg(R15 (PC))
T5E80 002:449.599 - 0.029ms returns 0x08007538
T5E80 002:449.634 JLINK_ReadReg(XPSR)
T5E80 002:449.674 - 0.049ms returns 0x61000000
T5E80 002:449.697 JLINK_ReadReg(MSP)
T5E80 002:449.753 - 0.065ms returns 0x20009658
T5E80 002:449.772 JLINK_ReadReg(PSP)
T5E80 002:449.807 - 0.067ms returns 0x20001000
T5E80 002:449.849 JLINK_ReadReg(CFBP)
T5E80 002:449.869 - 0.029ms returns 0x00000000
T5E80 002:449.889 JLINK_ReadReg(FPSCR)
T5E80 002:455.490 - 5.619ms returns 0x00000000
T5E80 002:455.522 JLINK_ReadReg(FPS0)
T5E80 002:455.566 - 0.053ms returns 0xA3424000
T5E80 002:455.601 JLINK_ReadReg(FPS1)
T5E80 002:455.643 - 0.051ms returns 0xA0D31190
T5E80 002:455.677 JLINK_ReadReg(FPS2)
T5E80 002:455.719 - 0.051ms returns 0x40174000
T5E80 002:455.755 JLINK_ReadReg(FPS3)
T5E80 002:455.796 - 0.050ms returns 0x6A610350
T5E80 002:455.831 JLINK_ReadReg(FPS4)
T5E80 002:455.871 - 0.049ms returns 0x80CD402C
T5E80 002:455.892 JLINK_ReadReg(FPS5)
T5E80 002:455.946 - 0.064ms returns 0x35490280
T5E80 002:455.968 JLINK_ReadReg(FPS6)
T5E80 002:456.023 - 0.064ms returns 0xD8863040
T5E80 002:456.044 JLINK_ReadReg(FPS7)
T5E80 002:456.063 - 0.028ms returns 0x634222A1
T5E80 002:456.083 JLINK_ReadReg(FPS8)
T5E80 002:456.103 - 0.029ms returns 0x0A1008E1
T5E80 002:456.123 JLINK_ReadReg(FPS9)
T5E80 002:456.143 - 0.029ms returns 0x4F607726
T5E80 002:456.163 JLINK_ReadReg(FPS10)
T5E80 002:456.183 - 0.029ms returns 0x710E9008
T5E80 002:456.203 JLINK_ReadReg(FPS11)
T5E80 002:456.223 - 0.029ms returns 0x432091A0
T5E80 002:456.243 JLINK_ReadReg(FPS12)
T5E80 002:456.263 - 0.029ms returns 0x822280C8
T5E80 002:456.284 JLINK_ReadReg(FPS13)
T5E80 002:456.302 - 0.028ms returns 0x1B944171
T5E80 002:456.324 JLINK_ReadReg(FPS14)
T5E80 002:456.343 - 0.027ms returns 0x4502E888
T5E80 002:456.363 JLINK_ReadReg(FPS15)
T5E80 002:456.383 - 0.028ms returns 0x112E0105
T5E80 002:456.403 JLINK_ReadReg(FPS16)
T5E80 002:456.423 - 0.030ms returns 0x80880000
T5E80 002:456.444 JLINK_ReadReg(FPS17)
T5E80 002:456.479 - 0.043ms returns 0x01092210
T5E80 002:456.498 JLINK_ReadReg(FPS18)
T5E80 002:456.517 - 0.028ms returns 0x6FC00247
T5E80 002:456.536 JLINK_ReadReg(FPS19)
T5E80 002:456.555 - 0.027ms returns 0x42109945
T5E80 002:456.574 JLINK_ReadReg(FPS20)
T5E80 002:456.593 - 0.028ms returns 0x02052300
T5E80 002:456.613 JLINK_ReadReg(FPS21)
T5E80 002:456.631 - 0.028ms returns 0x2802CC28
T5E80 002:456.651 JLINK_ReadReg(FPS22)
T5E80 002:456.669 - 0.027ms returns 0x10034492
T5E80 002:456.690 JLINK_ReadReg(FPS23)
T5E80 002:456.708 - 0.026ms returns 0x8970B47C
T5E80 002:456.728 JLINK_ReadReg(FPS24)
T5E80 002:456.746 - 0.027ms returns 0x8624020A
T5E80 002:456.765 JLINK_ReadReg(FPS25)
T5E80 002:456.785 - 0.028ms returns 0xC00C1403
T5E80 002:456.804 JLINK_ReadReg(FPS26)
T5E80 002:456.823 - 0.028ms returns 0x202A04C0
T5E80 002:456.844 JLINK_ReadReg(FPS27)
T5E80 002:456.863 - 0.028ms returns 0x152712A2
T5E80 002:456.882 JLINK_ReadReg(FPS28)
T5E80 002:456.901 - 0.028ms returns 0x545840BB
T5E80 002:456.920 JLINK_ReadReg(FPS29)
T5E80 002:456.939 - 0.028ms returns 0x01482A20
T5E80 002:456.959 JLINK_ReadReg(FPS30)
T5E80 002:456.977 - 0.027ms returns 0x0247A931
T5E80 002:456.998 JLINK_ReadReg(FPS31)
T5E80 002:457.016 - 0.026ms returns 0x64CC89A5
T82D4 002:457.166 JLINK_ReadMemEx(0x2000964C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:457.206   CPU_ReadMem(64 bytes @ 0x20009640)
T82D4 002:458.286    -- Updating C cache (64 bytes @ 0x20009640)
T82D4 002:458.324    -- Read from C cache (4 bytes @ 0x2000964C)
T82D4 002:458.350   Data:  00 00 00 00
T82D4 002:458.376 - 1.219ms returns 4 (0x4)
T82D4 002:458.398 JLINK_ReadMemEx(0x20009648, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:458.420    -- Read from C cache (4 bytes @ 0x20009648)
T82D4 002:458.446   Data:  00 00 00 00
T82D4 002:458.530 - 0.144ms returns 4 (0x4)
T82D4 002:459.697 JLINK_ReadMemEx(0x2000964C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:459.731    -- Read from C cache (4 bytes @ 0x2000964C)
T82D4 002:459.757   Data:  00 00 00 00
T82D4 002:459.782 - 0.093ms returns 4 (0x4)
T82D4 002:459.801 JLINK_ReadMemEx(0x20009648, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:459.821    -- Read from C cache (4 bytes @ 0x20009648)
T82D4 002:459.847   Data:  00 00 00 00
T82D4 002:459.871 - 0.077ms returns 4 (0x4)
T82D4 002:459.902 JLINK_HasError()
T82D4 002:459.923 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:459.944   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:460.293   Data:  18 8C 11 00
T82D4 002:460.347   Debug reg: DWT_CYCCNT
T82D4 002:460.373 - 0.458ms returns 1 (0x1)
T82D4 002:465.620 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:465.669   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:466.501    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:466.627    -- Read from C cache (4 bytes @ 0x2000057C)
T82D4 002:466.653   Data:  00 00 00 00
T82D4 002:466.679 - 1.067ms returns 4 (0x4)
T82D4 002:466.707 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:466.731   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:467.555    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:467.691    -- Read from C cache (4 bytes @ 0x20000580)
T82D4 002:467.717   Data:  00 00 00 00
T82D4 002:467.742 - 1.043ms returns 4 (0x4)
T82D4 002:467.767 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:467.791   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 002:468.114   Data:  38 80 00 20
T82D4 002:468.145 - 0.386ms returns 4 (0x4)
T82D4 002:468.373 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:468.404   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 002:468.717   Data:  38 80 00 20
T82D4 002:468.763 - 0.398ms returns 4 (0x4)
T5E80 003:556.146 JLINK_ReadMemEx(0x08007538, 0x2 Bytes, Flags = 0x02000000)
T5E80 003:556.207   CPU_ReadMem(64 bytes @ 0x08007500)
T5E80 003:557.097    -- Updating C cache (64 bytes @ 0x08007500)
T5E80 003:557.149    -- Read from C cache (2 bytes @ 0x08007538)
T5E80 003:557.175   Data:  1C B5
T5E80 003:557.201 - 1.064ms returns 2 (0x2)
T5E80 003:557.221 JLINK_HasError()
T5E80 003:557.241 JLINK_HasError()
T5E80 003:557.260 JLINK_Go()
T5E80 003:557.626   CPU_ReadMem(4 bytes @ 0xE0001000)
T5E80 003:558.046   CPU_WriteMem(4 bytes @ 0xE0002008)
T5E80 003:558.917 - 1.672ms
T5E80 003:659.654 JLINK_HasError()
T5E80 003:659.759 JLINK_IsHalted()
T5E80 003:660.181 - 0.444ms returns FALSE
T5E80 003:761.192 JLINK_HasError()
T5E80 003:761.311 JLINK_IsHalted()
T5E80 003:761.812 - 0.525ms returns FALSE
T5E80 003:862.266 JLINK_HasError()
T5E80 003:862.398 JLINK_IsHalted()
T5E80 003:862.789 - 0.411ms returns FALSE
T5E80 003:962.997 JLINK_HasError()
T5E80 003:963.062 JLINK_IsHalted()
T5E80 003:963.439 - 0.400ms returns FALSE
T5E80 004:063.878 JLINK_HasError()
T5E80 004:064.106 JLINK_IsHalted()
T5E80 004:064.540 - 0.455ms returns FALSE
T5E80 004:164.666 JLINK_HasError()
T5E80 004:164.789 JLINK_HasError()
T5E80 004:164.808 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 004:164.836   CPU_ReadMem(4 bytes @ 0xE0001004)
T5E80 004:165.178   Data:  E5 A9 BD 08
T5E80 004:165.210   Debug reg: DWT_CYCCNT
T5E80 004:165.234 - 0.434ms returns 1 (0x1)
T82D4 004:169.859 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:169.899   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 004:170.309   Data:  00 00 00 00
T82D4 004:170.346 - 0.495ms returns 4 (0x4)
T82D4 004:170.374 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:170.398   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 004:170.774   Data:  00 00 00 00
T82D4 004:170.810 - 0.445ms returns 4 (0x4)
T82D4 004:170.837 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:170.861   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 004:171.235   Data:  38 80 00 20
T82D4 004:171.272 - 0.443ms returns 4 (0x4)
T82D4 004:171.497 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:171.532   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 004:171.915   Data:  38 80 00 20
T82D4 004:171.952 - 0.463ms returns 4 (0x4)
T5E80 004:183.985 JLINK_IsHalted()
T5E80 004:184.603 - 0.656ms returns FALSE
T5E80 004:284.990 JLINK_HasError()
T5E80 004:285.128 JLINK_IsHalted()
T5E80 004:285.513 - 0.422ms returns FALSE
T5E80 004:386.101 JLINK_HasError()
T5E80 004:386.163 JLINK_IsHalted()
T5E80 004:386.511 - 0.366ms returns FALSE
T5E80 004:487.199 JLINK_HasError()
T5E80 004:487.259 JLINK_IsHalted()
T5E80 004:487.610 - 0.375ms returns FALSE
T5E80 004:588.574 JLINK_HasError()
T5E80 004:588.637 JLINK_IsHalted()
T5E80 004:589.081 - 0.465ms returns FALSE
T5E80 004:689.763 JLINK_HasError()
T5E80 004:689.824 JLINK_HasError()
T5E80 004:689.842 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 004:689.870   Data:  E5 A9 BD 08
T5E80 004:689.896   Debug reg: DWT_CYCCNT
T5E80 004:689.920 - 0.086ms returns 1 (0x1)
T82D4 004:695.377 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:695.434   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 004:695.883   Data:  00 00 00 00
T82D4 004:695.921 - 0.553ms returns 4 (0x4)
T82D4 004:695.950 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:695.973   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 004:696.410   Data:  00 00 00 00
T82D4 004:696.447 - 0.505ms returns 4 (0x4)
T82D4 004:696.474 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:696.497   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 004:696.862   Data:  38 80 00 20
T82D4 004:696.893 - 0.428ms returns 4 (0x4)
T82D4 004:697.117 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 004:697.146   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 004:697.512   Data:  38 80 00 20
T82D4 004:697.542 - 0.433ms returns 4 (0x4)
T5E80 004:710.298 JLINK_IsHalted()
T5E80 004:710.714 - 0.435ms returns FALSE
T5E80 004:811.306 JLINK_HasError()
T5E80 004:811.828 JLINK_IsHalted()
T5E80 004:812.318 - 0.511ms returns FALSE
T5E80 004:913.187 JLINK_HasError()
T5E80 004:913.249 JLINK_IsHalted()
T5E80 004:913.629 - 0.402ms returns FALSE
T5E80 005:014.345 JLINK_HasError()
T5E80 005:014.516 JLINK_IsHalted()
T5E80 005:015.013 - 0.514ms returns FALSE
T5E80 005:115.996 JLINK_HasError()
T5E80 005:116.140 JLINK_IsHalted()
T5E80 005:116.522 - 0.395ms returns FALSE
T5E80 005:217.149 JLINK_HasError()
T5E80 005:217.358 JLINK_HasError()
T5E80 005:217.451 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 005:217.570   Data:  E5 A9 BD 08
T5E80 005:217.699   Debug reg: DWT_CYCCNT
T5E80 005:217.827 - 0.417ms returns 1 (0x1)
T82D4 005:240.139 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:240.196   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 005:240.635   Data:  00 00 00 00
T82D4 005:240.673 - 0.543ms returns 4 (0x4)
T82D4 005:240.701 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:240.724   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 005:241.157   Data:  00 00 00 00
T82D4 005:241.195 - 0.502ms returns 4 (0x4)
T82D4 005:241.221 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:241.244   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 005:241.667   Data:  38 80 00 20
T82D4 005:241.704 - 0.492ms returns 4 (0x4)
T82D4 005:241.928 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:241.959   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 005:242.369   Data:  38 80 00 20
T82D4 005:242.425 - 0.505ms returns 4 (0x4)
T5E80 005:255.504 JLINK_IsHalted()
T5E80 005:255.960 - 0.476ms returns FALSE
T5E80 005:356.270 JLINK_HasError()
T5E80 005:356.475 JLINK_IsHalted()
T5E80 005:357.321 - 0.989ms returns FALSE
T5E80 005:457.838 JLINK_HasError()
T5E80 005:457.906 JLINK_IsHalted()
T5E80 005:459.033 - 1.193ms returns FALSE
T5E80 005:559.920 JLINK_HasError()
T5E80 005:559.981 JLINK_IsHalted()
T5E80 005:560.355 - 0.387ms returns FALSE
T5E80 005:660.472 JLINK_HasError()
T5E80 005:660.529 JLINK_IsHalted()
T5E80 005:660.882 - 0.369ms returns FALSE
T5E80 005:761.875 JLINK_HasError()
T5E80 005:762.058 JLINK_HasError()
T5E80 005:762.094 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 005:762.126   Data:  E5 A9 BD 08
T5E80 005:762.156   Debug reg: DWT_CYCCNT
T5E80 005:762.183 - 0.097ms returns 1 (0x1)
T82D4 005:767.538 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:767.586   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 005:768.044   Data:  00 00 00 00
T82D4 005:768.077 - 0.547ms returns 4 (0x4)
T82D4 005:768.104 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:768.129   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 005:768.469   Data:  00 00 00 00
T82D4 005:768.502 - 0.405ms returns 4 (0x4)
T82D4 005:768.526 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:768.549   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 005:768.883   Data:  38 80 00 20
T82D4 005:768.915 - 0.398ms returns 4 (0x4)
T82D4 005:769.136 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 005:769.167   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 005:769.526   Data:  38 80 00 20
T82D4 005:769.558 - 0.430ms returns 4 (0x4)
T5E80 005:782.806 JLINK_IsHalted()
T5E80 005:783.253 - 0.470ms returns FALSE
T5E80 005:883.306 JLINK_HasError()
T5E80 005:883.361 JLINK_IsHalted()
T5E80 005:883.757 - 0.408ms returns FALSE
T5E80 005:984.039 JLINK_HasError()
T5E80 005:984.133 JLINK_IsHalted()
T5E80 005:984.507 - 0.388ms returns FALSE
T5E80 006:084.989 JLINK_HasError()
T5E80 006:085.053 JLINK_IsHalted()
T5E80 006:085.456 - 0.423ms returns FALSE
T5E80 006:186.358 JLINK_HasError()
T5E80 006:186.415 JLINK_IsHalted()
T5E80 006:186.782 - 0.381ms returns FALSE
T5E80 006:287.763 JLINK_HasError()
T5E80 006:287.821 JLINK_HasError()
T5E80 006:287.841 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 006:287.870   Data:  E5 A9 BD 08
T5E80 006:287.899   Debug reg: DWT_CYCCNT
T5E80 006:287.926 - 0.093ms returns 1 (0x1)
T82D4 006:293.940 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:294.006   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 006:294.373   Data:  00 00 00 00
T82D4 006:294.410 - 0.479ms returns 4 (0x4)
T82D4 006:294.440 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:294.467   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 006:294.784   Data:  00 00 00 00
T82D4 006:294.829 - 0.412ms returns 4 (0x4)
T82D4 006:294.897 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:294.942   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 006:295.305   Data:  38 80 00 20
T82D4 006:295.342 - 0.457ms returns 4 (0x4)
T82D4 006:295.599 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:295.634   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 006:296.011   Data:  38 80 00 20
T82D4 006:296.045 - 0.456ms returns 4 (0x4)
T5E80 006:308.984 JLINK_IsHalted()
T5E80 006:309.469 - 0.512ms returns FALSE
T5E80 006:410.388 JLINK_HasError()
T5E80 006:410.448 JLINK_IsHalted()
T5E80 006:410.867 - 0.438ms returns FALSE
T5E80 006:511.039 JLINK_HasError()
T5E80 006:511.172 JLINK_IsHalted()
T5E80 006:511.678 - 0.539ms returns FALSE
T5E80 006:612.195 JLINK_HasError()
T5E80 006:612.268 JLINK_IsHalted()
T5E80 006:612.611 - 0.365ms returns FALSE
T5E80 006:713.306 JLINK_HasError()
T5E80 006:713.370 JLINK_IsHalted()
T5E80 006:713.721 - 0.366ms returns FALSE
T5E80 006:814.646 JLINK_HasError()
T5E80 006:814.708 JLINK_HasError()
T5E80 006:814.730 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 006:814.759   Data:  E5 A9 BD 08
T5E80 006:814.790   Debug reg: DWT_CYCCNT
T5E80 006:814.818 - 0.097ms returns 1 (0x1)
T82D4 006:820.721 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:820.789   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 006:821.245   Data:  00 00 00 00
T82D4 006:821.295 - 0.583ms returns 4 (0x4)
T82D4 006:821.327 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:821.356   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 006:821.702   Data:  00 00 00 00
T82D4 006:821.747 - 0.432ms returns 4 (0x4)
T82D4 006:821.782 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:821.812   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 006:822.136   Data:  38 80 00 20
T82D4 006:822.175 - 0.402ms returns 4 (0x4)
T82D4 006:822.467 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 006:822.501   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 006:822.815   Data:  38 80 00 20
T82D4 006:822.849 - 0.391ms returns 4 (0x4)
T5E80 006:835.125 JLINK_IsHalted()
T5E80 006:835.634 - 0.525ms returns FALSE
T5E80 006:935.727 JLINK_HasError()
T5E80 006:935.860 JLINK_IsHalted()
T5E80 006:936.253 - 0.407ms returns FALSE
T5E80 007:036.501 JLINK_HasError()
T5E80 007:036.552 JLINK_IsHalted()
T5E80 007:036.981 - 0.448ms returns FALSE
T5E80 007:137.925 JLINK_HasError()
T5E80 007:137.983 JLINK_IsHalted()
T5E80 007:138.327 - 0.366ms returns FALSE
T5E80 007:239.347 JLINK_HasError()
T5E80 007:239.494 JLINK_IsHalted()
T5E80 007:239.865 - 0.400ms returns FALSE
T5E80 007:340.785 JLINK_HasError()
T5E80 007:340.832 JLINK_HasError()
T5E80 007:340.854 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 007:340.882   Data:  E5 A9 BD 08
T5E80 007:340.909   Debug reg: DWT_CYCCNT
T5E80 007:340.936 - 0.091ms returns 1 (0x1)
T82D4 007:346.933 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:346.999   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 007:347.328   Data:  00 00 00 00
T82D4 007:347.369 - 0.445ms returns 4 (0x4)
T82D4 007:347.399 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:347.426   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 007:347.782   Data:  00 00 00 00
T82D4 007:347.840 - 0.464ms returns 4 (0x4)
T82D4 007:347.897 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:347.920   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 007:348.235   Data:  38 80 00 20
T82D4 007:348.268 - 0.378ms returns 4 (0x4)
T82D4 007:348.497 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:348.530   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 007:348.848   Data:  38 80 00 20
T82D4 007:348.894 - 0.408ms returns 4 (0x4)
T5E80 007:363.342 JLINK_IsHalted()
T5E80 007:363.850 - 0.533ms returns FALSE
T5E80 007:463.988 JLINK_HasError()
T5E80 007:464.046 JLINK_IsHalted()
T5E80 007:464.383 - 0.360ms returns FALSE
T5E80 007:565.191 JLINK_HasError()
T5E80 007:565.403 JLINK_IsHalted()
T5E80 007:565.883 - 0.496ms returns FALSE
T5E80 007:666.633 JLINK_HasError()
T5E80 007:666.745 JLINK_IsHalted()
T5E80 007:667.125 - 0.410ms returns FALSE
T82D4 007:757.921 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:758.038   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 007:758.416   Data:  00 00
T82D4 007:758.466 - 0.555ms returns 2 (0x2)
T82D4 007:758.495 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:758.524   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 007:758.871   Data:  00 00
T82D4 007:758.914 - 0.428ms returns 2 (0x2)
T82D4 007:758.939 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:758.964   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 007:759.271   Data:  00 00
T82D4 007:759.312 - 0.382ms returns 2 (0x2)
T82D4 007:759.337 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:759.361   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 007:759.684   Data:  00 00
T82D4 007:759.722 - 0.393ms returns 2 (0x2)
T82D4 007:759.747 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:759.774   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 007:760.114   Data:  00 00
T82D4 007:760.148 - 0.410ms returns 2 (0x2)
T82D4 007:760.171 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:760.197   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 007:760.508   Data:  00 00
T82D4 007:760.541 - 0.378ms returns 2 (0x2)
T82D4 007:760.563 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:760.588   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 007:760.907   Data:  00 00
T82D4 007:760.941 - 0.386ms returns 2 (0x2)
T82D4 007:760.963 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:760.987   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 007:761.306   Data:  00 00
T82D4 007:761.348 - 0.393ms returns 2 (0x2)
T82D4 007:761.371 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:761.397   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 007:761.704   Data:  00 00
T82D4 007:761.750 - 0.391ms returns 2 (0x2)
T82D4 007:761.781 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:761.813   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 007:762.134   Data:  00 00
T82D4 007:762.168 - 0.396ms returns 2 (0x2)
T82D4 007:762.191 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:762.215   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 007:762.534   Data:  00 00
T82D4 007:762.570 - 0.391ms returns 2 (0x2)
T82D4 007:762.600 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:762.625   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 007:763.010   Data:  00 00
T82D4 007:763.044 - 0.455ms returns 2 (0x2)
T82D4 007:763.069 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:763.096   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 007:763.406   Data:  00 00
T82D4 007:763.441 - 0.383ms returns 2 (0x2)
T82D4 007:763.467 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:763.491   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 007:763.807   Data:  00 00
T82D4 007:763.843 - 0.384ms returns 2 (0x2)
T82D4 007:763.865 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:763.887   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 007:764.236   Data:  00 00 00 00
T82D4 007:764.270 - 0.415ms returns 4 (0x4)
T82D4 007:764.297 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:764.321   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 007:764.633   Data:  00 00 00 00
T82D4 007:764.668 - 0.380ms returns 4 (0x4)
T82D4 007:764.694 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:764.717   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 007:765.037   Data:  00 00 00 00
T82D4 007:765.070 - 0.385ms returns 4 (0x4)
T82D4 007:765.093 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:765.116   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 007:765.433   Data:  00 00
T82D4 007:765.468 - 0.385ms returns 2 (0x2)
T82D4 007:765.494 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:765.519   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 007:765.826   Data:  00 00
T82D4 007:765.864 - 0.381ms returns 2 (0x2)
T82D4 007:765.893 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:765.917   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 007:766.234   Data:  00 00
T82D4 007:766.268 - 0.384ms returns 2 (0x2)
T82D4 007:766.293 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:766.317   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 007:766.738   Data:  00 00 00 00
T82D4 007:766.774 - 0.490ms returns 4 (0x4)
T82D4 007:766.801 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:766.824   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 007:767.238   Data:  00 00 00 00
T82D4 007:767.274 - 0.482ms returns 4 (0x4)
T82D4 007:767.298 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:767.323   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 007:767.736   Data:  00 00 00 00
T82D4 007:767.771 - 0.483ms returns 4 (0x4)
T5E80 007:767.855 JLINK_HasError()
T5E80 007:767.892 JLINK_IsHalted()
T5E80 007:768.249 - 0.374ms returns FALSE
T82D4 007:768.282 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:768.313   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 007:768.631   Data:  00 00
T82D4 007:768.666 - 0.393ms returns 2 (0x2)
T82D4 007:768.703 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:768.727   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 007:769.036   Data:  00 00
T82D4 007:769.067 - 0.373ms returns 2 (0x2)
T82D4 007:769.089 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:769.115   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 007:769.431   Data:  00 00 00 00
T82D4 007:769.465 - 0.384ms returns 4 (0x4)
T82D4 007:769.487 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:769.513   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 007:769.822   Data:  00 00 00 00
T82D4 007:769.857 - 0.378ms returns 4 (0x4)
T82D4 007:769.880 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:769.903   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 007:770.236   Data:  00 00 00 00
T82D4 007:770.269 - 0.397ms returns 4 (0x4)
T82D4 007:770.293 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:770.316   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 007:770.630   Data:  00 00
T82D4 007:770.661 - 0.377ms returns 2 (0x2)
T82D4 007:770.685 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:770.705   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 007:771.011   Data:  00 00
T82D4 007:771.046 - 0.370ms returns 2 (0x2)
T82D4 007:771.069 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:771.093   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 007:771.405   Data:  00 00
T82D4 007:771.439 - 0.378ms returns 2 (0x2)
T82D4 007:771.469 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:771.493   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 007:771.807   Data:  00 00 00 00
T82D4 007:771.838 - 0.377ms returns 4 (0x4)
T82D4 007:771.860 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:771.883   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 007:772.235   Data:  00 00 00 00
T82D4 007:772.270 - 0.417ms returns 4 (0x4)
T82D4 007:772.292 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:772.317   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 007:772.647   Data:  00 00 00 00
T82D4 007:772.677 - 0.394ms returns 4 (0x4)
T82D4 007:772.700 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:772.723   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 007:773.036   Data:  00 00
T82D4 007:773.070 - 0.378ms returns 2 (0x2)
T82D4 007:773.095 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:773.119   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 007:773.685   Data:  00 00 00 00 00 00 00 00
T82D4 007:773.719 - 0.632ms returns 8 (0x8)
T82D4 007:773.742 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:773.764   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 007:774.084   Data:  00 00
T82D4 007:774.116 - 0.382ms returns 2 (0x2)
T82D4 007:774.137 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:774.158   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 007:774.531   Data:  00 00 00 00 00 00 00 00
T82D4 007:774.596 - 0.470ms returns 8 (0x8)
T82D4 007:774.642 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:774.692   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 007:775.206   Data:  00 00 00 00 00 00 00 00
T82D4 007:775.284 - 0.651ms returns 8 (0x8)
T82D4 007:775.320 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:775.365   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 007:775.717   Data:  00 00 00 00
T82D4 007:775.758 - 0.446ms returns 4 (0x4)
T82D4 007:775.786 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:775.813   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 007:776.136   Data:  00 00 00 00
T82D4 007:776.170 - 0.392ms returns 4 (0x4)
T82D4 007:776.194 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:776.216   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 007:776.532   Data:  00 00 00 00
T82D4 007:776.565 - 0.380ms returns 4 (0x4)
T82D4 007:776.591 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:776.655   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 007:777.028   Data:  00 00 00 00
T82D4 007:777.079 - 0.497ms returns 4 (0x4)
T82D4 007:777.103 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:777.128   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 007:777.486   Data:  00 00 00 00
T82D4 007:777.522 - 0.429ms returns 4 (0x4)
T82D4 007:777.549 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:777.572   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 007:777.884   Data:  00 00 00 00
T82D4 007:777.919 - 0.379ms returns 4 (0x4)
T82D4 007:777.943 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:777.974   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 007:778.282   Data:  00 00 00 00
T82D4 007:778.314 - 0.380ms returns 4 (0x4)
T82D4 007:778.337 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:778.361   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 007:778.684   Data:  00 00 00 00
T82D4 007:778.728 - 0.400ms returns 4 (0x4)
T82D4 007:778.758 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:778.783   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 007:779.116   Data:  00 00 00 00
T82D4 007:779.154 - 0.405ms returns 4 (0x4)
T82D4 007:779.180 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:779.206   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 007:779.536   Data:  00 00 00 00
T82D4 007:779.573 - 0.404ms returns 4 (0x4)
T82D4 007:779.602 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:779.629   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 007:780.012   Data:  00 00 00 00
T82D4 007:780.049 - 0.455ms returns 4 (0x4)
T82D4 007:780.075 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:780.099   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 007:780.433   Data:  00 00 00 00
T82D4 007:780.470 - 0.404ms returns 4 (0x4)
T82D4 007:780.495 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:780.518   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 007:780.827   Data:  00 00 00 00
T82D4 007:780.860 - 0.374ms returns 4 (0x4)
T82D4 007:780.884 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:780.908   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 007:781.236   Data:  00 00 00 00
T82D4 007:781.269 - 0.393ms returns 4 (0x4)
T82D4 007:781.293 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:781.315   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 007:781.637   Data:  00 00 00 00
T82D4 007:781.690 - 0.410ms returns 4 (0x4)
T82D4 007:781.724 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:781.750   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 007:782.062   Data:  00 00 00 00
T82D4 007:782.101 - 0.386ms returns 4 (0x4)
T82D4 007:782.129 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:782.154   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 007:782.501   Data:  00 00 00 00
T82D4 007:782.538 - 0.418ms returns 4 (0x4)
T82D4 007:782.564 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:782.589   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 007:782.924   Data:  00 00 00 00
T82D4 007:782.959 - 0.403ms returns 4 (0x4)
T82D4 007:782.983 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:783.008   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 007:783.318   Data:  00 00 00 00
T82D4 007:783.351 - 0.376ms returns 4 (0x4)
T82D4 007:783.376 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:783.399   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 007:783.707   Data:  00 00 00 00
T82D4 007:783.742 - 0.375ms returns 4 (0x4)
T82D4 007:783.768 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:783.792   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 007:784.114   Data:  00 00 00 00
T82D4 007:784.150 - 0.391ms returns 4 (0x4)
T82D4 007:784.175 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:784.200   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 007:784.533   Data:  00 00 00 00
T82D4 007:784.568 - 0.403ms returns 4 (0x4)
T82D4 007:784.594 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:784.617   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 007:784.935   Data:  00 00
T82D4 007:784.971 - 0.385ms returns 2 (0x2)
T82D4 007:784.994 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:785.020   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 007:785.324   Data:  00 00
T82D4 007:785.356 - 0.370ms returns 2 (0x2)
T82D4 007:785.378 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:785.401   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 007:785.701   Data:  00 00
T82D4 007:785.736 - 0.369ms returns 2 (0x2)
T82D4 007:785.764 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:785.792   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 007:786.338   Data:  00 00 00 00 00 00 00 00
T82D4 007:786.371 - 0.615ms returns 8 (0x8)
T82D4 007:786.395 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:786.418   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 007:787.013   Data:  00 00 00 00 00 00 00 00
T82D4 007:787.048 - 0.662ms returns 8 (0x8)
T82D4 007:787.071 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:787.098   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 007:787.507   Data:  00 00 00 00
T82D4 007:787.541 - 0.479ms returns 4 (0x4)
T82D4 007:787.564 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:787.588   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 007:788.013   Data:  00 00 00 00
T82D4 007:788.050 - 0.494ms returns 4 (0x4)
T82D4 007:788.074 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:788.099   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 007:788.507   Data:  00 00 00 00
T82D4 007:788.541 - 0.476ms returns 4 (0x4)
T82D4 007:788.565 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:788.587   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 007:789.013   Data:  00 00 00 00
T82D4 007:789.050 - 0.494ms returns 4 (0x4)
T82D4 007:789.074 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:789.099   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 007:789.510   Data:  00 00 00 00
T82D4 007:789.546 - 0.481ms returns 4 (0x4)
T82D4 007:789.569 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:789.595   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 007:790.010   Data:  00 00 00 00
T82D4 007:790.046 - 0.486ms returns 4 (0x4)
T82D4 007:790.071 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:790.096   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 007:790.510   Data:  00 00 00 00
T82D4 007:790.546 - 0.483ms returns 4 (0x4)
T82D4 007:790.571 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:790.595   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 007:790.908   Data:  00 00
T82D4 007:790.944 - 0.382ms returns 2 (0x2)
T82D4 007:791.026 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:791.050   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 007:791.371   Data:  00 00
T82D4 007:791.423 - 0.426ms returns 2 (0x2)
T82D4 007:791.465 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:791.490   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 007:791.817   Data:  00 00
T82D4 007:791.850 - 0.396ms returns 2 (0x2)
T82D4 007:791.876 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:791.897   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 007:792.245   Data:  00 00
T82D4 007:792.299 - 0.432ms returns 2 (0x2)
T82D4 007:792.320 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:792.345   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 007:792.656   Data:  00 00
T82D4 007:792.689 - 0.379ms returns 2 (0x2)
T82D4 007:792.712 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:792.735   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 007:793.059   Data:  00 00
T82D4 007:793.093 - 0.391ms returns 2 (0x2)
T82D4 007:793.116 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:793.140   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 007:793.449   Data:  00 00
T82D4 007:793.479 - 0.371ms returns 2 (0x2)
T82D4 007:793.592 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:793.621   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 007:793.922   Data:  00 00
T82D4 007:793.956 - 0.372ms returns 2 (0x2)
T82D4 007:793.977 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:794.000   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 007:794.357   Data:  00 00
T82D4 007:794.387 - 0.418ms returns 2 (0x2)
T82D4 007:794.408 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:794.429   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 007:794.733   Data:  00 00
T82D4 007:794.768 - 0.368ms returns 2 (0x2)
T82D4 007:794.851 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:794.887   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 007:795.230   Data:  00 00
T82D4 007:795.267 - 0.424ms returns 2 (0x2)
T82D4 007:795.287 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:795.333   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 007:795.659   Data:  00 00
T82D4 007:795.739 - 0.461ms returns 2 (0x2)
T82D4 007:795.765 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:795.790   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 007:796.111   Data:  00 00
T82D4 007:796.148 - 0.391ms returns 2 (0x2)
T82D4 007:796.169 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:796.195   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 007:796.525   Data:  00 00
T82D4 007:796.566 - 0.406ms returns 2 (0x2)
T82D4 007:796.603 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:796.648   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 007:797.027   Data:  00 00
T82D4 007:797.066 - 0.472ms returns 2 (0x2)
T82D4 007:797.089 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:797.151   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 007:797.509   Data:  00 00 00 00
T82D4 007:797.547 - 0.467ms returns 4 (0x4)
T82D4 007:797.571 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:797.595   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 007:797.908   Data:  00 00 00 00
T82D4 007:797.943 - 0.383ms returns 4 (0x4)
T82D4 007:797.968 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:797.991   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 007:798.307   Data:  00 00 00 00
T82D4 007:798.344 - 0.385ms returns 4 (0x4)
T82D4 007:798.368 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:798.390   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 007:798.697   Data:  00 00
T82D4 007:798.732 - 0.373ms returns 2 (0x2)
T82D4 007:798.755 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:798.777   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 007:799.085   Data:  00 00
T82D4 007:799.122 - 0.375ms returns 2 (0x2)
T82D4 007:799.143 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:799.168   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 007:799.497   Data:  00 00
T82D4 007:799.533 - 0.398ms returns 2 (0x2)
T82D4 007:799.554 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:799.581   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 007:800.012   Data:  00 00 00 00
T82D4 007:800.048 - 0.504ms returns 4 (0x4)
T82D4 007:800.076 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:800.098   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 007:800.508   Data:  00 00 00 00
T82D4 007:800.544 - 0.476ms returns 4 (0x4)
T82D4 007:800.569 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:800.592   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 007:801.010   Data:  00 00 00 00
T82D4 007:801.046 - 0.486ms returns 4 (0x4)
T82D4 007:801.072 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:801.093   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 007:801.407   Data:  00 00
T82D4 007:801.442 - 0.379ms returns 2 (0x2)
T82D4 007:801.480 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:801.505   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 007:801.831   Data:  00 00
T82D4 007:801.864 - 0.392ms returns 2 (0x2)
T82D4 007:801.885 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:801.910   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 007:802.239   Data:  00 00 00 00
T82D4 007:802.275 - 0.400ms returns 4 (0x4)
T82D4 007:802.298 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:802.322   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 007:802.635   Data:  00 00 00 00
T82D4 007:802.673 - 0.384ms returns 4 (0x4)
T82D4 007:802.696 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:802.723   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 007:803.035   Data:  00 00 00 00
T82D4 007:803.071 - 0.383ms returns 4 (0x4)
T82D4 007:803.097 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:803.123   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 007:803.457   Data:  00 00
T82D4 007:803.494 - 0.406ms returns 2 (0x2)
T82D4 007:803.517 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:803.541   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 007:803.851   Data:  00 00
T82D4 007:803.887 - 0.379ms returns 2 (0x2)
T82D4 007:803.910 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:803.936   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 007:804.256   Data:  00 00
T82D4 007:804.290 - 0.389ms returns 2 (0x2)
T82D4 007:804.321 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:804.345   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 007:804.658   Data:  00 00 00 00
T82D4 007:804.690 - 0.378ms returns 4 (0x4)
T82D4 007:804.713 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:804.736   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 007:805.062   Data:  00 00 00 00
T82D4 007:805.098 - 0.394ms returns 4 (0x4)
T82D4 007:805.123 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:805.147   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 007:805.459   Data:  00 00 00 00
T82D4 007:805.495 - 0.381ms returns 4 (0x4)
T82D4 007:805.518 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:805.544   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 007:805.847   Data:  00 00
T82D4 007:805.882 - 0.373ms returns 2 (0x2)
T82D4 007:805.903 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:805.929   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 007:806.490   Data:  00 00 00 00 00 00 00 00
T82D4 007:806.526 - 0.632ms returns 8 (0x8)
T82D4 007:806.549 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:806.575   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 007:806.881   Data:  00 00
T82D4 007:806.914 - 0.375ms returns 2 (0x2)
T82D4 007:806.937 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:806.960   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 007:807.316   Data:  00 00 00 00 00 00 00 00
T82D4 007:807.347 - 0.418ms returns 8 (0x8)
T82D4 007:807.368 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:807.389   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 007:807.746   Data:  00 00 00 00 00 00 00 00
T82D4 007:807.802 - 0.443ms returns 8 (0x8)
T82D4 007:807.841 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:807.890   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 007:808.343   Data:  00 00 00 00
T82D4 007:808.384 - 0.552ms returns 4 (0x4)
T82D4 007:808.409 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:808.434   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 007:808.759   Data:  00 00 00 00
T82D4 007:808.795 - 0.394ms returns 4 (0x4)
T82D4 007:808.817 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:808.842   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 007:809.185   Data:  00 00 00 00
T82D4 007:809.238 - 0.429ms returns 4 (0x4)
T82D4 007:809.260 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:809.284   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 007:809.586   Data:  00 00 00 00
T82D4 007:809.619 - 0.368ms returns 4 (0x4)
T82D4 007:809.642 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:809.664   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 007:810.010   Data:  00 00 00 00
T82D4 007:810.040 - 0.405ms returns 4 (0x4)
T82D4 007:810.060 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:810.085   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 007:810.432   Data:  00 00 00 00
T82D4 007:810.468 - 0.416ms returns 4 (0x4)
T82D4 007:810.490 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:810.511   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 007:810.815   Data:  00 00 00 00
T82D4 007:810.849 - 0.366ms returns 4 (0x4)
T82D4 007:810.871 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:810.892   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 007:811.236   Data:  00 00 00 00
T82D4 007:811.272 - 0.409ms returns 4 (0x4)
T82D4 007:811.293 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:811.317   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 007:811.640   Data:  00 00 00 00
T82D4 007:811.700 - 0.420ms returns 4 (0x4)
T82D4 007:811.733 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:811.765   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 007:812.144   Data:  00 00 00 00
T82D4 007:812.202 - 0.478ms returns 4 (0x4)
T82D4 007:812.225 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:812.248   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 007:812.597   Data:  00 00 00 00
T82D4 007:812.634 - 0.432ms returns 4 (0x4)
T82D4 007:812.691 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:812.715   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 007:813.146   Data:  00 00 00 00
T82D4 007:813.216 - 0.536ms returns 4 (0x4)
T82D4 007:813.240 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:813.300   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 007:813.714   Data:  00 00 00 00
T82D4 007:813.771 - 0.539ms returns 4 (0x4)
T82D4 007:813.795 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:813.818   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 007:814.134   Data:  00 00 00 00
T82D4 007:814.167 - 0.382ms returns 4 (0x4)
T82D4 007:814.192 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:814.215   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 007:814.531   Data:  00 00 00 00
T82D4 007:814.584 - 0.403ms returns 4 (0x4)
T82D4 007:814.608 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:814.647   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 007:815.009   Data:  00 00 00 00
T82D4 007:815.040 - 0.442ms returns 4 (0x4)
T82D4 007:815.062 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:815.086   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 007:815.431   Data:  00 00 00 00
T82D4 007:815.463 - 0.410ms returns 4 (0x4)
T82D4 007:815.485 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:815.509   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 007:815.807   Data:  00 00 00 00
T82D4 007:815.839 - 0.362ms returns 4 (0x4)
T82D4 007:815.859 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:815.881   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 007:816.237   Data:  00 00 00 00
T82D4 007:816.271 - 0.420ms returns 4 (0x4)
T82D4 007:816.293 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:816.314   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 007:816.632   Data:  00 00 00 00
T82D4 007:816.664 - 0.380ms returns 4 (0x4)
T82D4 007:816.686 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:816.709   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 007:817.008   Data:  00 00 00 00
T82D4 007:817.041 - 0.363ms returns 4 (0x4)
T82D4 007:817.065 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:817.086   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 007:817.407   Data:  00 00 00 00
T82D4 007:817.439 - 0.385ms returns 4 (0x4)
T82D4 007:817.461 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:817.483   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 007:817.855   Data:  00 00
T82D4 007:817.886 - 0.432ms returns 2 (0x2)
T82D4 007:817.906 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:817.925   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 007:818.229   Data:  00 00
T82D4 007:818.262 - 0.365ms returns 2 (0x2)
T82D4 007:818.282 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:818.307   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 007:818.607   Data:  00 00
T82D4 007:818.638 - 0.366ms returns 2 (0x2)
T82D4 007:818.663 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:818.709   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 007:819.262   Data:  00 00 00 00 00 00 00 00
T82D4 007:819.293 - 0.639ms returns 8 (0x8)
T82D4 007:819.319 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:819.362   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 007:820.012   Data:  00 00 00 00 00 00 00 00
T82D4 007:820.045 - 0.734ms returns 8 (0x8)
T82D4 007:820.066 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:820.113   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 007:820.531   Data:  00 00 00 00
T82D4 007:820.564 - 0.508ms returns 4 (0x4)
T82D4 007:820.588 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:820.633   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 007:821.084   Data:  00 00 00 00
T82D4 007:821.115 - 0.537ms returns 4 (0x4)
T82D4 007:821.139 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:821.184   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 007:821.631   Data:  00 00 00 00
T82D4 007:821.664 - 0.533ms returns 4 (0x4)
T82D4 007:821.687 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:821.730   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 007:822.134   Data:  00 00 00 00
T82D4 007:822.165 - 0.487ms returns 4 (0x4)
T82D4 007:822.188 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:822.233   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 007:822.681   Data:  00 00 00 00
T82D4 007:822.714 - 0.534ms returns 4 (0x4)
T82D4 007:822.737 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:822.780   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 007:823.184   Data:  00 00 00 00
T82D4 007:823.217 - 0.488ms returns 4 (0x4)
T82D4 007:823.241 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:823.284   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 007:823.699   Data:  00 00 00 00
T82D4 007:823.733 - 0.500ms returns 4 (0x4)
T82D4 007:823.754 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:823.799   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 007:824.133   Data:  00 00
T82D4 007:824.166 - 0.420ms returns 2 (0x2)
T82D4 007:839.083 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:839.233   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 007:839.657   Data:  00 00
T82D4 007:839.725 - 0.654ms returns 2 (0x2)
T82D4 007:839.769 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:839.792   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 007:840.181   Data:  00 00
T82D4 007:840.239 - 0.501ms returns 2 (0x2)
T82D4 007:840.286 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:840.315   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 007:840.682   Data:  00 00
T82D4 007:840.722 - 0.447ms returns 2 (0x2)
T82D4 007:840.747 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:840.772   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 007:841.114   Data:  00 00
T82D4 007:841.157 - 0.419ms returns 2 (0x2)
T82D4 007:841.180 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:841.206   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 007:841.538   Data:  00 00
T82D4 007:841.583 - 0.411ms returns 2 (0x2)
T82D4 007:841.607 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:841.633   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 007:842.016   Data:  00 00
T82D4 007:842.059 - 0.463ms returns 2 (0x2)
T82D4 007:842.087 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:842.114   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 007:842.448   Data:  00 00
T82D4 007:842.510 - 0.437ms returns 2 (0x2)
T82D4 007:842.544 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:842.589   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 007:842.937   Data:  00 00
T82D4 007:842.983 - 0.448ms returns 2 (0x2)
T82D4 007:843.008 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:843.039   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 007:843.354   Data:  00 00
T82D4 007:843.396 - 0.397ms returns 2 (0x2)
T82D4 007:843.418 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:843.443   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 007:843.758   Data:  00 00
T82D4 007:843.797 - 0.388ms returns 2 (0x2)
T82D4 007:843.821 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:843.845   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 007:844.161   Data:  00 00
T82D4 007:844.196 - 0.386ms returns 2 (0x2)
T82D4 007:844.221 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:844.246   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 007:844.582   Data:  00 00
T82D4 007:844.617 - 0.406ms returns 2 (0x2)
T82D4 007:844.641 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:844.666   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 007:845.009   Data:  00 00
T82D4 007:845.046 - 0.413ms returns 2 (0x2)
T82D4 007:845.068 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:845.091   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 007:845.409   Data:  00 00
T82D4 007:845.443 - 0.383ms returns 2 (0x2)
T82D4 007:845.465 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:845.489   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 007:845.810   Data:  00 00 00 00
T82D4 007:845.847 - 0.393ms returns 4 (0x4)
T82D4 007:845.874 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:845.899   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 007:846.235   Data:  00 00 00 00
T82D4 007:846.271 - 0.407ms returns 4 (0x4)
T82D4 007:846.295 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:846.320   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 007:846.634   Data:  00 00 00 00
T82D4 007:846.668 - 0.382ms returns 4 (0x4)
T82D4 007:846.693 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:846.717   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 007:847.034   Data:  00 00
T82D4 007:847.070 - 0.386ms returns 2 (0x2)
T82D4 007:847.095 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:847.118   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 007:847.432   Data:  00 00
T82D4 007:847.466 - 0.380ms returns 2 (0x2)
T82D4 007:847.487 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:847.510   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 007:847.815   Data:  00 00
T82D4 007:847.851 - 0.373ms returns 2 (0x2)
T82D4 007:847.875 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:847.897   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 007:848.332   Data:  00 00 00 00
T82D4 007:848.365 - 0.500ms returns 4 (0x4)
T82D4 007:848.391 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:848.413   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 007:848.818   Data:  00 00 00 00
T82D4 007:848.851 - 0.469ms returns 4 (0x4)
T82D4 007:848.876 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:848.898   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 007:849.334   Data:  00 00 00 00
T82D4 007:849.366 - 0.499ms returns 4 (0x4)
T82D4 007:849.444 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:849.470   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 007:849.776   Data:  00 00
T82D4 007:849.813 - 0.378ms returns 2 (0x2)
T82D4 007:849.850 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:849.875   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 007:850.236   Data:  00 00
T82D4 007:850.273 - 0.431ms returns 2 (0x2)
T82D4 007:850.298 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:850.321   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 007:850.633   Data:  00 00 00 00
T82D4 007:850.666 - 0.377ms returns 4 (0x4)
T82D4 007:850.688 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:850.712   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 007:851.038   Data:  00 00 00 00
T82D4 007:851.072 - 0.395ms returns 4 (0x4)
T82D4 007:851.097 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:851.122   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 007:851.432   Data:  00 00 00 00
T82D4 007:851.470 - 0.382ms returns 4 (0x4)
T82D4 007:851.492 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:851.516   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 007:851.823   Data:  00 00
T82D4 007:851.856 - 0.373ms returns 2 (0x2)
T82D4 007:851.878 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:851.900   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 007:852.237   Data:  00 00
T82D4 007:852.278 - 0.409ms returns 2 (0x2)
T82D4 007:852.300 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:852.323   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 007:852.631   Data:  00 00
T82D4 007:852.667 - 0.377ms returns 2 (0x2)
T82D4 007:852.697 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:852.720   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 007:853.037   Data:  00 00 00 00
T82D4 007:853.073 - 0.384ms returns 4 (0x4)
T82D4 007:853.099 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:853.124   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 007:853.458   Data:  00 00 00 00
T82D4 007:853.493 - 0.403ms returns 4 (0x4)
T82D4 007:853.518 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:853.542   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 007:853.848   Data:  00 00 00 00
T82D4 007:853.884 - 0.375ms returns 4 (0x4)
T82D4 007:853.907 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:853.933   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 007:854.257   Data:  00 00
T82D4 007:854.290 - 0.392ms returns 2 (0x2)
T82D4 007:854.312 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:854.334   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 007:854.884   Data:  00 00 00 00 00 00 00 00
T82D4 007:854.918 - 0.614ms returns 8 (0x8)
T82D4 007:854.939 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:854.964   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 007:855.265   Data:  00 00
T82D4 007:855.296 - 0.364ms returns 2 (0x2)
T82D4 007:855.315 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:855.337   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 007:855.685   Data:  00 00 00 00 00 00 00 00
T82D4 007:855.719 - 0.411ms returns 8 (0x8)
T82D4 007:855.742 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:855.765   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 007:856.116   Data:  00 00 00 00 00 00 00 00
T82D4 007:856.147 - 0.413ms returns 8 (0x8)
T82D4 007:856.168 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:856.190   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 007:856.508   Data:  00 00 00 00
T82D4 007:856.543 - 0.383ms returns 4 (0x4)
T82D4 007:856.567 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:856.589   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 007:856.907   Data:  00 00 00 00
T82D4 007:856.938 - 0.380ms returns 4 (0x4)
T82D4 007:856.960 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:856.980   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 007:857.315   Data:  00 00 00 00
T82D4 007:857.380 - 0.436ms returns 4 (0x4)
T82D4 007:857.425 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:857.460   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 007:857.918   Data:  00 00 00 00
T82D4 007:857.991 - 0.578ms returns 4 (0x4)
T82D4 007:858.035 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:858.084   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 007:858.413   Data:  00 00 00 00
T82D4 007:858.506 - 0.484ms returns 4 (0x4)
T82D4 007:858.536 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:858.566   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 007:858.904   Data:  00 00 00 00
T82D4 007:858.998 - 0.497ms returns 4 (0x4)
T82D4 007:859.054 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:859.083   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 007:859.495   Data:  00 00 00 00
T82D4 007:859.531 - 0.486ms returns 4 (0x4)
T82D4 007:859.557 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:859.581   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 007:859.909   Data:  00 00 00 00
T82D4 007:859.941 - 0.392ms returns 4 (0x4)
T82D4 007:859.965 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:859.987   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 007:860.309   Data:  00 00 00 00
T82D4 007:860.344 - 0.389ms returns 4 (0x4)
T82D4 007:860.368 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:860.393   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 007:860.712   Data:  00 00 00 00
T82D4 007:860.750 - 0.391ms returns 4 (0x4)
T82D4 007:860.774 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:860.797   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 007:861.116   Data:  00 00 00 00
T82D4 007:861.152 - 0.387ms returns 4 (0x4)
T82D4 007:861.175 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:861.201   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 007:861.536   Data:  00 00 00 00
T82D4 007:861.577 - 0.411ms returns 4 (0x4)
T82D4 007:861.604 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:861.630   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 007:862.015   Data:  00 00 00 00
T82D4 007:862.050 - 0.459ms returns 4 (0x4)
T82D4 007:862.079 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:862.103   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 007:862.435   Data:  00 00 00 00
T82D4 007:862.469 - 0.400ms returns 4 (0x4)
T82D4 007:862.493 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:862.519   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 007:862.826   Data:  00 00 00 00
T82D4 007:862.862 - 0.379ms returns 4 (0x4)
T82D4 007:862.888 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:862.910   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 007:863.242   Data:  00 00 00 00
T82D4 007:863.277 - 0.399ms returns 4 (0x4)
T82D4 007:863.301 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:863.326   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 007:863.659   Data:  00 00 00 00
T82D4 007:863.696 - 0.403ms returns 4 (0x4)
T82D4 007:863.718 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:863.743   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 007:864.063   Data:  00 00 00 00
T82D4 007:864.097 - 0.388ms returns 4 (0x4)
T82D4 007:864.122 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:864.145   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 007:864.477   Data:  00 00 00 00
T82D4 007:864.512 - 0.400ms returns 4 (0x4)
T82D4 007:864.536 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:864.559   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 007:864.886   Data:  00 00 00 00
T82D4 007:864.918 - 0.390ms returns 4 (0x4)
T82D4 007:864.940 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:864.964   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 007:865.284   Data:  00 00 00 00
T82D4 007:865.321 - 0.390ms returns 4 (0x4)
T82D4 007:865.344 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:865.367   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 007:865.684   Data:  00 00 00 00
T82D4 007:865.720 - 0.384ms returns 4 (0x4)
T82D4 007:865.741 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:865.767   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 007:866.087   Data:  00 00
T82D4 007:866.122 - 0.389ms returns 2 (0x2)
T82D4 007:866.145 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:866.170   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 007:866.499   Data:  00 00
T82D4 007:866.534 - 0.398ms returns 2 (0x2)
T82D4 007:866.559 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:866.582   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 007:866.908   Data:  00 00
T82D4 007:866.941 - 0.391ms returns 2 (0x2)
T82D4 007:866.962 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:866.985   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 007:867.538   Data:  00 00 00 00 00 00 00 00
T82D4 007:867.570 - 0.617ms returns 8 (0x8)
T82D4 007:867.593 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:867.615   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 007:868.166   Data:  00 00 00 00 00 00 00 00
T82D4 007:868.198 - 0.613ms returns 8 (0x8)
T82D4 007:868.221 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:868.244   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 007:868.661   Data:  00 00 00 00
T82D4 007:868.693 - 0.481ms returns 4 (0x4)
T82D4 007:868.715 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:868.741   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 007:869.163   Data:  00 00 00 00
T82D4 007:869.194 - 0.488ms returns 4 (0x4)
T82D4 007:869.217 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:869.241   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 007:869.661   Data:  00 00 00 00
T82D4 007:869.697 - 0.489ms returns 4 (0x4)
T5E80 007:869.722 JLINK_HasError()
T5E80 007:869.759 JLINK_HasError()
T5E80 007:869.783 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 007:869.808   Data:  E5 A9 BD 08
T5E80 007:869.838   Debug reg: DWT_CYCCNT
T5E80 007:869.869 - 0.095ms returns 1 (0x1)
T82D4 007:869.892 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:869.920   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 007:870.336   Data:  00 00 00 00
T82D4 007:870.371 - 0.489ms returns 4 (0x4)
T82D4 007:870.395 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:870.420   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 007:870.826   Data:  00 00 00 00
T82D4 007:870.858 - 0.472ms returns 4 (0x4)
T82D4 007:870.880 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:870.902   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 007:871.338   Data:  00 00 00 00
T82D4 007:871.370 - 0.499ms returns 4 (0x4)
T82D4 007:871.392 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:871.413   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 007:871.821   Data:  00 00 00 00
T82D4 007:871.853 - 0.468ms returns 4 (0x4)
T82D4 007:871.874 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:871.896   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 007:872.254   Data:  00 00
T82D4 007:872.318 - 0.452ms returns 2 (0x2)
T82D4 007:874.364 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:874.446   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 007:874.845   Data:  00 00
T82D4 007:874.966 - 0.613ms returns 2 (0x2)
T82D4 007:875.022 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:875.051   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 007:875.447   Data:  00 00
T82D4 007:875.512 - 0.499ms returns 2 (0x2)
T82D4 007:875.606 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:875.648   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 007:876.115   Data:  00 00
T82D4 007:876.178 - 0.580ms returns 2 (0x2)
T82D4 007:876.201 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:876.250   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 007:876.594   Data:  00 00
T82D4 007:876.627 - 0.434ms returns 2 (0x2)
T82D4 007:876.650 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:876.673   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 007:877.007   Data:  00 00
T82D4 007:877.039 - 0.396ms returns 2 (0x2)
T82D4 007:877.060 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:877.084   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 007:877.405   Data:  00 00
T82D4 007:877.438 - 0.387ms returns 2 (0x2)
T82D4 007:877.460 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:877.484   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 007:877.804   Data:  00 00
T82D4 007:877.837 - 0.386ms returns 2 (0x2)
T82D4 007:877.859 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:877.883   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 007:878.243   Data:  00 00
T82D4 007:878.298 - 0.470ms returns 2 (0x2)
T82D4 007:878.343 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:878.372   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 007:878.695   Data:  00 00
T82D4 007:878.730 - 0.397ms returns 2 (0x2)
T82D4 007:878.754 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:878.778   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 007:879.086   Data:  00 00
T82D4 007:879.117 - 0.371ms returns 2 (0x2)
T82D4 007:879.138 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:879.160   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 007:879.488   Data:  00 00
T82D4 007:879.522 - 0.393ms returns 2 (0x2)
T82D4 007:879.542 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:879.572   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 007:879.879   Data:  00 00
T82D4 007:879.911 - 0.380ms returns 2 (0x2)
T82D4 007:879.935 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:879.959   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 007:880.281   Data:  00 00
T82D4 007:880.315 - 0.389ms returns 2 (0x2)
T82D4 007:880.338 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:880.359   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 007:880.680   Data:  00 00
T82D4 007:880.713 - 0.384ms returns 2 (0x2)
T82D4 007:880.738 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:880.758   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 007:881.059   Data:  00 00 00 00
T82D4 007:881.089 - 0.360ms returns 4 (0x4)
T82D4 007:881.109 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:881.131   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 007:881.432   Data:  00 00 00 00
T82D4 007:881.465 - 0.365ms returns 4 (0x4)
T82D4 007:881.487 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:881.511   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 007:881.811   Data:  00 00 00 00
T82D4 007:881.844 - 0.365ms returns 4 (0x4)
T82D4 007:881.867 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:881.889   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 007:882.235   Data:  00 00
T82D4 007:882.267 - 0.410ms returns 2 (0x2)
T82D4 007:882.290 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:882.313   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 007:882.630   Data:  00 00
T82D4 007:882.664 - 0.382ms returns 2 (0x2)
T82D4 007:882.686 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:882.708   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 007:883.009   Data:  00 00
T82D4 007:883.040 - 0.362ms returns 2 (0x2)
T82D4 007:883.060 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:883.082   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 007:883.485   Data:  00 00 00 00
T82D4 007:883.520 - 0.468ms returns 4 (0x4)
T82D4 007:883.543 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:883.568   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 007:884.010   Data:  00 00 00 00
T82D4 007:884.041 - 0.506ms returns 4 (0x4)
T82D4 007:884.062 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:884.083   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 007:884.486   Data:  00 00 00 00
T82D4 007:884.517 - 0.467ms returns 4 (0x4)
T82D4 007:884.541 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:884.563   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 007:884.880   Data:  00 00
T82D4 007:884.913 - 0.382ms returns 2 (0x2)
T82D4 007:884.951 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:884.976   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 007:885.280   Data:  00 00
T82D4 007:885.314 - 0.371ms returns 2 (0x2)
T82D4 007:885.337 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:885.358   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 007:885.681   Data:  00 00 00 00
T82D4 007:885.715 - 0.387ms returns 4 (0x4)
T82D4 007:885.739 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:885.759   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 007:886.084   Data:  00 00 00 00
T82D4 007:886.117 - 0.387ms returns 4 (0x4)
T82D4 007:886.137 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:886.162   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 007:886.494   Data:  00 00 00 00
T82D4 007:886.526 - 0.399ms returns 4 (0x4)
T82D4 007:886.551 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:886.571   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 007:886.880   Data:  00 00
T82D4 007:886.913 - 0.371ms returns 2 (0x2)
T82D4 007:886.935 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:886.956   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 007:887.255   Data:  00 00
T82D4 007:887.289 - 0.362ms returns 2 (0x2)
T82D4 007:887.312 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:887.334   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 007:887.655   Data:  00 00
T82D4 007:887.689 - 0.385ms returns 2 (0x2)
T82D4 007:887.717 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:887.741   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 007:888.058   Data:  00 00 00 00
T82D4 007:888.092 - 0.383ms returns 4 (0x4)
T82D4 007:888.114 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:888.139   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 007:888.486   Data:  00 00 00 00
T82D4 007:888.534 - 0.431ms returns 4 (0x4)
T82D4 007:888.560 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:888.605   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 007:889.035   Data:  00 00 00 00
T82D4 007:889.072 - 0.521ms returns 4 (0x4)
T82D4 007:889.095 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:889.119   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 007:889.444   Data:  00 00
T82D4 007:889.478 - 0.392ms returns 2 (0x2)
T82D4 007:889.500 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:889.521   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 007:890.062   Data:  00 00 00 00 00 00 00 00
T82D4 007:890.092 - 0.602ms returns 8 (0x8)
T82D4 007:890.115 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:890.139   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 007:890.478   Data:  00 00
T82D4 007:890.510 - 0.404ms returns 2 (0x2)
T82D4 007:890.533 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:890.555   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 007:890.891   Data:  00 00 00 00 00 00 00 00
T82D4 007:890.929 - 0.404ms returns 8 (0x8)
T82D4 007:890.950 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:891.006   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 007:891.428   Data:  00 00 00 00 00 00 00 00
T82D4 007:891.467 - 0.548ms returns 8 (0x8)
T82D4 007:891.515 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:891.542   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 007:891.866   Data:  00 00 00 00
T82D4 007:891.905 - 0.398ms returns 4 (0x4)
T82D4 007:891.928 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:891.956   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 007:892.285   Data:  00 00 00 00
T82D4 007:892.320 - 0.400ms returns 4 (0x4)
T82D4 007:892.342 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:892.364   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 007:892.682   Data:  00 00 00 00
T82D4 007:892.717 - 0.383ms returns 4 (0x4)
T82D4 007:892.739 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:892.764   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 007:893.086   Data:  00 00 00 00
T82D4 007:893.120 - 0.390ms returns 4 (0x4)
T82D4 007:893.145 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:893.170   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 007:893.485   Data:  00 00 00 00
T82D4 007:893.520 - 0.384ms returns 4 (0x4)
T82D4 007:893.544 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:893.566   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 007:893.882   Data:  00 00 00 00
T82D4 007:893.913 - 0.380ms returns 4 (0x4)
T82D4 007:893.937 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:893.961   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 007:894.282   Data:  00 00 00 00
T82D4 007:894.316 - 0.389ms returns 4 (0x4)
T82D4 007:894.342 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:894.363   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 007:894.681   Data:  00 00 00 00
T82D4 007:894.713 - 0.381ms returns 4 (0x4)
T82D4 007:894.736 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:894.762   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 007:895.115   Data:  00 00 00 00
T82D4 007:895.179 - 0.454ms returns 4 (0x4)
T82D4 007:895.205 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:895.249   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 007:895.633   Data:  00 00 00 00
T82D4 007:895.670 - 0.477ms returns 4 (0x4)
T82D4 007:895.700 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:895.726   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 007:896.035   Data:  00 00 00 00
T82D4 007:896.069 - 0.378ms returns 4 (0x4)
T82D4 007:896.091 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:896.117   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 007:896.432   Data:  00 00 00 00
T82D4 007:896.467 - 0.384ms returns 4 (0x4)
T82D4 007:896.489 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:896.514   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 007:896.816   Data:  00 00 00 00
T82D4 007:896.848 - 0.368ms returns 4 (0x4)
T82D4 007:896.870 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:896.890   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 007:897.238   Data:  00 00 00 00
T82D4 007:897.271 - 0.410ms returns 4 (0x4)
T82D4 007:897.296 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:897.318   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 007:897.631   Data:  00 00 00 00
T82D4 007:897.665 - 0.377ms returns 4 (0x4)
T82D4 007:897.689 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:897.712   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 007:898.034   Data:  00 00 00 00
T82D4 007:898.066 - 0.387ms returns 4 (0x4)
T82D4 007:898.090 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:898.116   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 007:898.509   Data:  00 00 00 00
T82D4 007:898.543 - 0.464ms returns 4 (0x4)
T82D4 007:898.568 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:898.590   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 007:898.932   Data:  00 00 00 00
T82D4 007:898.966 - 0.408ms returns 4 (0x4)
T82D4 007:898.989 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:899.013   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 007:899.345   Data:  00 00 00 00
T82D4 007:899.380 - 0.399ms returns 4 (0x4)
T82D4 007:899.401 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:899.427   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 007:899.736   Data:  00 00 00 00
T82D4 007:899.768 - 0.376ms returns 4 (0x4)
T82D4 007:899.792 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:899.815   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 007:900.135   Data:  00 00 00 00
T82D4 007:900.169 - 0.386ms returns 4 (0x4)
T82D4 007:900.190 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:900.216   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 007:900.533   Data:  00 00 00 00
T82D4 007:900.569 - 0.388ms returns 4 (0x4)
T82D4 007:900.593 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:900.618   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 007:900.935   Data:  00 00
T82D4 007:900.967 - 0.383ms returns 2 (0x2)
T82D4 007:900.988 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:901.011   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 007:901.313   Data:  00 00
T82D4 007:901.347 - 0.367ms returns 2 (0x2)
T82D4 007:901.372 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:901.396   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 007:901.704   Data:  00 00
T82D4 007:901.739 - 0.376ms returns 2 (0x2)
T82D4 007:901.762 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:901.786   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 007:902.360   Data:  00 00 00 00 00 00 00 00
T82D4 007:902.432 - 0.679ms returns 8 (0x8)
T82D4 007:902.457 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 007:902.506   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 007:903.121   Data:  00 00 00 00 00 00 00 00
T82D4 007:903.160 - 0.712ms returns 8 (0x8)
T82D4 007:903.186 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:903.226   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 007:903.659   Data:  00 00 00 00
T82D4 007:903.697 - 0.521ms returns 4 (0x4)
T82D4 007:903.723 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:903.770   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 007:904.198   Data:  00 00 00 00
T82D4 007:904.277 - 0.571ms returns 4 (0x4)
T82D4 007:904.418 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:904.486   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 007:904.948   Data:  00 00 00 00
T82D4 007:905.019 - 0.610ms returns 4 (0x4)
T82D4 007:905.042 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:905.105   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 007:905.558   Data:  00 00 00 00
T82D4 007:905.593 - 0.560ms returns 4 (0x4)
T82D4 007:905.619 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:905.642   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 007:906.061   Data:  00 00 00 00
T82D4 007:906.097 - 0.487ms returns 4 (0x4)
T82D4 007:906.124 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:906.147   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 007:906.560   Data:  00 00 00 00
T82D4 007:906.596 - 0.481ms returns 4 (0x4)
T82D4 007:906.621 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:906.644   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 007:907.063   Data:  00 00 00 00
T82D4 007:907.097 - 0.488ms returns 4 (0x4)
T82D4 007:907.150 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 007:907.176   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 007:907.506   Data:  00 00
T82D4 007:907.542 - 0.402ms returns 2 (0x2)
T82D4 007:909.096 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:909.133   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 007:909.470   Data:  00 00 00 00
T82D4 007:909.503 - 0.417ms returns 4 (0x4)
T82D4 007:909.533 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:909.560   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 007:909.882   Data:  00 00 00 00
T82D4 007:909.914 - 0.388ms returns 4 (0x4)
T82D4 007:909.938 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:909.962   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 007:910.283   Data:  38 80 00 20
T82D4 007:910.317 - 0.388ms returns 4 (0x4)
T82D4 007:910.386 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 007:910.413   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 007:910.725   Data:  38 80 00 20
T82D4 007:910.761 - 0.383ms returns 4 (0x4)
T5E80 007:912.004 JLINK_IsHalted()
T5E80 007:912.369 - 0.386ms returns FALSE
T5E80 008:012.842 JLINK_HasError()
T5E80 008:012.947 JLINK_IsHalted()
T5E80 008:013.388 - 0.501ms returns FALSE
T5E80 008:113.576 JLINK_HasError()
T5E80 008:113.631 JLINK_IsHalted()
T5E80 008:114.043 - 0.434ms returns FALSE
T5E80 008:215.179 JLINK_HasError()
T5E80 008:215.279 JLINK_IsHalted()
T5E80 008:215.720 - 0.462ms returns FALSE
T5E80 008:315.816 JLINK_HasError()
T5E80 008:315.952 JLINK_IsHalted()
T5E80 008:316.354 - 0.421ms returns FALSE
T5E80 008:416.870 JLINK_HasError()
T5E80 008:416.947 JLINK_HasError()
T5E80 008:416.967 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 008:416.995   Data:  E5 A9 BD 08
T5E80 008:417.023   Debug reg: DWT_CYCCNT
T5E80 008:417.048 - 0.090ms returns 1 (0x1)
T82D4 008:418.556 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:418.600   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 008:419.026   Data:  00 00
T82D4 008:419.084 - 0.540ms returns 2 (0x2)
T82D4 008:419.117 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:419.163   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 008:419.520   Data:  00 00
T82D4 008:419.580 - 0.471ms returns 2 (0x2)
T82D4 008:419.604 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:419.629   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 008:420.009   Data:  00 00
T82D4 008:420.059 - 0.463ms returns 2 (0x2)
T82D4 008:420.079 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:420.104   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 008:420.432   Data:  00 00
T82D4 008:420.468 - 0.397ms returns 2 (0x2)
T82D4 008:420.490 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:420.512   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 008:420.868   Data:  00 00
T82D4 008:420.902 - 0.421ms returns 2 (0x2)
T82D4 008:420.926 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:420.954   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 008:421.333   Data:  00 00
T82D4 008:421.372 - 0.454ms returns 2 (0x2)
T82D4 008:421.395 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:421.418   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 008:421.735   Data:  00 00
T82D4 008:421.770 - 0.383ms returns 2 (0x2)
T82D4 008:421.794 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:421.817   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 008:422.187   Data:  00 00
T82D4 008:422.226 - 0.441ms returns 2 (0x2)
T82D4 008:422.248 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:422.271   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 008:422.581   Data:  00 00
T82D4 008:422.612 - 0.372ms returns 2 (0x2)
T82D4 008:422.633 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:422.656   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 008:423.041   Data:  00 00
T82D4 008:423.079 - 0.455ms returns 2 (0x2)
T82D4 008:423.102 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:423.125   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 008:423.460   Data:  00 00
T82D4 008:423.507 - 0.414ms returns 2 (0x2)
T82D4 008:423.530 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:423.556   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 008:423.929   Data:  00 00
T82D4 008:423.964 - 0.442ms returns 2 (0x2)
T82D4 008:423.985 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:424.012   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 008:424.386   Data:  00 00
T82D4 008:424.440 - 0.463ms returns 2 (0x2)
T82D4 008:424.465 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:424.488   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 008:424.871   Data:  00 00
T82D4 008:424.906 - 0.450ms returns 2 (0x2)
T82D4 008:424.929 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:424.951   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 008:425.292   Data:  00 00 00 00
T82D4 008:425.348 - 0.430ms returns 4 (0x4)
T82D4 008:425.372 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:425.397   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 008:425.751   Data:  00 00 00 00
T82D4 008:425.789 - 0.426ms returns 4 (0x4)
T82D4 008:425.811 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:425.836   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 008:426.161   Data:  00 00 00 00
T82D4 008:426.195 - 0.393ms returns 4 (0x4)
T82D4 008:426.217 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:426.240   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 008:426.579   Data:  00 00
T82D4 008:426.625 - 0.417ms returns 2 (0x2)
T82D4 008:426.649 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:426.677   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 008:427.011   Data:  00 00
T82D4 008:427.045 - 0.405ms returns 2 (0x2)
T82D4 008:427.066 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:427.089   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 008:427.406   Data:  00 00
T82D4 008:427.439 - 0.383ms returns 2 (0x2)
T82D4 008:427.462 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:427.487   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 008:427.909   Data:  00 00 00 00
T82D4 008:427.943 - 0.490ms returns 4 (0x4)
T82D4 008:427.967 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:427.993   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 008:428.410   Data:  00 00 00 00
T82D4 008:428.447 - 0.489ms returns 4 (0x4)
T82D4 008:428.472 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:428.495   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 008:428.932   Data:  00 00 00 00
T82D4 008:428.975 - 0.512ms returns 4 (0x4)
T82D4 008:428.999 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:429.025   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 008:429.382   Data:  00 00
T82D4 008:429.420 - 0.429ms returns 2 (0x2)
T82D4 008:429.458 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:429.491   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 008:429.806   Data:  00 00
T82D4 008:429.841 - 0.392ms returns 2 (0x2)
T82D4 008:429.863 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:429.887   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 008:430.260   Data:  00 00 00 00
T82D4 008:430.302 - 0.449ms returns 4 (0x4)
T82D4 008:430.326 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:430.353   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 008:430.684   Data:  00 00 00 00
T82D4 008:430.718 - 0.403ms returns 4 (0x4)
T82D4 008:430.742 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:430.767   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 008:431.103   Data:  00 00 00 00
T82D4 008:431.142 - 0.410ms returns 4 (0x4)
T82D4 008:431.166 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:431.189   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 008:431.533   Data:  00 00
T82D4 008:431.568 - 0.410ms returns 2 (0x2)
T82D4 008:431.590 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:431.613   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 008:431.936   Data:  00 00
T82D4 008:431.971 - 0.389ms returns 2 (0x2)
T82D4 008:431.992 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:432.018   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 008:432.321   Data:  00 00
T82D4 008:432.355 - 0.372ms returns 2 (0x2)
T82D4 008:432.390 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:432.416   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 008:432.720   Data:  00 00 00 00
T82D4 008:432.752 - 0.371ms returns 4 (0x4)
T82D4 008:432.777 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:432.799   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 008:433.116   Data:  00 00 00 00
T82D4 008:433.148 - 0.381ms returns 4 (0x4)
T82D4 008:433.172 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:433.194   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 008:433.510   Data:  00 00 00 00
T82D4 008:433.548 - 0.384ms returns 4 (0x4)
T82D4 008:433.573 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:433.597   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 008:433.907   Data:  00 00
T82D4 008:433.943 - 0.378ms returns 2 (0x2)
T82D4 008:433.966 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:433.988   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 008:434.561   Data:  00 00 00 00 00 00 00 00
T82D4 008:434.593 - 0.635ms returns 8 (0x8)
T82D4 008:434.614 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:434.636   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 008:435.016   Data:  00 00
T82D4 008:435.072 - 0.482ms returns 2 (0x2)
T82D4 008:435.148 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:435.195   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 008:435.626   Data:  00 00 00 00 00 00 00 00
T82D4 008:435.718 - 0.579ms returns 8 (0x8)
T82D4 008:435.740 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:435.804   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 008:436.164   Data:  00 00 00 00 00 00 00 00
T82D4 008:436.201 - 0.469ms returns 8 (0x8)
T82D4 008:436.225 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:436.250   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 008:436.583   Data:  00 00 00 00
T82D4 008:436.619 - 0.402ms returns 4 (0x4)
T82D4 008:436.643 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:436.669   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 008:437.010   Data:  00 00 00 00
T82D4 008:437.042 - 0.409ms returns 4 (0x4)
T82D4 008:437.070 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:437.093   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 008:437.409   Data:  00 00 00 00
T82D4 008:437.445 - 0.384ms returns 4 (0x4)
T82D4 008:437.467 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:437.494   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 008:437.807   Data:  00 00 00 00
T82D4 008:437.840 - 0.385ms returns 4 (0x4)
T82D4 008:437.871 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:437.893   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 008:438.258   Data:  00 00 00 00
T82D4 008:438.290 - 0.427ms returns 4 (0x4)
T82D4 008:438.312 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:438.334   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 008:438.658   Data:  00 00 00 00
T82D4 008:438.693 - 0.390ms returns 4 (0x4)
T82D4 008:438.716 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:438.742   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 008:439.112   Data:  00 00 00 00
T82D4 008:439.144 - 0.440ms returns 4 (0x4)
T82D4 008:439.169 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:439.189   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 008:439.507   Data:  00 00 00 00
T82D4 008:439.538 - 0.377ms returns 4 (0x4)
T82D4 008:439.559 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:439.581   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 008:439.886   Data:  00 00 00 00
T82D4 008:439.936 - 0.385ms returns 4 (0x4)
T82D4 008:439.956 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:439.981   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 008:440.307   Data:  00 00 00 00
T82D4 008:440.341 - 0.393ms returns 4 (0x4)
T82D4 008:440.365 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:440.398   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 008:440.741   Data:  00 00 00 00
T82D4 008:440.827 - 0.472ms returns 4 (0x4)
T82D4 008:440.852 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:440.896   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 008:441.306   Data:  00 00 00 00
T82D4 008:441.349 - 0.506ms returns 4 (0x4)
T82D4 008:441.374 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:441.399   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 008:441.715   Data:  00 00 00 00
T82D4 008:441.800 - 0.437ms returns 4 (0x4)
T82D4 008:441.847 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:441.872   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 008:442.247   Data:  00 00 00 00
T82D4 008:442.282 - 0.444ms returns 4 (0x4)
T82D4 008:442.305 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:442.328   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 008:442.633   Data:  00 00 00 00
T82D4 008:442.669 - 0.373ms returns 4 (0x4)
T82D4 008:442.694 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:442.717   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 008:443.035   Data:  00 00 00 00
T82D4 008:443.068 - 0.386ms returns 4 (0x4)
T82D4 008:443.093 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:443.117   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 008:443.431   Data:  00 00 00 00
T82D4 008:443.464 - 0.381ms returns 4 (0x4)
T82D4 008:443.487 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:443.508   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 008:443.809   Data:  00 00 00 00
T82D4 008:443.842 - 0.364ms returns 4 (0x4)
T82D4 008:443.951 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:443.989   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 008:444.317   Data:  00 00 00 00
T82D4 008:444.351 - 0.409ms returns 4 (0x4)
T82D4 008:444.372 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:444.397   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 008:444.699   Data:  00 00 00 00
T82D4 008:444.732 - 0.391ms returns 4 (0x4)
T82D4 008:444.778 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:444.803   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 008:445.161   Data:  00 00 00 00
T82D4 008:445.212 - 0.443ms returns 4 (0x4)
T82D4 008:445.235 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:445.260   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 008:445.581   Data:  00 00 00 00
T82D4 008:445.612 - 0.385ms returns 4 (0x4)
T82D4 008:445.632 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:445.653   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 008:446.008   Data:  00 00
T82D4 008:446.052 - 0.431ms returns 2 (0x2)
T82D4 008:446.076 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:446.100   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 008:446.430   Data:  00 00
T82D4 008:446.464 - 0.396ms returns 2 (0x2)
T82D4 008:446.485 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:446.509   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 008:446.811   Data:  00 00
T82D4 008:446.845 - 0.368ms returns 2 (0x2)
T82D4 008:446.866 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:446.890   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 008:447.489   Data:  00 00 00 00 00 00 00 00
T82D4 008:447.524 - 0.666ms returns 8 (0x8)
T82D4 008:447.545 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:447.568   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 008:448.114   Data:  00 00 00 00 00 00 00 00
T82D4 008:448.148 - 0.611ms returns 8 (0x8)
T82D4 008:448.169 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:448.194   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 008:448.610   Data:  00 00 00 00
T82D4 008:448.645 - 0.485ms returns 4 (0x4)
T82D4 008:448.667 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:448.689   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 008:449.084   Data:  00 00 00 00
T82D4 008:449.118 - 0.459ms returns 4 (0x4)
T82D4 008:449.139 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:449.163   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 008:449.564   Data:  00 00 00 00
T82D4 008:449.599 - 0.469ms returns 4 (0x4)
T82D4 008:449.621 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:449.644   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 008:450.059   Data:  00 00 00 00
T82D4 008:450.093 - 0.480ms returns 4 (0x4)
T82D4 008:450.116 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:450.139   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 008:450.541   Data:  00 00 00 00
T82D4 008:450.573 - 0.465ms returns 4 (0x4)
T82D4 008:450.595 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:450.617   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 008:451.012   Data:  00 00 00 00
T82D4 008:451.048 - 0.462ms returns 4 (0x4)
T82D4 008:451.071 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:451.094   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 008:451.507   Data:  00 00 00 00
T82D4 008:451.537 - 0.474ms returns 4 (0x4)
T82D4 008:451.558 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:451.579   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 008:451.881   Data:  00 00
T82D4 008:451.913 - 0.364ms returns 2 (0x2)
T82D4 008:453.289 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:453.320   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 008:453.633   Data:  00 00 00 00
T82D4 008:453.667 - 0.387ms returns 4 (0x4)
T82D4 008:453.693 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:453.719   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 008:454.035   Data:  00 00 00 00
T82D4 008:454.068 - 0.382ms returns 4 (0x4)
T82D4 008:454.095 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:454.120   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 008:454.432   Data:  38 80 00 20
T82D4 008:454.463 - 0.377ms returns 4 (0x4)
T82D4 008:454.519 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:454.544   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 008:454.847   Data:  38 80 00 20
T82D4 008:454.879 - 0.370ms returns 4 (0x4)
T5E80 008:455.880 JLINK_IsHalted()
T5E80 008:456.284 - 0.424ms returns FALSE
T5E80 008:556.374 JLINK_HasError()
T5E80 008:556.429 JLINK_IsHalted()
T5E80 008:556.872 - 0.463ms returns FALSE
T5E80 008:656.938 JLINK_HasError()
T5E80 008:657.000 JLINK_IsHalted()
T5E80 008:657.369 - 0.385ms returns FALSE
T5E80 008:758.047 JLINK_HasError()
T5E80 008:758.330 JLINK_IsHalted()
T5E80 008:758.985 - 0.764ms returns FALSE
T82D4 008:859.243 JLINK_ReadMemEx(0x20000CA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:859.308   CPU_ReadMem(2 bytes @ 0x20000CA0)
T82D4 008:859.876   Data:  00 00
T82D4 008:859.914 - 0.679ms returns 2 (0x2)
T5E80 008:859.940 JLINK_HasError()
T5E80 008:859.977 JLINK_IsHalted()
T5E80 008:860.462 - 0.533ms returns FALSE
T82D4 008:860.562 JLINK_ReadMemEx(0x20000CA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:860.596   CPU_ReadMem(2 bytes @ 0x20000CA2)
T82D4 008:861.041   Data:  00 00
T82D4 008:861.072 - 0.519ms returns 2 (0x2)
T82D4 008:861.096 JLINK_ReadMemEx(0x20000CA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:861.116   CPU_ReadMem(2 bytes @ 0x20000CA4)
T82D4 008:861.496   Data:  00 00
T82D4 008:861.549 - 0.462ms returns 2 (0x2)
T82D4 008:861.574 JLINK_ReadMemEx(0x20000CA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:861.622   CPU_ReadMem(2 bytes @ 0x20000CA6)
T82D4 008:862.042   Data:  00 00
T82D4 008:862.074 - 0.508ms returns 2 (0x2)
T82D4 008:862.097 JLINK_ReadMemEx(0x20000CA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:862.119   CPU_ReadMem(2 bytes @ 0x20000CA8)
T82D4 008:862.431   Data:  00 00
T82D4 008:862.462 - 0.373ms returns 2 (0x2)
T82D4 008:862.484 JLINK_ReadMemEx(0x20000CAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:862.504   CPU_ReadMem(2 bytes @ 0x20000CAA)
T82D4 008:862.805   Data:  00 00
T82D4 008:862.835 - 0.358ms returns 2 (0x2)
T82D4 008:862.856 JLINK_ReadMemEx(0x20000CAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:862.877   CPU_ReadMem(2 bytes @ 0x20000CAC)
T82D4 008:863.255   Data:  00 00
T82D4 008:863.284 - 0.436ms returns 2 (0x2)
T82D4 008:863.305 JLINK_ReadMemEx(0x20000CAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:863.326   CPU_ReadMem(2 bytes @ 0x20000CAE)
T82D4 008:863.630   Data:  00 00
T82D4 008:863.659 - 0.362ms returns 2 (0x2)
T82D4 008:863.680 JLINK_ReadMemEx(0x20000CB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:863.702   CPU_ReadMem(2 bytes @ 0x20000CB0)
T82D4 008:864.009   Data:  00 00
T82D4 008:864.039 - 0.366ms returns 2 (0x2)
T82D4 008:864.060 JLINK_ReadMemEx(0x20000CB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:864.080   CPU_ReadMem(2 bytes @ 0x20000CB2)
T82D4 008:864.380   Data:  00 00
T82D4 008:864.409 - 0.357ms returns 2 (0x2)
T82D4 008:864.431 JLINK_ReadMemEx(0x20000CB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:864.452   CPU_ReadMem(2 bytes @ 0x20000CB4)
T82D4 008:864.755   Data:  00 00
T82D4 008:864.784 - 0.361ms returns 2 (0x2)
T82D4 008:864.805 JLINK_ReadMemEx(0x20000CB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:864.826   CPU_ReadMem(2 bytes @ 0x20000CB6)
T82D4 008:865.133   Data:  00 00
T82D4 008:865.161 - 0.365ms returns 2 (0x2)
T82D4 008:865.182 JLINK_ReadMemEx(0x20000CB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:865.204   CPU_ReadMem(2 bytes @ 0x20000CB8)
T82D4 008:865.523   Data:  00 00
T82D4 008:865.615 - 0.441ms returns 2 (0x2)
T82D4 008:865.639 JLINK_ReadMemEx(0x20000CBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:865.662   CPU_ReadMem(2 bytes @ 0x20000CBA)
T82D4 008:866.009   Data:  00 00
T82D4 008:866.039 - 0.408ms returns 2 (0x2)
T82D4 008:866.062 JLINK_ReadMemEx(0x20000CBC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:866.084   CPU_ReadMem(4 bytes @ 0x20000CBC)
T82D4 008:866.406   Data:  00 00 00 00
T82D4 008:866.435 - 0.381ms returns 4 (0x4)
T82D4 008:866.457 JLINK_ReadMemEx(0x20000CC0, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:866.478   CPU_ReadMem(4 bytes @ 0x20000CC0)
T82D4 008:866.781   Data:  00 00 00 00
T82D4 008:866.811 - 0.363ms returns 4 (0x4)
T82D4 008:866.833 JLINK_ReadMemEx(0x20000CC4, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:866.854   CPU_ReadMem(4 bytes @ 0x20000CC4)
T82D4 008:867.159   Data:  00 00 00 00
T82D4 008:867.189 - 0.364ms returns 4 (0x4)
T82D4 008:867.210 JLINK_ReadMemEx(0x20000CC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:867.230   CPU_ReadMem(2 bytes @ 0x20000CC8)
T82D4 008:867.530   Data:  00 00
T82D4 008:867.559 - 0.357ms returns 2 (0x2)
T82D4 008:867.581 JLINK_ReadMemEx(0x20000CCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:867.602   CPU_ReadMem(2 bytes @ 0x20000CCA)
T82D4 008:867.905   Data:  00 00
T82D4 008:867.934 - 0.360ms returns 2 (0x2)
T82D4 008:867.955 JLINK_ReadMemEx(0x20000CCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:867.979   CPU_ReadMem(2 bytes @ 0x20000CCC)
T82D4 008:868.279   Data:  00 00
T82D4 008:868.309 - 0.362ms returns 2 (0x2)
T82D4 008:868.330 JLINK_ReadMemEx(0x20000CCE, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:868.351   CPU_ReadMem(4 bytes @ 0x20000CCE)
T82D4 008:868.798   Data:  00 00 00 00
T82D4 008:868.868 - 0.546ms returns 4 (0x4)
T82D4 008:868.890 JLINK_ReadMemEx(0x20000CD2, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:868.911   CPU_ReadMem(4 bytes @ 0x20000CD2)
T82D4 008:869.373   Data:  00 00 00 00
T82D4 008:869.435 - 0.554ms returns 4 (0x4)
T82D4 008:869.456 JLINK_ReadMemEx(0x20000CD6, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:869.476   CPU_ReadMem(4 bytes @ 0x20000CD6)
T82D4 008:869.909   Data:  00 00 00 00
T82D4 008:869.977 - 0.529ms returns 4 (0x4)
T82D4 008:869.997 JLINK_ReadMemEx(0x20000CDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:870.018   CPU_ReadMem(2 bytes @ 0x20000CDA)
T82D4 008:870.316   Data:  00 00
T82D4 008:870.346 - 0.356ms returns 2 (0x2)
T82D4 008:870.379 JLINK_ReadMemEx(0x20000CE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:870.401   CPU_ReadMem(2 bytes @ 0x20000CE2)
T82D4 008:870.697   Data:  00 00
T82D4 008:870.727 - 0.356ms returns 2 (0x2)
T82D4 008:870.749 JLINK_ReadMemEx(0x20000CE4, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:870.770   CPU_ReadMem(4 bytes @ 0x20000CE4)
T82D4 008:871.084   Data:  00 00 00 00
T82D4 008:871.114 - 0.373ms returns 4 (0x4)
T82D4 008:871.135 JLINK_ReadMemEx(0x20000CE8, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:871.155   CPU_ReadMem(4 bytes @ 0x20000CE8)
T82D4 008:871.456   Data:  00 00 00 00
T82D4 008:871.485 - 0.358ms returns 4 (0x4)
T82D4 008:871.506 JLINK_ReadMemEx(0x20000CEC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:871.527   CPU_ReadMem(4 bytes @ 0x20000CEC)
T82D4 008:871.825   Data:  00 00 00 00
T82D4 008:871.855 - 0.357ms returns 4 (0x4)
T82D4 008:871.877 JLINK_ReadMemEx(0x20000CF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:871.897   CPU_ReadMem(2 bytes @ 0x20000CF0)
T82D4 008:872.258   Data:  00 00
T82D4 008:872.287 - 0.417ms returns 2 (0x2)
T82D4 008:872.308 JLINK_ReadMemEx(0x20000CF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:872.330   CPU_ReadMem(2 bytes @ 0x20000CF2)
T82D4 008:872.629   Data:  00 00
T82D4 008:872.658 - 0.358ms returns 2 (0x2)
T82D4 008:872.679 JLINK_ReadMemEx(0x20000CF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:872.700   CPU_ReadMem(2 bytes @ 0x20000CF4)
T82D4 008:873.008   Data:  00 00
T82D4 008:873.038 - 0.367ms returns 2 (0x2)
T82D4 008:873.065 JLINK_ReadMemEx(0x20000CFC, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:873.087   CPU_ReadMem(4 bytes @ 0x20000CFC)
T82D4 008:873.406   Data:  00 00 00 00
T82D4 008:873.436 - 0.379ms returns 4 (0x4)
T82D4 008:873.457 JLINK_ReadMemEx(0x20000D00, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:873.476   CPU_ReadMem(4 bytes @ 0x20000D00)
T82D4 008:873.797   Data:  00 00 00 00
T82D4 008:873.826 - 0.405ms returns 4 (0x4)
T82D4 008:873.875 JLINK_ReadMemEx(0x20000D04, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:873.918   CPU_ReadMem(4 bytes @ 0x20000D04)
T82D4 008:874.436   Data:  00 00 00 00
T82D4 008:874.513 - 0.668ms returns 4 (0x4)
T82D4 008:874.559 JLINK_ReadMemEx(0x20000D08, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:874.608   CPU_ReadMem(2 bytes @ 0x20000D08)
T82D4 008:874.921   Data:  00 00
T82D4 008:874.985 - 0.452ms returns 2 (0x2)
T82D4 008:875.028 JLINK_ReadMemEx(0x20000D0A, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:875.051   CPU_ReadMem(8 bytes @ 0x20000D0A)
T82D4 008:875.658   Data:  00 00 00 00 00 00 00 00
T82D4 008:875.765 - 0.746ms returns 8 (0x8)
T82D4 008:875.788 JLINK_ReadMemEx(0x20000D12, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:875.865   CPU_ReadMem(2 bytes @ 0x20000D12)
T82D4 008:876.255   Data:  00 00
T82D4 008:876.285 - 0.506ms returns 2 (0x2)
T82D4 008:876.309 JLINK_ReadMemEx(0x20000D14, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:876.330   CPU_ReadMem(8 bytes @ 0x20000D14)
T82D4 008:876.658   Data:  00 00 00 00 00 00 00 00
T82D4 008:876.687 - 0.386ms returns 8 (0x8)
T82D4 008:876.710 JLINK_ReadMemEx(0x20000D1C, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:876.736   CPU_ReadMem(8 bytes @ 0x20000D1C)
T82D4 008:877.063   Data:  00 00 00 00 00 00 00 00
T82D4 008:877.091 - 0.389ms returns 8 (0x8)
T82D4 008:877.112 JLINK_ReadMemEx(0x20000D24, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:877.134   CPU_ReadMem(4 bytes @ 0x20000D24)
T82D4 008:877.432   Data:  00 00 00 00
T82D4 008:877.461 - 0.356ms returns 4 (0x4)
T82D4 008:877.482 JLINK_ReadMemEx(0x20000D28, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:877.503   CPU_ReadMem(4 bytes @ 0x20000D28)
T82D4 008:877.801   Data:  00 00 00 00
T82D4 008:877.831 - 0.357ms returns 4 (0x4)
T82D4 008:877.852 JLINK_ReadMemEx(0x20000D2C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:877.873   CPU_ReadMem(4 bytes @ 0x20000D2C)
T82D4 008:878.255   Data:  00 00 00 00
T82D4 008:878.290 - 0.467ms returns 4 (0x4)
T82D4 008:878.336 JLINK_ReadMemEx(0x20000D30, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:878.359   CPU_ReadMem(4 bytes @ 0x20000D30)
T82D4 008:878.683   Data:  00 00 00 00
T82D4 008:878.718 - 0.390ms returns 4 (0x4)
T82D4 008:878.741 JLINK_ReadMemEx(0x20000D34, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:878.763   CPU_ReadMem(4 bytes @ 0x20000D34)
T82D4 008:879.085   Data:  00 00 00 00
T82D4 008:879.114 - 0.382ms returns 4 (0x4)
T82D4 008:879.137 JLINK_ReadMemEx(0x20000D38, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:879.157   CPU_ReadMem(4 bytes @ 0x20000D38)
T82D4 008:879.475   Data:  00 00 00 00
T82D4 008:879.504 - 0.375ms returns 4 (0x4)
T82D4 008:879.527 JLINK_ReadMemEx(0x20000D3C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:879.547   CPU_ReadMem(4 bytes @ 0x20000D3C)
T82D4 008:879.846   Data:  00 00 00 00
T82D4 008:879.875 - 0.356ms returns 4 (0x4)
T82D4 008:879.896 JLINK_ReadMemEx(0x20000D40, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:879.918   CPU_ReadMem(4 bytes @ 0x20000D40)
T82D4 008:880.271   Data:  00 00 00 00
T82D4 008:880.301 - 0.413ms returns 4 (0x4)
T82D4 008:880.441 JLINK_ReadMemEx(0x20000D44, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:880.467   CPU_ReadMem(4 bytes @ 0x20000D44)
T82D4 008:880.872   Data:  00 00 00 00
T82D4 008:880.955 - 0.522ms returns 4 (0x4)
T82D4 008:880.976 JLINK_ReadMemEx(0x20000D48, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:880.998   CPU_ReadMem(4 bytes @ 0x20000D48)
T82D4 008:881.354   Data:  00 00 00 00
T82D4 008:881.488 - 0.545ms returns 4 (0x4)
T82D4 008:881.572 JLINK_ReadMemEx(0x20000D4C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:881.617   CPU_ReadMem(4 bytes @ 0x20000D4C)
T82D4 008:882.037   Data:  00 00 00 00
T82D4 008:882.070 - 0.507ms returns 4 (0x4)
T82D4 008:882.093 JLINK_ReadMemEx(0x20000D50, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:882.116   CPU_ReadMem(4 bytes @ 0x20000D50)
T82D4 008:882.457   Data:  00 00 00 00
T82D4 008:882.490 - 0.405ms returns 4 (0x4)
T82D4 008:882.511 JLINK_ReadMemEx(0x20000D54, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:882.533   CPU_ReadMem(4 bytes @ 0x20000D54)
T82D4 008:882.833   Data:  00 00 00 00
T82D4 008:882.863 - 0.362ms returns 4 (0x4)
T82D4 008:882.886 JLINK_ReadMemEx(0x20000D58, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:882.906   CPU_ReadMem(4 bytes @ 0x20000D58)
T82D4 008:883.259   Data:  00 00 00 00
T82D4 008:883.289 - 0.411ms returns 4 (0x4)
T82D4 008:883.309 JLINK_ReadMemEx(0x20000D5C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:883.331   CPU_ReadMem(4 bytes @ 0x20000D5C)
T82D4 008:883.632   Data:  00 00 00 00
T82D4 008:883.663 - 0.362ms returns 4 (0x4)
T82D4 008:883.684 JLINK_ReadMemEx(0x20000D60, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:883.706   CPU_ReadMem(4 bytes @ 0x20000D60)
T82D4 008:884.009   Data:  00 00 00 00
T82D4 008:884.039 - 0.363ms returns 4 (0x4)
T82D4 008:884.059 JLINK_ReadMemEx(0x20000D64, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:884.081   CPU_ReadMem(4 bytes @ 0x20000D64)
T82D4 008:884.382   Data:  00 00 00 00
T82D4 008:884.412 - 0.362ms returns 4 (0x4)
T82D4 008:884.434 JLINK_ReadMemEx(0x20000D68, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:884.455   CPU_ReadMem(4 bytes @ 0x20000D68)
T82D4 008:884.757   Data:  00 00 00 00
T82D4 008:884.790 - 0.364ms returns 4 (0x4)
T82D4 008:884.811 JLINK_ReadMemEx(0x20000D6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:884.833   CPU_ReadMem(4 bytes @ 0x20000D6C)
T82D4 008:885.134   Data:  00 00 00 00
T82D4 008:885.164 - 0.361ms returns 4 (0x4)
T82D4 008:885.185 JLINK_ReadMemEx(0x20000D70, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:885.207   CPU_ReadMem(4 bytes @ 0x20000D70)
T82D4 008:885.507   Data:  00 00 00 00
T82D4 008:885.537 - 0.361ms returns 4 (0x4)
T82D4 008:885.558 JLINK_ReadMemEx(0x20000D74, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:885.579   CPU_ReadMem(4 bytes @ 0x20000D74)
T82D4 008:885.881   Data:  00 00 00 00
T82D4 008:885.911 - 0.362ms returns 4 (0x4)
T82D4 008:885.936 JLINK_ReadMemEx(0x20000D78, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:885.957   CPU_ReadMem(4 bytes @ 0x20000D78)
T82D4 008:886.282   Data:  00 00 00 00
T82D4 008:886.311 - 0.383ms returns 4 (0x4)
T82D4 008:886.333 JLINK_ReadMemEx(0x20000D7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:886.355   CPU_ReadMem(2 bytes @ 0x20000D7C)
T82D4 008:886.655   Data:  00 00
T82D4 008:886.684 - 0.360ms returns 2 (0x2)
T82D4 008:886.706 JLINK_ReadMemEx(0x20000D7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:886.728   CPU_ReadMem(2 bytes @ 0x20000D7E)
T82D4 008:887.033   Data:  00 00
T82D4 008:887.063 - 0.365ms returns 2 (0x2)
T82D4 008:887.085 JLINK_ReadMemEx(0x20000D80, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:887.106   CPU_ReadMem(2 bytes @ 0x20000D80)
T82D4 008:887.405   Data:  00 00
T82D4 008:887.435 - 0.359ms returns 2 (0x2)
T82D4 008:887.458 JLINK_ReadMemEx(0x20000D82, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:887.478   CPU_ReadMem(8 bytes @ 0x20000D82)
T82D4 008:888.075   Data:  00 00 00 00 00 00 00 00
T82D4 008:888.222 - 0.773ms returns 8 (0x8)
T82D4 008:888.243 JLINK_ReadMemEx(0x20000D8A, 0x8 Bytes, Flags = 0x02000000)
T82D4 008:888.267   CPU_ReadMem(8 bytes @ 0x20000D8A)
T82D4 008:888.853   Data:  00 00 00 00 00 00 00 00
T82D4 008:888.994 - 0.759ms returns 8 (0x8)
T82D4 008:889.014 JLINK_ReadMemEx(0x20000D92, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:889.036   CPU_ReadMem(4 bytes @ 0x20000D92)
T82D4 008:889.476   Data:  00 00 00 00
T82D4 008:889.547 - 0.541ms returns 4 (0x4)
T82D4 008:889.568 JLINK_ReadMemEx(0x20000D96, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:889.590   CPU_ReadMem(4 bytes @ 0x20000D96)
T82D4 008:890.052   Data:  00 00 00 00
T82D4 008:890.132 - 0.572ms returns 4 (0x4)
T82D4 008:890.153 JLINK_ReadMemEx(0x20000D9A, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:890.173   CPU_ReadMem(4 bytes @ 0x20000D9A)
T82D4 008:890.640   Data:  00 00 00 00
T82D4 008:890.682 - 0.553ms returns 4 (0x4)
T82D4 008:890.721 JLINK_ReadMemEx(0x20000D9E, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:890.743   CPU_ReadMem(4 bytes @ 0x20000D9E)
T82D4 008:891.190   Data:  00 00 00 00
T82D4 008:891.224 - 0.526ms returns 4 (0x4)
T82D4 008:891.261 JLINK_ReadMemEx(0x20000DA2, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:891.284   CPU_ReadMem(4 bytes @ 0x20000DA2)
T82D4 008:891.696   Data:  00 00 00 00
T82D4 008:891.730 - 0.477ms returns 4 (0x4)
T82D4 008:891.753 JLINK_ReadMemEx(0x20000DA6, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:891.776   CPU_ReadMem(4 bytes @ 0x20000DA6)
T82D4 008:892.185   Data:  00 00 00 00
T82D4 008:892.216 - 0.470ms returns 4 (0x4)
T82D4 008:892.236 JLINK_ReadMemEx(0x20000DAA, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:892.256   CPU_ReadMem(4 bytes @ 0x20000DAA)
T82D4 008:892.656   Data:  00 00 00 00
T82D4 008:892.687 - 0.459ms returns 4 (0x4)
T82D4 008:892.707 JLINK_ReadMemEx(0x20000DAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 008:892.728   CPU_ReadMem(2 bytes @ 0x20000DAE)
T82D4 008:893.033   Data:  00 00
T82D4 008:893.063 - 0.364ms returns 2 (0x2)
T5E80 008:960.931 JLINK_HasError()
T5E80 008:961.001 JLINK_HasError()
T5E80 008:961.020 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 008:961.048   Data:  E5 A9 BD 08
T5E80 008:961.074   Debug reg: DWT_CYCCNT
T5E80 008:961.099 - 0.087ms returns 1 (0x1)
T82D4 008:966.876 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:966.924   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 008:967.359   Data:  00 00 00 00
T82D4 008:967.397 - 0.530ms returns 4 (0x4)
T82D4 008:967.426 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:967.450   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 008:967.818   Data:  00 00 00 00
T82D4 008:967.871 - 0.454ms returns 4 (0x4)
T82D4 008:967.899 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:967.922   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 008:968.289   Data:  38 80 00 20
T82D4 008:968.336 - 0.445ms returns 4 (0x4)
T82D4 008:968.602 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 008:968.632   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 008:969.042   Data:  38 80 00 20
T82D4 008:969.091 - 0.498ms returns 4 (0x4)
T5E80 008:981.058 JLINK_IsHalted()
T5E80 008:981.494 - 0.450ms returns FALSE
T5E80 009:082.243 JLINK_HasError()
T5E80 009:082.392 JLINK_IsHalted()
T5E80 009:082.817 - 0.476ms returns FALSE
T5E80 009:183.694 JLINK_HasError()
T5E80 009:183.775 JLINK_IsHalted()
T5E80 009:184.232 - 0.494ms returns FALSE
T5E80 009:284.955 JLINK_HasError()
T5E80 009:285.096 JLINK_IsHalted()
T5E80 009:285.461 - 0.378ms returns FALSE
T5E80 009:385.696 JLINK_HasError()
T5E80 009:385.752 JLINK_IsHalted()
T5E80 009:386.202 - 0.485ms returns FALSE
T82D4 009:443.420 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:443.488   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 009:443.848   Data:  00 00
T82D4 009:443.901 - 0.491ms returns 2 (0x2)
T82D4 009:443.928 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:443.951   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 009:444.286   Data:  00 00
T82D4 009:444.330 - 0.410ms returns 2 (0x2)
T82D4 009:444.352 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:444.374   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 009:444.762   Data:  00 00
T82D4 009:444.817 - 0.488ms returns 2 (0x2)
T82D4 009:444.856 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:444.901   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 009:445.241   Data:  00 00
T82D4 009:445.280 - 0.433ms returns 2 (0x2)
T82D4 009:445.306 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:445.344   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 009:445.656   Data:  00 00
T82D4 009:445.690 - 0.393ms returns 2 (0x2)
T82D4 009:445.714 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:445.735   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 009:446.059   Data:  00 00
T82D4 009:446.090 - 0.385ms returns 2 (0x2)
T82D4 009:446.112 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:446.133   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 009:446.431   Data:  00 00
T82D4 009:446.461 - 0.356ms returns 2 (0x2)
T82D4 009:446.483 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:446.504   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 009:446.806   Data:  00 00
T82D4 009:446.858 - 0.384ms returns 2 (0x2)
T82D4 009:446.880 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:446.902   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 009:447.235   Data:  00 00
T82D4 009:447.265 - 0.393ms returns 2 (0x2)
T82D4 009:447.287 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:447.309   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 009:447.605   Data:  00 00
T82D4 009:447.635 - 0.357ms returns 2 (0x2)
T82D4 009:447.657 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:447.677   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 009:448.009   Data:  00 00
T82D4 009:448.038 - 0.389ms returns 2 (0x2)
T82D4 009:448.060 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:448.081   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 009:448.379   Data:  00 00
T82D4 009:448.408 - 0.377ms returns 2 (0x2)
T82D4 009:448.451 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:448.487   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 009:448.804   Data:  00 00
T82D4 009:448.833 - 0.389ms returns 2 (0x2)
T82D4 009:448.853 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:448.880   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 009:449.231   Data:  00 00
T82D4 009:449.261 - 0.416ms returns 2 (0x2)
T82D4 009:449.282 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:449.303   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 009:449.601   Data:  00 00
T82D4 009:449.630 - 0.357ms returns 2 (0x2)
T82D4 009:449.653 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:449.673   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 009:450.050   Data:  00 00
T82D4 009:450.079 - 0.434ms returns 2 (0x2)
T82D4 009:450.100 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:450.121   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 009:450.430   Data:  00 00
T82D4 009:450.459 - 0.367ms returns 2 (0x2)
T82D4 009:450.480 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:450.502   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 009:450.820   Data:  00 00
T82D4 009:450.850 - 0.377ms returns 2 (0x2)
T82D4 009:450.871 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:450.891   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 009:451.234   Data:  00 00
T82D4 009:451.263 - 0.401ms returns 2 (0x2)
T82D4 009:451.286 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:451.306   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 009:451.604   Data:  00 00
T82D4 009:451.633 - 0.355ms returns 2 (0x2)
T82D4 009:451.653 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:451.675   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 009:452.008   Data:  00 00
T82D4 009:452.037 - 0.392ms returns 2 (0x2)
T82D4 009:452.058 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:452.080   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 009:452.379   Data:  00 00
T82D4 009:452.409 - 0.359ms returns 2 (0x2)
T82D4 009:452.430 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:452.450   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 009:452.755   Data:  00 00
T82D4 009:452.784 - 0.363ms returns 2 (0x2)
T82D4 009:452.806 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:452.827   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 009:453.150   Data:  00 00
T82D4 009:453.194 - 0.396ms returns 2 (0x2)
T82D4 009:453.216 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:453.237   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 009:453.554   Data:  00 00
T82D4 009:453.583 - 0.375ms returns 2 (0x2)
T82D4 009:453.604 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:453.625   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 009:453.921   Data:  00 00
T82D4 009:453.950 - 0.354ms returns 2 (0x2)
T82D4 009:453.971 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:453.992   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 009:454.306   Data:  00 00
T82D4 009:454.336 - 0.374ms returns 2 (0x2)
T82D4 009:454.357 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:454.377   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 009:454.680   Data:  00 00
T82D4 009:454.709 - 0.361ms returns 2 (0x2)
T82D4 009:454.732 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:454.752   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 009:455.058   Data:  00 00
T82D4 009:455.087 - 0.363ms returns 2 (0x2)
T82D4 009:455.108 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:455.129   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 009:455.424   Data:  00 00
T82D4 009:455.454 - 0.354ms returns 2 (0x2)
T82D4 009:455.475 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:455.496   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 009:455.870   Data:  00 00
T82D4 009:455.926 - 0.544ms returns 2 (0x2)
T82D4 009:456.044 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:456.082   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 009:456.498   Data:  00 00
T82D4 009:456.534 - 0.498ms returns 2 (0x2)
T82D4 009:456.558 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:456.579   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 009:456.939   Data:  00 00
T82D4 009:456.983 - 0.448ms returns 2 (0x2)
T82D4 009:457.045 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:457.085   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 009:457.515   Data:  00 00
T82D4 009:457.574 - 0.538ms returns 2 (0x2)
T82D4 009:457.599 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:457.641   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 009:458.079   Data:  00 00
T82D4 009:458.116 - 0.525ms returns 2 (0x2)
T82D4 009:458.139 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:458.162   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 009:458.468   Data:  00 00
T82D4 009:458.502 - 0.372ms returns 2 (0x2)
T82D4 009:458.526 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:458.549   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 009:458.847   Data:  00 00
T82D4 009:458.877 - 0.359ms returns 2 (0x2)
T82D4 009:458.898 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:458.920   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 009:459.236   Data:  00 00
T82D4 009:459.266 - 0.376ms returns 2 (0x2)
T82D4 009:459.287 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:459.308   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 009:459.609   Data:  00 00
T82D4 009:459.639 - 0.361ms returns 2 (0x2)
T82D4 009:459.662 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:459.683   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 009:460.008   Data:  00 00
T82D4 009:460.037 - 0.382ms returns 2 (0x2)
T82D4 009:460.059 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:460.080   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 009:460.380   Data:  00 00
T82D4 009:460.409 - 0.359ms returns 2 (0x2)
T82D4 009:460.430 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:460.452   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 009:460.771   Data:  00 00
T82D4 009:460.856 - 0.448ms returns 2 (0x2)
T82D4 009:460.893 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:460.915   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 009:461.374   Data:  00 00
T82D4 009:461.409 - 0.524ms returns 2 (0x2)
T82D4 009:461.431 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:461.452   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 009:461.755   Data:  00 00
T82D4 009:461.784 - 0.362ms returns 2 (0x2)
T82D4 009:461.806 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:461.828   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 009:462.132   Data:  00 00
T82D4 009:462.162 - 0.364ms returns 2 (0x2)
T82D4 009:462.183 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:462.204   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 009:462.505   Data:  00 00
T82D4 009:462.535 - 0.361ms returns 2 (0x2)
T82D4 009:462.557 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:462.579   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 009:462.880   Data:  00 00
T82D4 009:462.910 - 0.360ms returns 2 (0x2)
T82D4 009:462.932 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:462.953   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 009:463.310   Data:  00 00
T82D4 009:463.339 - 0.416ms returns 2 (0x2)
T82D4 009:463.362 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:463.382   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 009:463.776   Data:  00 00
T82D4 009:463.841 - 0.488ms returns 2 (0x2)
T82D4 009:463.881 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:463.939   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 009:464.316   Data:  00 00
T82D4 009:464.411 - 0.553ms returns 2 (0x2)
T82D4 009:464.483 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:464.540   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 009:465.031   Data:  00 00
T82D4 009:465.065 - 0.590ms returns 2 (0x2)
T82D4 009:465.104 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:465.127   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 009:465.490   Data:  00 00
T82D4 009:465.528 - 0.432ms returns 2 (0x2)
T82D4 009:465.549 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:465.572   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 009:465.905   Data:  00 00
T82D4 009:465.936 - 0.395ms returns 2 (0x2)
T82D4 009:466.016 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:466.041   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 009:466.370   Data:  00 00
T82D4 009:466.400 - 0.393ms returns 2 (0x2)
T82D4 009:466.421 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:466.444   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 009:466.755   Data:  00 00
T82D4 009:466.786 - 0.372ms returns 2 (0x2)
T82D4 009:466.808 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:466.829   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 009:467.133   Data:  00 00
T82D4 009:467.163 - 0.364ms returns 2 (0x2)
T82D4 009:467.186 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:467.206   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 009:467.505   Data:  00 00
T82D4 009:467.534 - 0.356ms returns 2 (0x2)
T82D4 009:467.555 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:467.578   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 009:467.880   Data:  00 00
T82D4 009:467.910 - 0.363ms returns 2 (0x2)
T82D4 009:467.932 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:467.954   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 009:468.255   Data:  00 00
T82D4 009:468.286 - 0.370ms returns 2 (0x2)
T82D4 009:468.315 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:468.336   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 009:468.654   Data:  00 00
T82D4 009:468.708 - 0.402ms returns 2 (0x2)
T82D4 009:468.730 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:468.766   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 009:469.083   Data:  00 00
T82D4 009:469.113 - 0.391ms returns 2 (0x2)
T82D4 009:469.135 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:469.157   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 009:469.455   Data:  00 00
T82D4 009:469.484 - 0.357ms returns 2 (0x2)
T82D4 009:469.505 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:469.527   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 009:469.822   Data:  00 00
T82D4 009:469.853 - 0.356ms returns 2 (0x2)
T82D4 009:469.874 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:469.897   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 009:470.232   Data:  00 00
T82D4 009:470.263 - 0.397ms returns 2 (0x2)
T82D4 009:470.285 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:470.305   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 009:470.604   Data:  00 00
T82D4 009:470.634 - 0.357ms returns 2 (0x2)
T82D4 009:470.656 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:470.678   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 009:471.035   Data:  00 00
T82D4 009:471.066 - 0.419ms returns 2 (0x2)
T82D4 009:471.089 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:471.109   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 009:471.429   Data:  00 00
T82D4 009:471.458 - 0.377ms returns 2 (0x2)
T82D4 009:471.479 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:471.500   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 009:471.797   Data:  00 00
T82D4 009:471.827 - 0.356ms returns 2 (0x2)
T82D4 009:471.848 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:471.871   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 009:472.231   Data:  00 00
T82D4 009:472.262 - 0.421ms returns 2 (0x2)
T82D4 009:472.282 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:472.303   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 009:472.620   Data:  00 00
T82D4 009:472.649 - 0.375ms returns 2 (0x2)
T82D4 009:472.672 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:472.692   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 009:473.009   Data:  00 00
T82D4 009:473.038 - 0.374ms returns 2 (0x2)
T82D4 009:473.060 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:473.084   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 009:473.487   Data:  00 00
T82D4 009:473.516 - 0.464ms returns 2 (0x2)
T82D4 009:473.538 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:473.656   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 009:474.090   Data:  00 00
T82D4 009:474.189 - 0.660ms returns 2 (0x2)
T82D4 009:474.214 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:474.253   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 009:474.592   Data:  00 00
T82D4 009:474.629 - 0.423ms returns 2 (0x2)
T82D4 009:474.653 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:474.699   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 009:475.036   Data:  00 00
T82D4 009:475.068 - 0.423ms returns 2 (0x2)
T82D4 009:475.090 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:475.112   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 009:475.482   Data:  00 00
T82D4 009:475.512 - 0.430ms returns 2 (0x2)
T82D4 009:475.534 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:475.554   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 009:475.854   Data:  00 00
T82D4 009:475.883 - 0.357ms returns 2 (0x2)
T82D4 009:475.905 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:475.926   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 009:476.237   Data:  00 00
T82D4 009:476.265 - 0.368ms returns 2 (0x2)
T82D4 009:476.287 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:476.308   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 009:476.607   Data:  00 00
T82D4 009:476.636 - 0.357ms returns 2 (0x2)
T82D4 009:476.657 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:476.679   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 009:477.007   Data:  00 00
T82D4 009:477.037 - 0.388ms returns 2 (0x2)
T82D4 009:477.058 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:477.078   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 009:477.380   Data:  00 00
T82D4 009:477.409 - 0.358ms returns 2 (0x2)
T82D4 009:477.430 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:477.451   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 009:477.761   Data:  00 00
T82D4 009:477.790 - 0.368ms returns 2 (0x2)
T82D4 009:477.811 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:477.833   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 009:478.132   Data:  00 00
T82D4 009:478.161 - 0.358ms returns 2 (0x2)
T82D4 009:478.182 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:478.204   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 009:478.505   Data:  00 00
T82D4 009:478.535 - 0.361ms returns 2 (0x2)
T82D4 009:478.556 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:478.576   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 009:478.972   Data:  00 00
T82D4 009:479.039 - 0.491ms returns 2 (0x2)
T82D4 009:479.062 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:479.127   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 009:479.484   Data:  00 00
T82D4 009:479.514 - 0.460ms returns 2 (0x2)
T82D4 009:479.535 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:479.557   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 009:479.854   Data:  00 00
T82D4 009:479.884 - 0.357ms returns 2 (0x2)
T82D4 009:479.905 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:479.926   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 009:480.236   Data:  00 00
T82D4 009:480.266 - 0.370ms returns 2 (0x2)
T82D4 009:480.289 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:480.308   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 009:480.609   Data:  00 00
T82D4 009:480.638 - 0.358ms returns 2 (0x2)
T82D4 009:480.659 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:480.680   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 009:481.008   Data:  00 00
T82D4 009:481.037 - 0.386ms returns 2 (0x2)
T82D4 009:481.057 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:481.082   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 009:481.379   Data:  00 00
T82D4 009:481.409 - 0.360ms returns 2 (0x2)
T82D4 009:481.430 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:481.451   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 009:481.754   Data:  00 00
T82D4 009:481.784 - 0.362ms returns 2 (0x2)
T82D4 009:481.806 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:481.826   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 009:482.133   Data:  00 00
T82D4 009:482.162 - 0.364ms returns 2 (0x2)
T82D4 009:482.183 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:482.204   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 009:482.506   Data:  00 00
T82D4 009:482.535 - 0.359ms returns 2 (0x2)
T82D4 009:482.555 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:482.577   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 009:482.880   Data:  00 00
T82D4 009:482.909 - 0.361ms returns 2 (0x2)
T82D4 009:482.930 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:482.951   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 009:483.373   Data:  00 00
T82D4 009:483.403 - 0.481ms returns 2 (0x2)
T82D4 009:483.424 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:483.443   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 009:483.754   Data:  00 00
T82D4 009:483.783 - 0.367ms returns 2 (0x2)
T82D4 009:483.805 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:483.826   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 009:484.133   Data:  00 00
T82D4 009:484.161 - 0.364ms returns 2 (0x2)
T82D4 009:484.182 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:484.203   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 009:484.505   Data:  00 00
T82D4 009:484.534 - 0.360ms returns 2 (0x2)
T82D4 009:484.555 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:484.576   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 009:484.879   Data:  00 00
T82D4 009:484.910 - 0.363ms returns 2 (0x2)
T82D4 009:484.931 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:484.950   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 009:485.255   Data:  00 00
T82D4 009:485.285 - 0.382ms returns 2 (0x2)
T82D4 009:485.342 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:485.363   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 009:485.704   Data:  00 00
T82D4 009:485.733 - 0.399ms returns 2 (0x2)
T82D4 009:485.753 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:485.775   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 009:486.083   Data:  00 00
T82D4 009:486.139 - 0.422ms returns 2 (0x2)
T82D4 009:486.195 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:486.248   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 009:486.670   Data:  00 00
T82D4 009:486.704 - 0.517ms returns 2 (0x2)
T82D4 009:486.727 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:486.750   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 009:487.057   Data:  00 00
T82D4 009:487.088 - 0.369ms returns 2 (0x2)
T5E80 009:487.111 JLINK_HasError()
T5E80 009:487.145 JLINK_HasError()
T5E80 009:487.165 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 009:487.188   Data:  E5 A9 BD 08
T5E80 009:487.214   Debug reg: DWT_CYCCNT
T5E80 009:487.238 - 0.081ms returns 1 (0x1)
T82D4 009:487.260 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:487.286   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 009:487.588   Data:  00 00
T82D4 009:487.618 - 0.367ms returns 2 (0x2)
T82D4 009:487.640 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:487.660   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 009:488.063   Data:  00 00
T82D4 009:488.091 - 0.459ms returns 2 (0x2)
T82D4 009:488.112 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:488.134   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 009:488.430   Data:  00 00
T82D4 009:488.460 - 0.356ms returns 2 (0x2)
T82D4 009:488.481 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:488.502   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 009:488.805   Data:  00 00
T82D4 009:488.836 - 0.363ms returns 2 (0x2)
T82D4 009:488.856 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:488.877   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 009:489.236   Data:  00 00
T82D4 009:489.265 - 0.417ms returns 2 (0x2)
T82D4 009:489.294 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:489.315   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 009:489.629   Data:  00 00
T82D4 009:489.658 - 0.371ms returns 2 (0x2)
T82D4 009:489.679 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:489.700   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 009:490.007   Data:  00 00
T82D4 009:490.037 - 0.366ms returns 2 (0x2)
T82D4 009:490.058 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:490.079   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 009:490.398   Data:  00 00
T82D4 009:490.453 - 0.404ms returns 2 (0x2)
T82D4 009:490.477 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:490.500   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 009:490.821   Data:  00 00
T82D4 009:490.856 - 0.387ms returns 2 (0x2)
T82D4 009:490.879 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:490.902   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 009:491.241   Data:  00 00
T82D4 009:491.274 - 0.403ms returns 2 (0x2)
T82D4 009:491.297 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:491.320   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 009:491.637   Data:  00 00
T82D4 009:491.668 - 0.379ms returns 2 (0x2)
T82D4 009:491.691 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:491.715   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 009:492.032   Data:  00 00
T82D4 009:492.063 - 0.380ms returns 2 (0x2)
T82D4 009:492.086 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:492.109   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 009:492.430   Data:  00 00
T82D4 009:492.462 - 0.384ms returns 2 (0x2)
T82D4 009:492.485 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:492.507   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 009:492.808   Data:  00 00
T82D4 009:492.840 - 0.364ms returns 2 (0x2)
T82D4 009:492.864 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:492.886   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 009:493.229   Data:  00 00
T82D4 009:493.261 - 0.405ms returns 2 (0x2)
T82D4 009:493.283 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:493.306   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 009:493.613   Data:  00 00
T82D4 009:493.645 - 0.370ms returns 2 (0x2)
T82D4 009:493.668 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:493.691   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 009:494.008   Data:  00 00
T82D4 009:494.038 - 0.379ms returns 2 (0x2)
T82D4 009:494.062 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:494.084   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 009:494.405   Data:  00 00
T82D4 009:494.436 - 0.383ms returns 2 (0x2)
T82D4 009:494.459 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:494.481   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 009:494.780   Data:  00 00
T82D4 009:494.812 - 0.361ms returns 2 (0x2)
T82D4 009:494.835 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:494.858   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 009:495.158   Data:  00 00
T82D4 009:495.189 - 0.362ms returns 2 (0x2)
T82D4 009:495.211 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:495.234   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 009:495.531   Data:  00 00
T82D4 009:495.562 - 0.359ms returns 2 (0x2)
T82D4 009:495.674 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:495.713   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 009:496.112   Data:  00 00
T82D4 009:496.147 - 0.481ms returns 2 (0x2)
T82D4 009:496.170 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:496.194   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 009:496.505   Data:  00 00
T82D4 009:496.543 - 0.382ms returns 2 (0x2)
T82D4 009:496.566 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:496.589   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 009:496.906   Data:  00 00
T82D4 009:496.938 - 0.381ms returns 2 (0x2)
T82D4 009:496.960 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:496.981   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 009:497.280   Data:  00 00
T82D4 009:497.310 - 0.358ms returns 2 (0x2)
T82D4 009:497.333 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:497.355   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 009:497.656   Data:  00 00
T82D4 009:497.686 - 0.361ms returns 2 (0x2)
T82D4 009:497.708 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:497.730   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 009:498.033   Data:  00 00
T82D4 009:498.064 - 0.365ms returns 2 (0x2)
T82D4 009:498.086 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:498.108   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 009:498.405   Data:  00 00
T82D4 009:498.436 - 0.359ms returns 2 (0x2)
T82D4 009:498.458 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:498.479   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 009:498.780   Data:  00 00
T82D4 009:498.810 - 0.361ms returns 2 (0x2)
T82D4 009:498.833 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:498.854   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 009:499.158   Data:  00 00
T82D4 009:499.188 - 0.364ms returns 2 (0x2)
T82D4 009:499.211 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:499.233   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 009:499.531   Data:  00 00
T82D4 009:499.562 - 0.359ms returns 2 (0x2)
T82D4 009:499.585 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:499.623   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 009:499.933   Data:  00 00
T82D4 009:499.964 - 0.387ms returns 2 (0x2)
T82D4 009:499.985 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:500.008   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 009:500.306   Data:  00 00
T82D4 009:500.337 - 0.361ms returns 2 (0x2)
T82D4 009:500.360 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:500.380   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 009:500.680   Data:  00 00
T82D4 009:500.712 - 0.361ms returns 2 (0x2)
T82D4 009:500.734 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:500.755   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 009:501.058   Data:  00 00
T82D4 009:501.089 - 0.364ms returns 2 (0x2)
T82D4 009:501.112 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:501.133   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 009:501.431   Data:  00 00
T82D4 009:501.461 - 0.357ms returns 2 (0x2)
T82D4 009:501.482 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:501.505   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 009:501.821   Data:  00 00
T82D4 009:501.919 - 0.450ms returns 2 (0x2)
T82D4 009:501.949 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:501.988   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 009:502.321   Data:  00 00
T82D4 009:502.356 - 0.415ms returns 2 (0x2)
T82D4 009:502.380 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:502.403   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 009:502.744   Data:  00 00
T82D4 009:502.777 - 0.406ms returns 2 (0x2)
T82D4 009:502.800 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:502.822   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 009:503.145   Data:  00 00
T82D4 009:503.183 - 0.392ms returns 2 (0x2)
T82D4 009:503.209 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:503.232   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 009:503.557   Data:  00 00
T82D4 009:503.590 - 0.389ms returns 2 (0x2)
T82D4 009:503.613 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:503.637   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 009:504.058   Data:  00 00
T82D4 009:504.090 - 0.485ms returns 2 (0x2)
T82D4 009:504.112 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:504.140   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 009:504.440   Data:  00 00
T82D4 009:504.471 - 0.369ms returns 2 (0x2)
T82D4 009:504.494 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:504.515   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 009:504.816   Data:  00 00
T82D4 009:504.846 - 0.361ms returns 2 (0x2)
T82D4 009:504.869 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:504.891   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 009:505.234   Data:  00 00
T82D4 009:505.265 - 0.404ms returns 2 (0x2)
T82D4 009:505.286 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:505.309   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 009:505.607   Data:  00 00
T82D4 009:505.637 - 0.359ms returns 2 (0x2)
T82D4 009:505.659 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:505.682   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 009:506.007   Data:  00 00
T82D4 009:506.039 - 0.389ms returns 2 (0x2)
T82D4 009:506.061 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:506.081   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 009:506.380   Data:  00 00
T82D4 009:506.410 - 0.357ms returns 2 (0x2)
T82D4 009:506.433 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:506.455   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 009:506.756   Data:  00 00
T82D4 009:506.786 - 0.361ms returns 2 (0x2)
T82D4 009:506.808 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:506.831   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 009:507.159   Data:  00 00
T82D4 009:507.195 - 0.395ms returns 2 (0x2)
T82D4 009:507.218 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:507.262   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 009:507.586   Data:  00 00
T82D4 009:507.618 - 0.408ms returns 2 (0x2)
T82D4 009:507.640 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:507.664   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 009:508.083   Data:  00 00
T82D4 009:508.115 - 0.483ms returns 2 (0x2)
T82D4 009:508.138 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:508.159   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 009:508.468   Data:  00 00
T82D4 009:508.499 - 0.369ms returns 2 (0x2)
T82D4 009:508.522 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:508.544   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 009:508.844   Data:  00 00
T82D4 009:508.874 - 0.360ms returns 2 (0x2)
T82D4 009:508.896 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:508.918   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 009:509.234   Data:  00 00
T82D4 009:509.264 - 0.377ms returns 2 (0x2)
T82D4 009:509.286 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:509.309   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 009:509.607   Data:  00 00
T82D4 009:509.639 - 0.361ms returns 2 (0x2)
T82D4 009:509.662 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:509.690   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 009:510.007   Data:  00 00
T82D4 009:510.038 - 0.385ms returns 2 (0x2)
T82D4 009:510.061 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:510.083   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 009:510.380   Data:  00 00
T82D4 009:510.410 - 0.358ms returns 2 (0x2)
T82D4 009:510.432 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:510.455   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 009:510.755   Data:  00 00
T82D4 009:510.787 - 0.363ms returns 2 (0x2)
T82D4 009:510.808 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:510.831   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 009:511.132   Data:  00 00
T82D4 009:511.164 - 0.364ms returns 2 (0x2)
T82D4 009:511.185 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:511.207   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 009:511.505   Data:  00 00
T82D4 009:511.537 - 0.360ms returns 2 (0x2)
T82D4 009:511.559 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:511.582   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 009:511.906   Data:  00 00
T82D4 009:511.944 - 0.393ms returns 2 (0x2)
T82D4 009:511.968 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:511.989   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 009:512.305   Data:  00 00
T82D4 009:512.335 - 0.376ms returns 2 (0x2)
T82D4 009:512.358 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:512.380   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 009:512.682   Data:  00 00
T82D4 009:512.712 - 0.362ms returns 2 (0x2)
T82D4 009:512.734 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:512.757   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 009:513.058   Data:  00 00
T82D4 009:513.089 - 0.363ms returns 2 (0x2)
T82D4 009:513.111 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:513.134   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 009:513.439   Data:  00 00
T82D4 009:513.471 - 0.369ms returns 2 (0x2)
T82D4 009:513.493 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:513.513   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 009:513.814   Data:  00 00
T82D4 009:513.845 - 0.360ms returns 2 (0x2)
T82D4 009:513.868 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:513.889   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 009:514.235   Data:  00 00
T82D4 009:514.265 - 0.406ms returns 2 (0x2)
T82D4 009:514.287 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:514.310   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 009:514.608   Data:  00 00
T82D4 009:514.639 - 0.360ms returns 2 (0x2)
T82D4 009:514.660 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:514.683   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 009:515.008   Data:  00 00
T82D4 009:515.040 - 0.388ms returns 2 (0x2)
T82D4 009:515.062 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:515.083   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 009:515.380   Data:  00 00
T82D4 009:515.411 - 0.357ms returns 2 (0x2)
T82D4 009:515.434 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:515.456   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 009:515.755   Data:  00 00
T82D4 009:515.785 - 0.360ms returns 2 (0x2)
T82D4 009:515.807 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:515.829   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 009:516.133   Data:  00 00
T82D4 009:516.164 - 0.365ms returns 2 (0x2)
T82D4 009:516.185 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:516.208   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 009:516.506   Data:  00 00
T82D4 009:516.537 - 0.361ms returns 2 (0x2)
T82D4 009:516.560 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:516.580   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 009:516.890   Data:  00 00
T82D4 009:516.948 - 0.401ms returns 2 (0x2)
T82D4 009:516.984 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:517.030   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 009:517.338   Data:  00 00
T82D4 009:517.370 - 0.395ms returns 2 (0x2)
T82D4 009:517.393 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:517.417   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 009:517.721   Data:  00 00
T82D4 009:517.754 - 0.369ms returns 2 (0x2)
T82D4 009:517.777 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:517.799   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 009:518.109   Data:  00 00
T82D4 009:518.140 - 0.372ms returns 2 (0x2)
T82D4 009:518.163 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:518.184   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 009:518.506   Data:  00 00
T82D4 009:518.536 - 0.382ms returns 2 (0x2)
T82D4 009:518.558 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:518.581   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 009:518.882   Data:  00 00
T82D4 009:518.913 - 0.363ms returns 2 (0x2)
T82D4 009:518.935 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:518.957   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 009:519.257   Data:  00 00
T82D4 009:519.294 - 0.368ms returns 2 (0x2)
T82D4 009:519.357 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:519.381   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 009:519.679   Data:  00 00
T82D4 009:519.710 - 0.363ms returns 2 (0x2)
T82D4 009:519.733 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:519.753   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 009:520.058   Data:  00 00
T82D4 009:520.088 - 0.364ms returns 2 (0x2)
T82D4 009:520.111 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:520.132   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 009:520.431   Data:  00 00
T82D4 009:520.463 - 0.361ms returns 2 (0x2)
T82D4 009:520.485 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:520.507   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 009:520.805   Data:  00 00
T82D4 009:520.836 - 0.359ms returns 2 (0x2)
T82D4 009:520.858 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:520.880   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 009:521.232   Data:  00 00
T82D4 009:521.264 - 0.414ms returns 2 (0x2)
T82D4 009:521.286 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:521.307   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 009:521.605   Data:  00 00
T82D4 009:521.635 - 0.358ms returns 2 (0x2)
T82D4 009:521.660 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:521.681   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 009:522.007   Data:  00 00
T82D4 009:522.038 - 0.386ms returns 2 (0x2)
T82D4 009:522.061 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:522.083   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 009:522.380   Data:  00 00
T82D4 009:522.411 - 0.358ms returns 2 (0x2)
T82D4 009:522.433 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:522.457   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 009:522.755   Data:  00 00
T82D4 009:522.785 - 0.360ms returns 2 (0x2)
T82D4 009:522.808 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:522.831   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 009:523.133   Data:  00 00
T82D4 009:523.163 - 0.363ms returns 2 (0x2)
T82D4 009:523.186 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:523.208   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 009:523.536   Data:  00 00
T82D4 009:523.623 - 0.467ms returns 2 (0x2)
T82D4 009:523.672 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:523.746   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 009:524.083   Data:  00 00
T82D4 009:524.117 - 0.454ms returns 2 (0x2)
T82D4 009:524.142 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:524.166   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 009:524.472   Data:  00 00
T82D4 009:524.506 - 0.373ms returns 2 (0x2)
T82D4 009:524.530 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:524.552   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 009:524.855   Data:  00 00
T82D4 009:524.887 - 0.367ms returns 2 (0x2)
T82D4 009:524.927 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:524.949   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 009:525.256   Data:  00 00
T82D4 009:525.287 - 0.368ms returns 2 (0x2)
T82D4 009:525.310 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:525.332   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 009:525.631   Data:  00 00
T82D4 009:525.663 - 0.361ms returns 2 (0x2)
T82D4 009:525.739 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:525.764   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 009:526.083   Data:  00 00
T82D4 009:526.114 - 0.384ms returns 2 (0x2)
T82D4 009:526.135 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:526.156   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 009:526.456   Data:  00 00
T82D4 009:526.488 - 0.361ms returns 2 (0x2)
T82D4 009:526.509 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:526.529   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 009:526.830   Data:  00 00
T82D4 009:526.861 - 0.360ms returns 2 (0x2)
T82D4 009:526.882 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:526.910   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 009:527.236   Data:  00 00
T82D4 009:527.268 - 0.394ms returns 2 (0x2)
T82D4 009:527.289 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:527.311   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 009:527.630   Data:  00 00
T82D4 009:527.691 - 0.411ms returns 2 (0x2)
T82D4 009:527.713 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:527.751   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 009:528.130   Data:  00 00
T82D4 009:528.191 - 0.488ms returns 2 (0x2)
T82D4 009:528.229 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:528.276   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 009:528.637   Data:  00 00
T82D4 009:528.708 - 0.488ms returns 2 (0x2)
T82D4 009:528.731 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:528.791   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 009:529.110   Data:  00 00
T82D4 009:529.143 - 0.421ms returns 2 (0x2)
T82D4 009:529.165 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:529.187   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 009:529.507   Data:  00 00
T82D4 009:529.539 - 0.383ms returns 2 (0x2)
T82D4 009:529.562 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:529.583   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 009:529.880   Data:  00 00
T82D4 009:529.912 - 0.358ms returns 2 (0x2)
T82D4 009:529.934 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:529.963   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 009:530.280   Data:  00 00
T82D4 009:530.311 - 0.386ms returns 2 (0x2)
T82D4 009:530.333 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:530.356   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 009:530.656   Data:  00 00
T82D4 009:530.687 - 0.362ms returns 2 (0x2)
T82D4 009:530.708 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:530.731   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 009:531.032   Data:  00 00
T82D4 009:531.065 - 0.365ms returns 2 (0x2)
T82D4 009:531.086 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:531.107   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 009:531.406   Data:  00 00
T82D4 009:531.438 - 0.361ms returns 2 (0x2)
T82D4 009:531.461 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:531.483   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 009:531.804   Data:  00 00
T82D4 009:531.835 - 0.383ms returns 2 (0x2)
T82D4 009:531.857 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:531.880   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 009:532.230   Data:  00 00
T82D4 009:532.262 - 0.414ms returns 2 (0x2)
T82D4 009:532.283 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:532.306   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 009:532.620   Data:  00 00
T82D4 009:532.673 - 0.399ms returns 2 (0x2)
T82D4 009:532.697 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:532.723   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 009:533.036   Data:  00 00
T82D4 009:533.069 - 0.381ms returns 2 (0x2)
T82D4 009:533.092 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:533.116   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 009:533.431   Data:  00 00
T82D4 009:533.464 - 0.381ms returns 2 (0x2)
T82D4 009:533.485 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:533.508   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 009:533.808   Data:  00 00
T82D4 009:533.841 - 0.364ms returns 2 (0x2)
T82D4 009:533.862 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:533.883   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 009:534.230   Data:  00 00
T82D4 009:534.261 - 0.408ms returns 2 (0x2)
T82D4 009:534.283 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:534.306   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 009:534.608   Data:  00 00
T82D4 009:534.639 - 0.364ms returns 2 (0x2)
T82D4 009:534.661 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:534.740   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 009:535.086   Data:  00 00
T82D4 009:535.124 - 0.487ms returns 2 (0x2)
T82D4 009:535.162 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:535.186   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 009:535.524   Data:  00 00
T82D4 009:535.558 - 0.404ms returns 2 (0x2)
T82D4 009:535.580 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:535.601   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 009:535.906   Data:  00 00
T82D4 009:535.937 - 0.365ms returns 2 (0x2)
T82D4 009:535.958 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:535.980   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 009:536.280   Data:  00 00
T82D4 009:536.311 - 0.361ms returns 2 (0x2)
T82D4 009:536.331 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:536.354   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 009:536.656   Data:  00 00
T82D4 009:536.687 - 0.364ms returns 2 (0x2)
T82D4 009:536.708 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:536.730   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 009:537.033   Data:  00 00
T82D4 009:537.065 - 0.365ms returns 2 (0x2)
T82D4 009:537.086 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:537.106   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 009:537.406   Data:  00 00
T82D4 009:537.437 - 0.359ms returns 2 (0x2)
T82D4 009:537.458 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:537.480   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 009:537.780   Data:  00 00
T82D4 009:537.810 - 0.360ms returns 2 (0x2)
T82D4 009:537.831 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:537.853   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 009:538.158   Data:  00 00
T82D4 009:538.189 - 0.367ms returns 2 (0x2)
T82D4 009:538.210 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:538.232   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 009:538.531   Data:  00 00
T82D4 009:538.563 - 0.362ms returns 2 (0x2)
T82D4 009:538.584 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:538.605   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 009:538.905   Data:  00 00
T82D4 009:538.936 - 0.360ms returns 2 (0x2)
T82D4 009:538.957 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:538.979   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 009:539.281   Data:  00 00
T82D4 009:539.311 - 0.363ms returns 2 (0x2)
T82D4 009:539.332 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:539.355   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 009:539.655   Data:  00 00
T82D4 009:539.686 - 0.362ms returns 2 (0x2)
T82D4 009:539.707 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:539.729   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 009:540.033   Data:  00 00
T82D4 009:540.065 - 0.366ms returns 2 (0x2)
T82D4 009:540.085 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:540.106   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 009:540.408   Data:  00 00
T82D4 009:540.444 - 0.367ms returns 2 (0x2)
T82D4 009:540.467 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:540.490   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 009:540.845   Data:  00 00
T82D4 009:540.877 - 0.420ms returns 2 (0x2)
T82D4 009:540.900 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:540.920   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 009:541.285   Data:  00 00
T82D4 009:541.320 - 0.430ms returns 2 (0x2)
T82D4 009:541.344 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:541.366   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 009:541.679   Data:  00 00
T82D4 009:541.711 - 0.375ms returns 2 (0x2)
T82D4 009:541.732 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:541.754   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 009:542.109   Data:  00 00
T82D4 009:542.141 - 0.418ms returns 2 (0x2)
T82D4 009:542.162 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:542.185   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 009:542.505   Data:  00 00
T82D4 009:542.540 - 0.387ms returns 2 (0x2)
T82D4 009:542.561 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:542.583   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 009:542.880   Data:  00 00
T82D4 009:542.911 - 0.359ms returns 2 (0x2)
T82D4 009:542.933 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:542.954   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 009:543.314   Data:  00 00
T82D4 009:543.346 - 0.422ms returns 2 (0x2)
T82D4 009:543.367 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:543.388   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 009:543.694   Data:  00 00
T82D4 009:543.725 - 0.366ms returns 2 (0x2)
T82D4 009:543.746 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:543.768   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 009:544.084   Data:  00 00
T82D4 009:544.116 - 0.378ms returns 2 (0x2)
T82D4 009:544.138 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:544.160   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 009:544.526   Data:  00 00
T82D4 009:544.562 - 0.434ms returns 2 (0x2)
T82D4 009:544.585 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:544.606   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 009:544.933   Data:  00 00
T82D4 009:544.964 - 0.388ms returns 2 (0x2)
T82D4 009:544.986 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:545.007   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 009:545.309   Data:  00 00
T82D4 009:545.340 - 0.362ms returns 2 (0x2)
T82D4 009:545.360 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:545.383   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 009:545.680   Data:  00 00
T82D4 009:545.711 - 0.359ms returns 2 (0x2)
T82D4 009:545.732 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:545.754   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 009:546.059   Data:  00 00
T82D4 009:546.091 - 0.368ms returns 2 (0x2)
T82D4 009:546.112 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:546.134   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 009:546.434   Data:  00 00
T82D4 009:546.465 - 0.362ms returns 2 (0x2)
T82D4 009:546.487 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:546.508   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 009:546.806   Data:  00 00
T82D4 009:546.836 - 0.357ms returns 2 (0x2)
T82D4 009:546.857 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:546.879   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 009:547.232   Data:  00 00
T82D4 009:547.263 - 0.415ms returns 2 (0x2)
T82D4 009:547.284 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:547.306   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 009:547.625   Data:  00 00
T82D4 009:547.656 - 0.381ms returns 2 (0x2)
T82D4 009:547.677 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:547.698   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 009:548.008   Data:  00 00
T82D4 009:548.039 - 0.371ms returns 2 (0x2)
T82D4 009:548.061 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:548.081   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 009:548.381   Data:  00 00
T82D4 009:548.411 - 0.359ms returns 2 (0x2)
T82D4 009:548.434 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:548.492   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 009:548.844   Data:  00 00
T82D4 009:548.877 - 0.452ms returns 2 (0x2)
T82D4 009:548.899 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:548.922   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 009:549.236   Data:  00 00
T82D4 009:549.268 - 0.378ms returns 2 (0x2)
T82D4 009:549.289 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:549.311   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 009:549.640   Data:  00 00
T82D4 009:549.717 - 0.437ms returns 2 (0x2)
T82D4 009:549.738 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:549.781   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 009:550.110   Data:  00 00
T82D4 009:550.144 - 0.415ms returns 2 (0x2)
T82D4 009:550.167 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:550.192   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 009:550.507   Data:  00 00
T82D4 009:550.538 - 0.380ms returns 2 (0x2)
T82D4 009:550.561 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:550.583   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 009:550.880   Data:  00 00
T82D4 009:550.911 - 0.359ms returns 2 (0x2)
T82D4 009:550.932 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:550.955   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 009:551.256   Data:  00 00
T82D4 009:551.288 - 0.365ms returns 2 (0x2)
T82D4 009:551.310 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:551.332   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 009:551.630   Data:  00 00
T82D4 009:551.663 - 0.362ms returns 2 (0x2)
T82D4 009:551.687 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:551.709   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 009:552.032   Data:  00 00
T82D4 009:552.064 - 0.387ms returns 2 (0x2)
T82D4 009:552.142 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:552.167   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 009:552.467   Data:  00 00
T82D4 009:552.501 - 0.368ms returns 2 (0x2)
T82D4 009:552.523 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:552.545   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 009:552.845   Data:  00 00
T82D4 009:552.876 - 0.363ms returns 2 (0x2)
T82D4 009:552.899 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:552.920   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 009:553.235   Data:  00 00
T82D4 009:553.265 - 0.374ms returns 2 (0x2)
T82D4 009:553.285 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:553.307   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 009:553.605   Data:  00 00
T82D4 009:553.635 - 0.358ms returns 2 (0x2)
T82D4 009:553.657 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:553.679   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 009:554.007   Data:  00 00
T82D4 009:554.038 - 0.389ms returns 2 (0x2)
T82D4 009:554.058 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:554.080   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 009:554.380   Data:  00 00
T82D4 009:554.412 - 0.362ms returns 2 (0x2)
T82D4 009:554.433 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:554.455   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 009:554.756   Data:  00 00
T82D4 009:554.787 - 0.363ms returns 2 (0x2)
T82D4 009:554.808 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:554.829   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 009:555.134   Data:  00 00
T82D4 009:555.165 - 0.366ms returns 2 (0x2)
T82D4 009:555.187 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:555.208   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 009:555.505   Data:  00 00
T82D4 009:555.535 - 0.356ms returns 2 (0x2)
T82D4 009:555.556 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:555.578   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 009:555.880   Data:  00 00
T82D4 009:555.911 - 0.363ms returns 2 (0x2)
T82D4 009:555.931 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:555.954   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 009:556.257   Data:  00 00
T82D4 009:556.289 - 0.366ms returns 2 (0x2)
T82D4 009:556.310 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:556.331   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 009:556.630   Data:  00 00
T82D4 009:556.662 - 0.361ms returns 2 (0x2)
T82D4 009:556.683 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:556.704   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 009:557.010   Data:  00 00
T82D4 009:557.074 - 0.399ms returns 2 (0x2)
T82D4 009:557.097 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:557.135   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 009:557.485   Data:  00 00
T82D4 009:557.518 - 0.429ms returns 2 (0x2)
T82D4 009:557.539 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:557.563   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 009:557.880   Data:  00 00
T82D4 009:557.911 - 0.381ms returns 2 (0x2)
T82D4 009:557.932 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:557.953   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 009:558.281   Data:  00 00
T82D4 009:558.312 - 0.388ms returns 2 (0x2)
T82D4 009:558.333 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:558.354   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 009:558.655   Data:  00 00
T82D4 009:558.685 - 0.360ms returns 2 (0x2)
T82D4 009:558.706 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:558.729   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 009:559.035   Data:  00 00
T82D4 009:559.066 - 0.368ms returns 2 (0x2)
T82D4 009:559.086 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:559.108   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 009:559.406   Data:  00 00
T82D4 009:559.438 - 0.361ms returns 2 (0x2)
T82D4 009:559.459 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:559.479   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 009:559.781   Data:  00 00
T82D4 009:559.811 - 0.360ms returns 2 (0x2)
T82D4 009:559.832 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:559.854   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 009:560.183   Data:  00 00
T82D4 009:560.214 - 0.390ms returns 2 (0x2)
T82D4 009:560.234 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:560.257   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 009:560.555   Data:  00 00
T82D4 009:560.586 - 0.360ms returns 2 (0x2)
T82D4 009:560.607 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:560.629   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 009:560.933   Data:  00 00
T82D4 009:560.966 - 0.367ms returns 2 (0x2)
T82D4 009:560.987 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:561.007   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 009:561.362   Data:  00 00
T82D4 009:561.397 - 0.420ms returns 2 (0x2)
T82D4 009:561.420 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:561.442   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 009:561.754   Data:  00 00
T82D4 009:561.785 - 0.374ms returns 2 (0x2)
T82D4 009:561.807 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:561.829   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 009:562.134   Data:  00 00
T82D4 009:562.165 - 0.366ms returns 2 (0x2)
T82D4 009:562.186 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:562.209   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 009:562.505   Data:  00 00
T82D4 009:562.535 - 0.358ms returns 2 (0x2)
T82D4 009:562.556 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:562.579   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 009:562.880   Data:  00 00
T82D4 009:562.912 - 0.364ms returns 2 (0x2)
T82D4 009:562.933 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:562.953   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 009:563.373   Data:  00 00
T82D4 009:563.405 - 0.480ms returns 2 (0x2)
T82D4 009:563.426 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:563.448   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 009:563.755   Data:  00 00
T82D4 009:563.785 - 0.367ms returns 2 (0x2)
T82D4 009:563.806 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:563.828   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 009:564.139   Data:  00 00
T82D4 009:564.204 - 0.414ms returns 2 (0x2)
T82D4 009:564.240 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:564.272   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 009:564.622   Data:  00 00
T82D4 009:564.659 - 0.427ms returns 2 (0x2)
T82D4 009:564.682 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:564.704   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 009:565.008   Data:  00 00
T82D4 009:565.041 - 0.368ms returns 2 (0x2)
T82D4 009:565.063 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:565.084   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 009:565.404   Data:  00 00
T82D4 009:565.441 - 0.387ms returns 2 (0x2)
T82D4 009:565.462 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:565.485   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 009:565.804   Data:  00 00
T82D4 009:565.835 - 0.381ms returns 2 (0x2)
T82D4 009:565.855 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:565.878   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 009:566.233   Data:  00 00
T82D4 009:566.267 - 0.419ms returns 2 (0x2)
T82D4 009:566.288 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:566.310   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 009:566.631   Data:  00 00
T82D4 009:566.661 - 0.383ms returns 2 (0x2)
T82D4 009:566.684 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:566.704   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 009:567.009   Data:  00 00
T82D4 009:567.039 - 0.364ms returns 2 (0x2)
T82D4 009:567.060 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:567.082   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 009:567.380   Data:  00 00
T82D4 009:567.412 - 0.360ms returns 2 (0x2)
T82D4 009:567.433 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:567.455   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 009:567.755   Data:  00 00
T82D4 009:567.786 - 0.362ms returns 2 (0x2)
T82D4 009:567.807 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:567.828   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 009:568.134   Data:  00 00
T82D4 009:568.165 - 0.367ms returns 2 (0x2)
T82D4 009:568.186 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:568.207   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 009:568.504   Data:  00 00
T82D4 009:568.535 - 0.356ms returns 2 (0x2)
T82D4 009:568.555 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:568.577   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 009:568.880   Data:  00 00
T82D4 009:568.911 - 0.364ms returns 2 (0x2)
T82D4 009:568.931 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:568.953   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 009:569.254   Data:  00 00
T82D4 009:569.286 - 0.363ms returns 2 (0x2)
T82D4 009:569.306 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:569.328   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 009:569.632   Data:  00 00
T82D4 009:569.663 - 0.365ms returns 2 (0x2)
T82D4 009:569.685 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:569.706   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 009:570.009   Data:  00 00
T82D4 009:570.040 - 0.364ms returns 2 (0x2)
T82D4 009:570.061 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:570.082   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 009:570.405   Data:  00 00
T82D4 009:570.435 - 0.382ms returns 2 (0x2)
T82D4 009:570.456 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:570.478   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 009:570.780   Data:  00 00
T82D4 009:570.811 - 0.363ms returns 2 (0x2)
T82D4 009:570.831 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:570.854   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 009:571.182   Data:  00 00
T82D4 009:571.214 - 0.391ms returns 2 (0x2)
T82D4 009:571.235 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:571.255   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 009:571.555   Data:  00 00
T82D4 009:571.586 - 0.359ms returns 2 (0x2)
T82D4 009:571.607 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:571.628   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 009:571.933   Data:  00 00
T82D4 009:571.963 - 0.364ms returns 2 (0x2)
T82D4 009:571.983 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:572.005   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 009:572.305   Data:  00 00
T82D4 009:572.336 - 0.361ms returns 2 (0x2)
T82D4 009:572.356 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:572.379   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 009:572.681   Data:  00 00
T82D4 009:572.712 - 0.364ms returns 2 (0x2)
T82D4 009:572.736 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:572.758   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 009:573.083   Data:  00 00
T82D4 009:573.114 - 0.387ms returns 2 (0x2)
T82D4 009:573.137 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:573.157   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 009:573.473   Data:  00 00
T82D4 009:573.504 - 0.405ms returns 2 (0x2)
T82D4 009:573.555 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:573.593   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 009:574.010   Data:  00 00
T82D4 009:574.045 - 0.498ms returns 2 (0x2)
T82D4 009:574.067 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:574.091   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 009:574.404   Data:  00 00
T82D4 009:574.436 - 0.377ms returns 2 (0x2)
T82D4 009:574.457 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:574.479   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 009:574.781   Data:  00 00
T82D4 009:574.812 - 0.364ms returns 2 (0x2)
T82D4 009:574.833 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:574.855   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 009:575.157   Data:  00 00
T82D4 009:575.189 - 0.365ms returns 2 (0x2)
T82D4 009:575.210 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:575.231   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 009:575.530   Data:  00 00
T82D4 009:575.560 - 0.360ms returns 2 (0x2)
T82D4 009:575.583 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:575.603   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 009:575.907   Data:  00 00
T82D4 009:575.937 - 0.362ms returns 2 (0x2)
T82D4 009:575.957 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:575.979   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 009:576.279   Data:  00 00
T82D4 009:576.310 - 0.361ms returns 2 (0x2)
T82D4 009:576.330 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:576.353   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 009:576.655   Data:  00 00
T82D4 009:576.686 - 0.364ms returns 2 (0x2)
T82D4 009:576.707 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:576.728   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 009:577.033   Data:  00 00
T82D4 009:577.064 - 0.366ms returns 2 (0x2)
T82D4 009:577.087 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:577.109   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 009:577.404   Data:  00 00
T82D4 009:577.435 - 0.357ms returns 2 (0x2)
T82D4 009:577.458 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:577.478   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 009:577.782   Data:  00 00
T82D4 009:577.819 - 0.370ms returns 2 (0x2)
T82D4 009:577.865 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:577.925   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 009:578.257   Data:  00 00
T82D4 009:578.293 - 0.437ms returns 2 (0x2)
T82D4 009:578.316 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:578.339   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 009:578.655   Data:  00 00
T82D4 009:578.687 - 0.380ms returns 2 (0x2)
T82D4 009:578.710 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:578.730   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 009:579.032   Data:  00 00
T82D4 009:579.063 - 0.362ms returns 2 (0x2)
T82D4 009:579.085 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:579.107   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 009:579.829   Data:  00 00
T82D4 009:579.899 - 0.829ms returns 2 (0x2)
T82D4 009:580.027 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:580.087   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 009:580.406   Data:  00 00
T82D4 009:580.438 - 0.421ms returns 2 (0x2)
T82D4 009:580.462 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:580.484   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 009:580.804   Data:  00 00
T82D4 009:580.835 - 0.381ms returns 2 (0x2)
T82D4 009:580.856 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:580.883   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 009:581.235   Data:  00 00
T82D4 009:581.267 - 0.419ms returns 2 (0x2)
T82D4 009:581.287 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:581.310   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 009:581.629   Data:  00 00
T82D4 009:581.660 - 0.381ms returns 2 (0x2)
T82D4 009:581.681 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:581.703   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 009:582.008   Data:  00 00
T82D4 009:582.041 - 0.369ms returns 2 (0x2)
T82D4 009:582.062 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:582.083   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 009:582.405   Data:  00 00
T82D4 009:582.437 - 0.384ms returns 2 (0x2)
T82D4 009:582.459 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:582.480   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 009:582.780   Data:  00 00
T82D4 009:582.810 - 0.359ms returns 2 (0x2)
T82D4 009:582.831 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:582.853   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 009:583.158   Data:  00 00
T82D4 009:583.189 - 0.366ms returns 2 (0x2)
T82D4 009:583.210 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:583.232   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 009:583.554   Data:  00 00
T82D4 009:583.586 - 0.385ms returns 2 (0x2)
T82D4 009:583.607 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:583.628   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 009:584.063   Data:  00 00
T82D4 009:584.094 - 0.496ms returns 2 (0x2)
T82D4 009:584.116 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:584.137   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 009:584.438   Data:  00 00
T82D4 009:584.469 - 0.361ms returns 2 (0x2)
T82D4 009:584.491 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:584.513   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 009:584.811   Data:  00 00
T82D4 009:584.841 - 0.359ms returns 2 (0x2)
T82D4 009:584.862 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:584.884   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 009:585.238   Data:  00 00
T82D4 009:585.268 - 0.415ms returns 2 (0x2)
T82D4 009:585.289 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:585.312   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 009:585.630   Data:  00 00
T82D4 009:585.662 - 0.381ms returns 2 (0x2)
T82D4 009:585.682 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:585.705   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 009:586.008   Data:  00 00
T82D4 009:586.039 - 0.365ms returns 2 (0x2)
T82D4 009:586.060 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:586.082   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 009:586.381   Data:  00 00
T82D4 009:586.414 - 0.362ms returns 2 (0x2)
T82D4 009:586.435 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:586.455   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 009:586.755   Data:  00 00
T82D4 009:586.786 - 0.359ms returns 2 (0x2)
T82D4 009:586.807 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:586.829   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 009:587.133   Data:  00 00
T82D4 009:587.164 - 0.365ms returns 2 (0x2)
T82D4 009:587.185 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:587.207   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 009:587.505   Data:  00 00
T82D4 009:587.537 - 0.361ms returns 2 (0x2)
T82D4 009:587.558 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:587.581   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 009:587.879   Data:  00 00
T82D4 009:587.911 - 0.361ms returns 2 (0x2)
T82D4 009:587.932 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:587.953   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 009:588.256   Data:  00 00
T82D4 009:588.286 - 0.363ms returns 2 (0x2)
T82D4 009:588.308 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:588.330   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 009:588.747   Data:  00 00
T82D4 009:588.780 - 0.480ms returns 2 (0x2)
T82D4 009:588.802 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:588.824   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 009:589.134   Data:  00 00
T82D4 009:589.165 - 0.371ms returns 2 (0x2)
T82D4 009:589.186 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:589.208   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 009:589.506   Data:  00 00
T82D4 009:589.543 - 0.366ms returns 2 (0x2)
T82D4 009:589.565 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:589.589   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 009:589.907   Data:  00 00
T82D4 009:589.941 - 0.385ms returns 2 (0x2)
T82D4 009:589.964 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:589.985   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 009:590.309   Data:  00 00
T82D4 009:590.375 - 0.421ms returns 2 (0x2)
T82D4 009:590.400 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:590.423   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 009:590.757   Data:  00 00
T82D4 009:590.793 - 0.402ms returns 2 (0x2)
T82D4 009:590.816 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:590.840   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 009:591.160   Data:  00 00
T82D4 009:591.193 - 0.385ms returns 2 (0x2)
T82D4 009:591.214 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:591.236   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 009:591.555   Data:  00 00
T82D4 009:591.586 - 0.381ms returns 2 (0x2)
T82D4 009:591.608 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:591.629   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 009:592.061   Data:  00 00
T82D4 009:592.091 - 0.491ms returns 2 (0x2)
T82D4 009:592.135 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:592.159   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 009:592.472   Data:  00 00
T82D4 009:592.503 - 0.376ms returns 2 (0x2)
T82D4 009:592.523 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:592.545   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 009:592.841   Data:  00 00
T82D4 009:592.871 - 0.357ms returns 2 (0x2)
T82D4 009:592.892 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:592.914   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 009:593.255   Data:  00 00
T82D4 009:593.285 - 0.402ms returns 2 (0x2)
T82D4 009:593.305 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:593.326   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 009:593.629   Data:  00 00
T82D4 009:593.659 - 0.363ms returns 2 (0x2)
T82D4 009:593.681 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:593.701   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 009:594.007   Data:  00 00
T82D4 009:594.037 - 0.365ms returns 2 (0x2)
T82D4 009:594.059 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:594.079   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 009:594.380   Data:  00 00
T82D4 009:594.409 - 0.358ms returns 2 (0x2)
T82D4 009:594.429 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:594.451   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 009:594.755   Data:  00 00
T82D4 009:594.814 - 0.397ms returns 2 (0x2)
T82D4 009:594.843 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:594.879   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 009:595.247   Data:  00 00
T82D4 009:595.300 - 0.485ms returns 2 (0x2)
T82D4 009:595.366 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:595.398   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 009:595.755   Data:  00 00
T82D4 009:595.792 - 0.434ms returns 2 (0x2)
T82D4 009:595.814 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:595.835   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 009:596.182   Data:  00 00
T82D4 009:596.212 - 0.407ms returns 2 (0x2)
T82D4 009:596.234 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:596.255   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 009:596.619   Data:  00 00
T82D4 009:596.648 - 0.423ms returns 2 (0x2)
T82D4 009:596.672 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:596.696   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 009:597.058   Data:  00 00
T82D4 009:597.089 - 0.426ms returns 2 (0x2)
T82D4 009:597.110 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:597.166   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 009:597.505   Data:  00 00
T82D4 009:597.536 - 0.435ms returns 2 (0x2)
T82D4 009:597.556 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:597.577   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 009:597.879   Data:  00 00
T82D4 009:597.910 - 0.363ms returns 2 (0x2)
T82D4 009:597.931 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:597.951   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 009:598.255   Data:  00 00
T82D4 009:598.285 - 0.362ms returns 2 (0x2)
T82D4 009:598.305 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:598.326   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 009:598.631   Data:  00 00
T82D4 009:598.661 - 0.365ms returns 2 (0x2)
T82D4 009:598.681 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:598.702   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 009:599.008   Data:  00 00
T82D4 009:599.039 - 0.366ms returns 2 (0x2)
T82D4 009:599.059 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:599.080   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 009:599.381   Data:  00 00
T82D4 009:599.411 - 0.361ms returns 2 (0x2)
T82D4 009:599.433 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:599.453   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 009:599.755   Data:  00 00
T82D4 009:599.784 - 0.360ms returns 2 (0x2)
T82D4 009:599.804 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:599.826   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 009:600.133   Data:  00 00
T82D4 009:600.164 - 0.382ms returns 2 (0x2)
T82D4 009:600.198 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:600.219   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 009:600.530   Data:  00 00
T82D4 009:600.560 - 0.370ms returns 2 (0x2)
T82D4 009:600.580 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:600.600   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 009:600.904   Data:  00 00
T82D4 009:600.934 - 0.363ms returns 2 (0x2)
T82D4 009:600.955 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:600.974   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 009:601.280   Data:  00 00
T82D4 009:601.309 - 0.362ms returns 2 (0x2)
T82D4 009:601.330 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:601.351   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 009:601.654   Data:  00 00
T82D4 009:601.684 - 0.362ms returns 2 (0x2)
T82D4 009:601.703 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:601.724   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 009:602.033   Data:  00 00
T82D4 009:602.063 - 0.368ms returns 2 (0x2)
T82D4 009:602.083 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:602.104   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 009:602.405   Data:  00 00
T82D4 009:602.435 - 0.360ms returns 2 (0x2)
T82D4 009:602.456 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:602.475   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 009:602.779   Data:  00 00
T82D4 009:602.808 - 0.360ms returns 2 (0x2)
T82D4 009:602.828 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:602.849   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 009:603.174   Data:  00 00
T82D4 009:603.203 - 0.384ms returns 2 (0x2)
T82D4 009:603.223 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:603.244   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 009:603.554   Data:  00 00
T82D4 009:603.584 - 0.369ms returns 2 (0x2)
T82D4 009:603.604 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:603.624   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 009:603.921   Data:  00 00
T82D4 009:603.950 - 0.355ms returns 2 (0x2)
T82D4 009:603.971 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:603.992   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 009:604.305   Data:  00 00
T82D4 009:604.334 - 0.371ms returns 2 (0x2)
T82D4 009:604.354 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:604.376   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 009:604.672   Data:  00 00
T82D4 009:604.701 - 0.355ms returns 2 (0x2)
T82D4 009:622.957 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:623.020   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 009:623.439   Data:  00 00
T82D4 009:623.482 - 0.535ms returns 2 (0x2)
T82D4 009:623.513 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:623.570   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 009:623.970   Data:  00 00
T82D4 009:624.009 - 0.504ms returns 2 (0x2)
T82D4 009:624.058 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:624.110   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 009:624.585   Data:  00 00
T82D4 009:624.622 - 0.573ms returns 2 (0x2)
T82D4 009:624.646 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:624.671   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 009:625.038   Data:  00 00
T82D4 009:625.075 - 0.438ms returns 2 (0x2)
T82D4 009:625.098 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:625.121   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 009:625.513   Data:  00 00
T82D4 009:625.570 - 0.484ms returns 2 (0x2)
T82D4 009:625.607 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:625.641   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 009:626.011   Data:  00 00
T82D4 009:626.046 - 0.447ms returns 2 (0x2)
T82D4 009:626.067 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:626.090   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 009:626.530   Data:  00 00
T82D4 009:626.567 - 0.508ms returns 2 (0x2)
T82D4 009:626.590 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:626.612   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 009:626.913   Data:  00 00
T82D4 009:626.946 - 0.364ms returns 2 (0x2)
T82D4 009:626.967 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:626.988   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 009:627.357   Data:  00 00
T82D4 009:627.387 - 0.428ms returns 2 (0x2)
T82D4 009:627.407 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:627.430   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 009:627.918   Data:  00 00
T82D4 009:627.959 - 0.561ms returns 2 (0x2)
T82D4 009:628.018 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:628.044   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 009:628.365   Data:  00 00
T82D4 009:628.400 - 0.389ms returns 2 (0x2)
T82D4 009:628.422 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:628.445   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 009:628.755   Data:  00 00
T82D4 009:628.785 - 0.372ms returns 2 (0x2)
T82D4 009:628.805 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:628.825   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 009:629.133   Data:  00 00
T82D4 009:629.162 - 0.365ms returns 2 (0x2)
T82D4 009:629.183 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:629.204   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 009:629.505   Data:  00 00
T82D4 009:629.534 - 0.359ms returns 2 (0x2)
T82D4 009:629.554 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:629.575   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 009:629.879   Data:  00 00
T82D4 009:629.908 - 0.362ms returns 2 (0x2)
T82D4 009:629.928 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:629.949   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 009:630.255   Data:  00 00
T82D4 009:630.285 - 0.366ms returns 2 (0x2)
T82D4 009:630.305 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:630.325   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 009:630.630   Data:  00 00
T82D4 009:630.667 - 0.369ms returns 2 (0x2)
T82D4 009:630.688 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:630.708   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 009:631.033   Data:  00 00
T82D4 009:631.062 - 0.383ms returns 2 (0x2)
T82D4 009:631.082 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:631.104   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 009:631.405   Data:  00 00
T82D4 009:631.436 - 0.362ms returns 2 (0x2)
T82D4 009:631.511 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:631.536   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 009:631.833   Data:  00 00
T82D4 009:631.862 - 0.359ms returns 2 (0x2)
T82D4 009:631.882 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:631.903   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 009:632.235   Data:  00 00
T82D4 009:632.264 - 0.390ms returns 2 (0x2)
T82D4 009:632.284 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:632.305   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 009:632.605   Data:  00 00
T82D4 009:632.636 - 0.361ms returns 2 (0x2)
T82D4 009:632.657 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:632.676   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 009:633.008   Data:  00 00
T82D4 009:633.037 - 0.388ms returns 2 (0x2)
T82D4 009:633.057 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:633.078   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 009:633.380   Data:  00 00
T82D4 009:633.408 - 0.359ms returns 2 (0x2)
T82D4 009:633.428 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:633.449   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 009:633.754   Data:  00 00
T82D4 009:633.784 - 0.364ms returns 2 (0x2)
T82D4 009:633.804 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:633.826   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 009:634.134   Data:  00 00
T82D4 009:634.164 - 0.368ms returns 2 (0x2)
T82D4 009:634.184 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:634.203   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 009:634.505   Data:  00 00
T82D4 009:634.534 - 0.358ms returns 2 (0x2)
T82D4 009:634.554 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:634.574   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 009:634.880   Data:  00 00
T82D4 009:634.909 - 0.363ms returns 2 (0x2)
T82D4 009:634.929 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:634.950   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 009:635.256   Data:  00 00
T82D4 009:635.286 - 0.365ms returns 2 (0x2)
T82D4 009:635.305 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:635.326   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 009:635.629   Data:  00 00
T82D4 009:635.660 - 0.363ms returns 2 (0x2)
T82D4 009:635.680 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:635.699   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 009:636.008   Data:  00 00
T82D4 009:636.037 - 0.365ms returns 2 (0x2)
T82D4 009:636.057 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:636.078   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 009:636.379   Data:  00 00
T82D4 009:636.407 - 0.358ms returns 2 (0x2)
T82D4 009:636.428 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:636.470   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 009:636.779   Data:  00 00
T82D4 009:636.809 - 0.389ms returns 2 (0x2)
T82D4 009:636.829 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:636.851   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 009:637.158   Data:  00 00
T82D4 009:637.188 - 0.368ms returns 2 (0x2)
T82D4 009:637.208 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:637.228   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 009:637.529   Data:  00 00
T82D4 009:637.559 - 0.359ms returns 2 (0x2)
T82D4 009:637.578 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:637.600   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 009:637.905   Data:  00 00
T82D4 009:637.934 - 0.365ms returns 2 (0x2)
T82D4 009:637.955 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:637.974   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 009:638.279   Data:  00 00
T82D4 009:638.308 - 0.361ms returns 2 (0x2)
T82D4 009:638.330 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:638.352   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 009:638.656   Data:  00 00
T82D4 009:638.685 - 0.363ms returns 2 (0x2)
T82D4 009:638.704 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:638.726   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 009:639.034   Data:  00 00
T82D4 009:639.063 - 0.367ms returns 2 (0x2)
T82D4 009:639.083 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:639.104   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 009:639.405   Data:  00 00
T82D4 009:639.435 - 0.360ms returns 2 (0x2)
T82D4 009:639.455 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:639.474   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 009:639.780   Data:  00 00
T82D4 009:639.809 - 0.362ms returns 2 (0x2)
T82D4 009:639.829 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:639.850   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 009:640.221   Data:  00 00
T82D4 009:640.291 - 0.491ms returns 2 (0x2)
T82D4 009:640.335 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:640.358   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 009:640.764   Data:  00 00
T82D4 009:640.836 - 0.510ms returns 2 (0x2)
T82D4 009:640.860 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:640.913   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 009:641.306   Data:  00 00
T82D4 009:641.363 - 0.511ms returns 2 (0x2)
T82D4 009:641.385 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:641.410   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 009:641.808   Data:  00 00
T82D4 009:641.891 - 0.516ms returns 2 (0x2)
T82D4 009:641.918 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:641.992   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 009:642.410   Data:  00 00
T82D4 009:642.448 - 0.539ms returns 2 (0x2)
T82D4 009:642.471 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:642.494   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 009:642.822   Data:  00 00
T82D4 009:642.855 - 0.392ms returns 2 (0x2)
T82D4 009:642.877 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:642.898   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 009:643.234   Data:  00 00
T82D4 009:643.263 - 0.394ms returns 2 (0x2)
T82D4 009:643.284 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:643.305   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 009:643.610   Data:  00 00
T82D4 009:643.640 - 0.365ms returns 2 (0x2)
T82D4 009:643.660 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:643.682   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 009:644.007   Data:  00 00
T82D4 009:644.037 - 0.384ms returns 2 (0x2)
T82D4 009:644.058 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:644.078   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 009:644.380   Data:  00 00
T82D4 009:644.409 - 0.361ms returns 2 (0x2)
T82D4 009:644.431 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:644.450   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 009:644.814   Data:  00 00
T82D4 009:644.889 - 0.467ms returns 2 (0x2)
T82D4 009:644.933 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:644.958   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 009:645.304   Data:  00 00
T82D4 009:645.336 - 0.411ms returns 2 (0x2)
T82D4 009:645.357 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:645.379   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 009:645.735   Data:  00 00
T82D4 009:645.765 - 0.416ms returns 2 (0x2)
T82D4 009:645.786 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:645.806   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 009:646.109   Data:  00 00
T82D4 009:646.138 - 0.362ms returns 2 (0x2)
T82D4 009:646.160 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:646.179   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 009:646.482   Data:  00 00
T82D4 009:646.511 - 0.360ms returns 2 (0x2)
T82D4 009:646.544 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:646.569   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 009:646.880   Data:  00 00
T82D4 009:646.910 - 0.374ms returns 2 (0x2)
T82D4 009:646.929 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:646.951   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 009:647.248   Data:  00 00
T82D4 009:647.277 - 0.355ms returns 2 (0x2)
T82D4 009:647.297 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:647.318   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 009:647.631   Data:  00 00
T82D4 009:647.661 - 0.373ms returns 2 (0x2)
T82D4 009:647.683 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:647.703   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 009:648.007   Data:  00 00
T82D4 009:648.037 - 0.362ms returns 2 (0x2)
T82D4 009:648.057 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:648.077   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 009:648.381   Data:  00 00
T82D4 009:648.411 - 0.362ms returns 2 (0x2)
T82D4 009:648.431 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:648.451   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 009:648.756   Data:  00 00
T82D4 009:648.786 - 0.363ms returns 2 (0x2)
T82D4 009:648.805 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:648.827   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 009:649.133   Data:  00 00
T82D4 009:649.163 - 0.366ms returns 2 (0x2)
T82D4 009:649.183 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:649.203   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 009:649.505   Data:  00 00
T82D4 009:649.535 - 0.359ms returns 2 (0x2)
T82D4 009:649.555 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:649.576   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 009:649.879   Data:  00 00
T82D4 009:649.908 - 0.360ms returns 2 (0x2)
T82D4 009:649.928 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:649.949   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 009:650.255   Data:  00 00
T82D4 009:650.284 - 0.364ms returns 2 (0x2)
T82D4 009:650.304 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:650.325   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 009:650.630   Data:  00 00
T82D4 009:650.661 - 0.365ms returns 2 (0x2)
T82D4 009:650.681 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:650.700   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 009:651.007   Data:  00 00
T82D4 009:651.044 - 0.371ms returns 2 (0x2)
T82D4 009:651.065 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:651.085   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 009:651.380   Data:  00 00
T82D4 009:651.409 - 0.352ms returns 2 (0x2)
T82D4 009:651.428 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:651.449   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 009:651.754   Data:  00 00
T82D4 009:651.783 - 0.362ms returns 2 (0x2)
T82D4 009:651.803 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:651.825   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 009:652.235   Data:  00 00
T82D4 009:652.265 - 0.470ms returns 2 (0x2)
T82D4 009:652.284 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:652.305   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 009:652.605   Data:  00 00
T82D4 009:652.634 - 0.358ms returns 2 (0x2)
T82D4 009:652.655 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:652.674   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 009:653.008   Data:  00 00
T82D4 009:653.037 - 0.389ms returns 2 (0x2)
T82D4 009:653.056 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:653.077   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 009:653.380   Data:  00 00
T82D4 009:653.409 - 0.361ms returns 2 (0x2)
T82D4 009:653.429 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:653.450   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 009:653.754   Data:  00 00
T82D4 009:653.784 - 0.362ms returns 2 (0x2)
T82D4 009:653.804 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:653.824   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 009:654.134   Data:  00 00
T82D4 009:654.163 - 0.368ms returns 2 (0x2)
T82D4 009:654.184 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:654.204   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 009:654.505   Data:  00 00
T82D4 009:654.534 - 0.357ms returns 2 (0x2)
T82D4 009:654.553 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:654.574   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 009:654.880   Data:  00 00
T82D4 009:654.909 - 0.364ms returns 2 (0x2)
T82D4 009:654.929 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:654.950   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 009:655.246   Data:  00 00
T82D4 009:655.275 - 0.355ms returns 2 (0x2)
T82D4 009:655.295 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:655.316   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 009:655.629   Data:  00 00
T82D4 009:655.659 - 0.372ms returns 2 (0x2)
T82D4 009:655.679 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:655.699   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 009:656.008   Data:  00 00
T82D4 009:656.037 - 0.365ms returns 2 (0x2)
T82D4 009:656.057 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:656.077   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 009:656.476   Data:  00 00
T82D4 009:656.559 - 0.523ms returns 2 (0x2)
T82D4 009:656.612 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:656.683   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 009:657.072   Data:  00 00
T82D4 009:657.110 - 0.506ms returns 2 (0x2)
T82D4 009:657.167 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:657.228   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 009:657.539   Data:  00 00
T82D4 009:657.576 - 0.418ms returns 2 (0x2)
T82D4 009:657.740 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:657.776   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 009:658.109   Data:  00 00
T82D4 009:658.142 - 0.410ms returns 2 (0x2)
T82D4 009:658.163 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:658.186   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 009:658.506   Data:  00 00
T82D4 009:658.538 - 0.383ms returns 2 (0x2)
T82D4 009:658.559 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:658.581   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 009:658.880   Data:  00 00
T82D4 009:658.910 - 0.359ms returns 2 (0x2)
T82D4 009:658.930 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:658.951   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 009:659.255   Data:  00 00
T82D4 009:659.285 - 0.363ms returns 2 (0x2)
T82D4 009:659.305 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:659.325   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 009:659.631   Data:  00 00
T82D4 009:659.662 - 0.365ms returns 2 (0x2)
T82D4 009:659.682 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:659.701   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 009:660.006   Data:  00 00
T82D4 009:660.035 - 0.362ms returns 2 (0x2)
T82D4 009:660.056 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:660.077   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 009:660.380   Data:  00 00
T82D4 009:660.409 - 0.361ms returns 2 (0x2)
T82D4 009:660.428 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:660.449   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 009:660.755   Data:  00 00
T82D4 009:660.785 - 0.365ms returns 2 (0x2)
T82D4 009:660.805 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:660.826   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 009:661.133   Data:  00 00
T82D4 009:661.162 - 0.365ms returns 2 (0x2)
T82D4 009:661.182 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:661.203   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 009:661.505   Data:  00 00
T82D4 009:661.534 - 0.362ms returns 2 (0x2)
T82D4 009:661.557 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:661.577   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 009:661.879   Data:  00 00
T82D4 009:661.909 - 0.363ms returns 2 (0x2)
T82D4 009:661.933 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:661.953   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 009:662.255   Data:  00 00
T82D4 009:662.285 - 0.360ms returns 2 (0x2)
T82D4 009:662.305 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:662.325   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 009:662.690   Data:  00 00
T82D4 009:662.744 - 0.447ms returns 2 (0x2)
T82D4 009:662.787 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:662.812   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 009:663.237   Data:  00 00
T82D4 009:663.271 - 0.493ms returns 2 (0x2)
T82D4 009:663.292 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:663.315   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 009:663.631   Data:  00 00
T82D4 009:663.662 - 0.377ms returns 2 (0x2)
T82D4 009:663.682 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:663.703   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 009:664.008   Data:  00 00
T82D4 009:664.039 - 0.366ms returns 2 (0x2)
T82D4 009:664.060 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:664.079   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 009:664.381   Data:  00 00
T82D4 009:664.410 - 0.359ms returns 2 (0x2)
T82D4 009:664.431 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:664.451   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 009:664.755   Data:  00 00
T82D4 009:664.785 - 0.362ms returns 2 (0x2)
T82D4 009:664.805 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:664.826   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 009:665.133   Data:  00 00
T82D4 009:665.163 - 0.366ms returns 2 (0x2)
T82D4 009:665.183 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:665.204   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 009:665.505   Data:  00 00
T82D4 009:665.535 - 0.360ms returns 2 (0x2)
T82D4 009:665.555 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:665.575   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 009:665.879   Data:  00 00
T82D4 009:665.908 - 0.363ms returns 2 (0x2)
T82D4 009:665.930 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:665.949   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 009:666.256   Data:  00 00
T82D4 009:666.286 - 0.364ms returns 2 (0x2)
T82D4 009:666.305 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:666.326   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 009:666.629   Data:  00 00
T82D4 009:666.659 - 0.362ms returns 2 (0x2)
T82D4 009:666.680 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:666.700   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 009:667.008   Data:  00 00
T82D4 009:667.037 - 0.366ms returns 2 (0x2)
T82D4 009:667.058 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:667.078   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 009:667.381   Data:  00 00
T82D4 009:667.411 - 0.361ms returns 2 (0x2)
T82D4 009:667.431 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:667.451   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 009:667.754   Data:  00 00
T82D4 009:667.784 - 0.361ms returns 2 (0x2)
T82D4 009:667.803 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:667.823   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 009:668.133   Data:  00 00
T82D4 009:668.163 - 0.367ms returns 2 (0x2)
T82D4 009:668.184 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:668.205   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 009:668.504   Data:  00 00
T82D4 009:668.534 - 0.359ms returns 2 (0x2)
T82D4 009:668.555 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:668.576   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 009:668.879   Data:  00 00
T82D4 009:668.908 - 0.361ms returns 2 (0x2)
T82D4 009:668.929 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:668.950   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 009:669.256   Data:  00 00
T82D4 009:669.288 - 0.367ms returns 2 (0x2)
T82D4 009:669.309 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:669.333   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 009:669.655   Data:  00 00
T82D4 009:669.686 - 0.385ms returns 2 (0x2)
T82D4 009:669.707 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:669.727   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 009:670.034   Data:  00 00
T82D4 009:670.064 - 0.365ms returns 2 (0x2)
T82D4 009:670.083 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:670.104   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 009:670.405   Data:  00 00
T82D4 009:670.435 - 0.360ms returns 2 (0x2)
T82D4 009:670.455 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:670.476   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 009:670.779   Data:  00 00
T82D4 009:670.810 - 0.363ms returns 2 (0x2)
T82D4 009:670.830 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:670.851   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 009:671.158   Data:  00 00
T82D4 009:671.194 - 0.372ms returns 2 (0x2)
T82D4 009:671.214 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:671.236   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 009:671.554   Data:  00 00
T82D4 009:671.585 - 0.379ms returns 2 (0x2)
T82D4 009:671.605 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:671.626   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 009:672.047   Data:  00 00
T82D4 009:672.108 - 0.512ms returns 2 (0x2)
T82D4 009:672.128 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:672.150   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 009:672.465   Data:  00 00
T82D4 009:672.550 - 0.441ms returns 2 (0x2)
T82D4 009:672.592 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:672.634   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 009:673.009   Data:  00 00
T82D4 009:673.041 - 0.458ms returns 2 (0x2)
T82D4 009:673.064 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:673.085   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 009:673.519   Data:  00 00
T82D4 009:673.591 - 0.536ms returns 2 (0x2)
T82D4 009:673.629 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:673.652   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 009:674.014   Data:  00 00
T82D4 009:674.065 - 0.444ms returns 2 (0x2)
T82D4 009:674.086 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:674.124   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 009:674.465   Data:  00 00
T82D4 009:674.533 - 0.455ms returns 2 (0x2)
T82D4 009:674.568 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:674.590   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 009:674.893   Data:  00 00
T82D4 009:674.923 - 0.363ms returns 2 (0x2)
T82D4 009:674.967 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:675.013   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 009:675.380   Data:  00 00
T82D4 009:675.411 - 0.452ms returns 2 (0x2)
T82D4 009:675.431 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:675.452   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 009:675.755   Data:  00 00
T82D4 009:675.785 - 0.363ms returns 2 (0x2)
T82D4 009:675.807 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:675.826   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 009:676.158   Data:  00 00
T82D4 009:676.187 - 0.388ms returns 2 (0x2)
T82D4 009:676.207 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:676.230   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 009:676.531   Data:  00 00
T82D4 009:676.561 - 0.361ms returns 2 (0x2)
T82D4 009:676.580 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:676.601   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 009:676.906   Data:  00 00
T82D4 009:676.936 - 0.363ms returns 2 (0x2)
T82D4 009:676.955 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:676.977   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 009:677.280   Data:  00 00
T82D4 009:677.310 - 0.362ms returns 2 (0x2)
T82D4 009:677.330 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:677.351   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 009:677.863   Data:  00 00
T82D4 009:677.899 - 0.578ms returns 2 (0x2)
T82D4 009:677.922 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:677.945   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 009:678.271   Data:  00 00
T82D4 009:678.316 - 0.422ms returns 2 (0x2)
T82D4 009:678.414 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:678.475   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 009:678.782   Data:  00 00
T82D4 009:678.816 - 0.411ms returns 2 (0x2)
T82D4 009:678.838 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:678.859   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 009:679.254   Data:  00 00
T82D4 009:679.299 - 0.470ms returns 2 (0x2)
T82D4 009:679.320 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:679.340   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 009:679.654   Data:  00 00
T82D4 009:679.683 - 0.371ms returns 2 (0x2)
T82D4 009:679.703 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:679.724   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 009:680.033   Data:  00 00
T82D4 009:680.084 - 0.389ms returns 2 (0x2)
T82D4 009:680.139 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:680.161   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 009:680.605   Data:  00 00
T82D4 009:680.635 - 0.504ms returns 2 (0x2)
T82D4 009:680.656 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:680.677   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 009:681.009   Data:  00 00
T82D4 009:681.038 - 0.390ms returns 2 (0x2)
T82D4 009:681.059 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:681.079   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 009:681.380   Data:  00 00
T82D4 009:681.409 - 0.358ms returns 2 (0x2)
T82D4 009:681.429 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:681.451   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 009:681.755   Data:  00 00
T82D4 009:681.785 - 0.363ms returns 2 (0x2)
T82D4 009:681.804 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:681.826   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 009:682.198   Data:  00 00
T82D4 009:682.231 - 0.435ms returns 2 (0x2)
T82D4 009:682.251 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:682.273   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 009:682.579   Data:  00 00
T82D4 009:682.608 - 0.365ms returns 2 (0x2)
T82D4 009:682.628 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:682.650   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 009:683.008   Data:  00 00
T82D4 009:683.037 - 0.417ms returns 2 (0x2)
T82D4 009:683.057 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:683.079   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 009:683.499   Data:  00 00
T82D4 009:683.529 - 0.479ms returns 2 (0x2)
T82D4 009:683.550 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:683.572   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 009:683.879   Data:  00 00
T82D4 009:683.909 - 0.367ms returns 2 (0x2)
T82D4 009:683.930 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:683.951   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 009:684.255   Data:  00 00
T82D4 009:684.285 - 0.363ms returns 2 (0x2)
T82D4 009:684.305 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:684.327   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 009:684.629   Data:  00 00
T82D4 009:684.659 - 0.362ms returns 2 (0x2)
T82D4 009:684.680 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:684.702   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 009:685.009   Data:  00 00
T82D4 009:685.040 - 0.368ms returns 2 (0x2)
T82D4 009:685.061 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:685.081   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 009:685.379   Data:  00 00
T82D4 009:685.409 - 0.357ms returns 2 (0x2)
T82D4 009:685.431 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:685.451   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 009:685.757   Data:  00 00
T82D4 009:685.786 - 0.366ms returns 2 (0x2)
T82D4 009:685.813 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:685.834   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 009:686.133   Data:  00 00
T82D4 009:686.163 - 0.358ms returns 2 (0x2)
T82D4 009:686.184 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:686.205   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 009:686.503   Data:  00 00
T82D4 009:686.533 - 0.356ms returns 2 (0x2)
T82D4 009:686.554 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:686.575   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 009:686.881   Data:  00 00
T82D4 009:686.911 - 0.365ms returns 2 (0x2)
T82D4 009:686.931 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:686.953   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 009:687.248   Data:  00 00
T82D4 009:687.278 - 0.354ms returns 2 (0x2)
T82D4 009:687.299 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:687.321   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 009:687.630   Data:  00 00
T82D4 009:687.658 - 0.368ms returns 2 (0x2)
T82D4 009:687.680 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:687.700   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 009:688.030   Data:  00 00
T82D4 009:688.084 - 0.412ms returns 2 (0x2)
T82D4 009:688.107 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:688.156   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 009:688.480   Data:  00 00
T82D4 009:688.559 - 0.477ms returns 2 (0x2)
T82D4 009:688.712 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:688.744   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 009:689.126   Data:  00 00
T82D4 009:689.195 - 0.491ms returns 2 (0x2)
T82D4 009:689.219 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:689.242   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 009:689.580   Data:  00 00
T82D4 009:689.611 - 0.401ms returns 2 (0x2)
T82D4 009:689.632 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:689.655   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 009:690.008   Data:  00 00
T82D4 009:690.039 - 0.415ms returns 2 (0x2)
T82D4 009:690.061 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:690.082   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 009:690.433   Data:  00 00
T82D4 009:690.491 - 0.439ms returns 2 (0x2)
T82D4 009:690.513 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:690.537   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 009:690.846   Data:  00 00
T82D4 009:690.880 - 0.376ms returns 2 (0x2)
T82D4 009:690.903 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:690.928   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 009:691.246   Data:  00 00
T82D4 009:691.281 - 0.386ms returns 2 (0x2)
T82D4 009:691.303 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:691.326   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 009:691.666   Data:  00 00
T82D4 009:691.700 - 0.405ms returns 2 (0x2)
T82D4 009:691.721 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:691.749   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 009:692.058   Data:  00 00
T82D4 009:692.089 - 0.377ms returns 2 (0x2)
T82D4 009:692.110 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:692.132   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 009:692.432   Data:  00 00
T82D4 009:692.465 - 0.363ms returns 2 (0x2)
T82D4 009:692.486 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:692.506   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 009:692.808   Data:  00 00
T82D4 009:692.840 - 0.388ms returns 2 (0x2)
T82D4 009:692.887 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:692.908   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 009:693.252   Data:  00 00
T82D4 009:693.297 - 0.418ms returns 2 (0x2)
T82D4 009:693.318 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:693.340   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 009:693.656   Data:  00 00
T82D4 009:693.687 - 0.377ms returns 2 (0x2)
T82D4 009:693.707 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:693.734   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 009:694.083   Data:  00 00
T82D4 009:694.114 - 0.415ms returns 2 (0x2)
T82D4 009:694.135 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:694.157   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 009:694.513   Data:  00 00
T82D4 009:694.545 - 0.419ms returns 2 (0x2)
T82D4 009:694.566 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:694.586   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 009:694.936   Data:  00 00
T82D4 009:694.970 - 0.413ms returns 2 (0x2)
T82D4 009:694.992 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:695.015   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 009:695.330   Data:  00 00
T82D4 009:695.362 - 0.378ms returns 2 (0x2)
T82D4 009:695.383 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:695.407   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 009:695.715   Data:  00 00
T82D4 009:695.747 - 0.373ms returns 2 (0x2)
T82D4 009:695.770 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:695.792   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 009:696.114   Data:  00 00
T82D4 009:696.154 - 0.393ms returns 2 (0x2)
T82D4 009:696.177 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:696.202   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 009:696.536   Data:  00 00
T82D4 009:696.570 - 0.402ms returns 2 (0x2)
T82D4 009:696.593 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:696.616   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 009:696.935   Data:  00 00
T82D4 009:696.966 - 0.382ms returns 2 (0x2)
T82D4 009:696.987 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:697.009   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 009:697.308   Data:  00 00
T82D4 009:697.340 - 0.361ms returns 2 (0x2)
T82D4 009:697.361 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:697.382   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 009:697.681   Data:  00 00
T82D4 009:697.712 - 0.361ms returns 2 (0x2)
T82D4 009:697.734 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:697.755   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 009:698.059   Data:  00 00
T82D4 009:698.089 - 0.362ms returns 2 (0x2)
T82D4 009:698.109 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:698.131   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 009:698.431   Data:  00 00
T82D4 009:698.462 - 0.361ms returns 2 (0x2)
T82D4 009:698.482 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:698.505   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 009:698.805   Data:  00 00
T82D4 009:698.836 - 0.362ms returns 2 (0x2)
T82D4 009:698.856 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:698.878   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 009:699.232   Data:  00 00
T82D4 009:699.263 - 0.416ms returns 2 (0x2)
T82D4 009:699.284 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:699.305   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 009:699.604   Data:  00 00
T82D4 009:699.634 - 0.358ms returns 2 (0x2)
T82D4 009:699.655 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:699.677   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 009:700.008   Data:  00 00
T82D4 009:700.039 - 0.392ms returns 2 (0x2)
T82D4 009:700.059 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:700.081   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 009:700.381   Data:  00 00
T82D4 009:700.412 - 0.362ms returns 2 (0x2)
T82D4 009:700.434 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:700.456   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 009:700.755   Data:  00 00
T82D4 009:700.786 - 0.361ms returns 2 (0x2)
T82D4 009:700.807 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:700.828   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 009:701.134   Data:  00 00
T82D4 009:701.164 - 0.365ms returns 2 (0x2)
T82D4 009:701.185 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:701.206   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 009:701.529   Data:  00 00
T82D4 009:701.560 - 0.383ms returns 2 (0x2)
T82D4 009:701.581 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:701.603   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 009:701.906   Data:  00 00
T82D4 009:701.937 - 0.365ms returns 2 (0x2)
T82D4 009:701.958 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:701.980   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 009:702.280   Data:  00 00
T82D4 009:702.312 - 0.362ms returns 2 (0x2)
T82D4 009:702.332 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:702.353   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 009:702.654   Data:  00 00
T82D4 009:702.684 - 0.360ms returns 2 (0x2)
T82D4 009:702.705 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:702.727   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 009:703.035   Data:  00 00
T82D4 009:703.065 - 0.368ms returns 2 (0x2)
T82D4 009:703.085 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:703.107   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 009:703.429   Data:  00 00
T82D4 009:703.460 - 0.383ms returns 2 (0x2)
T82D4 009:703.481 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:703.503   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 009:703.817   Data:  00 00
T82D4 009:703.875 - 0.406ms returns 2 (0x2)
T82D4 009:703.907 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:703.949   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 009:704.315   Data:  00 00
T82D4 009:704.351 - 0.452ms returns 2 (0x2)
T82D4 009:704.374 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:704.397   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 009:704.721   Data:  00 00
T82D4 009:704.753 - 0.388ms returns 2 (0x2)
T82D4 009:706.122 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:706.157   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 009:706.460   Data:  00 00
T82D4 009:706.491 - 0.377ms returns 2 (0x2)
T82D4 009:706.513 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:706.535   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 009:706.848   Data:  00 00
T82D4 009:706.922 - 0.418ms returns 2 (0x2)
T82D4 009:706.960 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:707.021   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 009:707.359   Data:  00 00
T82D4 009:707.395 - 0.443ms returns 2 (0x2)
T82D4 009:707.417 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:707.440   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 009:707.807   Data:  00 00
T82D4 009:707.840 - 0.432ms returns 2 (0x2)
T82D4 009:707.861 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:707.884   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 009:708.230   Data:  00 00
T82D4 009:708.262 - 0.409ms returns 2 (0x2)
T82D4 009:708.282 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:708.303   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 009:708.608   Data:  00 00
T82D4 009:708.638 - 0.364ms returns 2 (0x2)
T82D4 009:708.660 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:708.681   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 009:709.008   Data:  00 00
T82D4 009:709.038 - 0.387ms returns 2 (0x2)
T82D4 009:709.059 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:709.081   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 009:709.380   Data:  00 00
T82D4 009:709.411 - 0.361ms returns 2 (0x2)
T82D4 009:709.432 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:709.454   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 009:709.755   Data:  00 00
T82D4 009:709.787 - 0.364ms returns 2 (0x2)
T82D4 009:709.807 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:709.828   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 009:710.132   Data:  00 00
T82D4 009:710.163 - 0.364ms returns 2 (0x2)
T82D4 009:710.184 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:710.205   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 009:710.505   Data:  00 00
T82D4 009:710.540 - 0.366ms returns 2 (0x2)
T82D4 009:710.563 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:710.586   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 009:710.908   Data:  00 00
T82D4 009:710.948 - 0.394ms returns 2 (0x2)
T82D4 009:710.972 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:710.996   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 009:711.306   Data:  00 00
T82D4 009:711.342 - 0.379ms returns 2 (0x2)
T82D4 009:711.365 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:711.386   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 009:711.695   Data:  00 00
T82D4 009:711.726 - 0.369ms returns 2 (0x2)
T82D4 009:711.749 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:711.771   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 009:712.083   Data:  00 00
T82D4 009:712.113 - 0.373ms returns 2 (0x2)
T82D4 009:712.133 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:712.156   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 009:712.455   Data:  00 00
T82D4 009:712.486 - 0.360ms returns 2 (0x2)
T82D4 009:712.506 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:712.528   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 009:712.830   Data:  00 00
T82D4 009:712.863 - 0.365ms returns 2 (0x2)
T82D4 009:712.883 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:712.905   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 009:713.266   Data:  00 00
T82D4 009:713.297 - 0.423ms returns 2 (0x2)
T82D4 009:713.319 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:713.340   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 009:713.656   Data:  00 00
T82D4 009:713.686 - 0.375ms returns 2 (0x2)
T82D4 009:713.707 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:713.729   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 009:714.034   Data:  00 00
T82D4 009:714.066 - 0.368ms returns 2 (0x2)
T82D4 009:714.089 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:714.113   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 009:714.431   Data:  00 00
T82D4 009:714.466 - 0.385ms returns 2 (0x2)
T82D4 009:714.496 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:714.519   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 009:714.836   Data:  00 00
T82D4 009:714.906 - 0.421ms returns 2 (0x2)
T82D4 009:714.932 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:714.956   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 009:715.282   Data:  00 00
T82D4 009:715.316 - 0.393ms returns 2 (0x2)
T82D4 009:715.398 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:715.425   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 009:715.726   Data:  00 00
T82D4 009:715.759 - 0.369ms returns 2 (0x2)
T82D4 009:715.780 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:715.802   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 009:716.112   Data:  00 00
T82D4 009:716.144 - 0.373ms returns 2 (0x2)
T82D4 009:716.166 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:716.187   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 009:716.507   Data:  00 00
T82D4 009:716.538 - 0.380ms returns 2 (0x2)
T82D4 009:716.559 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:716.582   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 009:716.880   Data:  00 00
T82D4 009:716.912 - 0.361ms returns 2 (0x2)
T82D4 009:716.933 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:716.956   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 009:717.255   Data:  00 00
T82D4 009:717.287 - 0.362ms returns 2 (0x2)
T82D4 009:717.308 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:717.330   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 009:717.630   Data:  00 00
T82D4 009:717.662 - 0.364ms returns 2 (0x2)
T82D4 009:717.685 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:717.706   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 009:718.007   Data:  00 00
T82D4 009:718.038 - 0.361ms returns 2 (0x2)
T82D4 009:718.059 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:718.084   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 009:718.405   Data:  00 00
T82D4 009:718.436 - 0.386ms returns 2 (0x2)
T82D4 009:718.457 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:718.479   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 009:718.779   Data:  00 00
T82D4 009:718.811 - 0.363ms returns 2 (0x2)
T82D4 009:718.832 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:718.853   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 009:719.237   Data:  00 00
T82D4 009:719.272 - 0.450ms returns 2 (0x2)
T82D4 009:719.295 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:719.316   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 009:719.631   Data:  00 00
T82D4 009:719.662 - 0.376ms returns 2 (0x2)
T82D4 009:719.684 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:719.705   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 009:720.008   Data:  00 00
T82D4 009:720.038 - 0.363ms returns 2 (0x2)
T82D4 009:720.059 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:720.081   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 009:720.381   Data:  00 00
T82D4 009:720.411 - 0.361ms returns 2 (0x2)
T82D4 009:720.432 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:720.455   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 009:720.754   Data:  00 00
T82D4 009:720.786 - 0.363ms returns 2 (0x2)
T82D4 009:720.807 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:720.827   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 009:721.133   Data:  00 00
T82D4 009:721.164 - 0.366ms returns 2 (0x2)
T82D4 009:721.185 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:721.207   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 009:721.505   Data:  00 00
T82D4 009:721.535 - 0.358ms returns 2 (0x2)
T82D4 009:721.556 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:721.578   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 009:721.879   Data:  00 00
T82D4 009:721.911 - 0.363ms returns 2 (0x2)
T82D4 009:721.931 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:721.953   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 009:722.255   Data:  00 00
T82D4 009:722.287 - 0.364ms returns 2 (0x2)
T82D4 009:722.308 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:722.328   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 009:722.632   Data:  00 00
T82D4 009:722.665 - 0.365ms returns 2 (0x2)
T82D4 009:722.688 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:722.710   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 009:723.033   Data:  00 00
T82D4 009:723.065 - 0.386ms returns 2 (0x2)
T82D4 009:723.089 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:723.110   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 009:723.440   Data:  00 00
T82D4 009:723.479 - 0.399ms returns 2 (0x2)
T82D4 009:723.503 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:723.527   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 009:723.891   Data:  00 00
T82D4 009:723.956 - 0.477ms returns 2 (0x2)
T82D4 009:723.993 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:724.017   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 009:724.347   Data:  00 00
T82D4 009:724.382 - 0.397ms returns 2 (0x2)
T82D4 009:724.405 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:724.429   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 009:724.731   Data:  00 00
T82D4 009:724.763 - 0.366ms returns 2 (0x2)
T82D4 009:724.783 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:724.805   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 009:725.110   Data:  00 00
T82D4 009:725.141 - 0.367ms returns 2 (0x2)
T82D4 009:725.163 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:725.184   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 009:725.506   Data:  00 00
T82D4 009:725.536 - 0.381ms returns 2 (0x2)
T82D4 009:725.556 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:725.579   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 009:725.880   Data:  00 00
T82D4 009:725.911 - 0.362ms returns 2 (0x2)
T82D4 009:725.931 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:725.954   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 009:726.256   Data:  00 00
T82D4 009:726.288 - 0.365ms returns 2 (0x2)
T82D4 009:726.308 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:726.330   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 009:726.630   Data:  00 00
T82D4 009:726.660 - 0.362ms returns 2 (0x2)
T82D4 009:726.682 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:726.703   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 009:727.008   Data:  00 00
T82D4 009:727.038 - 0.364ms returns 2 (0x2)
T82D4 009:727.058 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:727.080   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 009:727.380   Data:  00 00
T82D4 009:727.411 - 0.361ms returns 2 (0x2)
T82D4 009:727.431 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:727.454   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 009:727.755   Data:  00 00
T82D4 009:727.786 - 0.363ms returns 2 (0x2)
T82D4 009:727.806 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:727.828   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 009:728.133   Data:  00 00
T82D4 009:728.164 - 0.367ms returns 2 (0x2)
T82D4 009:728.186 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:728.206   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 009:728.505   Data:  00 00
T82D4 009:728.535 - 0.357ms returns 2 (0x2)
T82D4 009:728.555 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:728.577   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 009:728.882   Data:  00 00
T82D4 009:728.918 - 0.372ms returns 2 (0x2)
T82D4 009:728.940 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:728.962   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 009:729.282   Data:  00 00
T82D4 009:729.312 - 0.380ms returns 2 (0x2)
T82D4 009:729.334 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:729.356   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 009:729.655   Data:  00 00
T82D4 009:729.685 - 0.359ms returns 2 (0x2)
T82D4 009:729.705 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:729.728   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 009:730.034   Data:  00 00
T82D4 009:730.064 - 0.367ms returns 2 (0x2)
T82D4 009:730.085 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:730.107   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 009:730.405   Data:  00 00
T82D4 009:730.437 - 0.360ms returns 2 (0x2)
T82D4 009:730.457 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:730.478   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 009:730.780   Data:  00 00
T82D4 009:730.810 - 0.361ms returns 2 (0x2)
T82D4 009:730.832 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:730.854   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 009:731.158   Data:  00 00
T82D4 009:731.189 - 0.365ms returns 2 (0x2)
T82D4 009:731.209 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:731.231   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 009:731.529   Data:  00 00
T82D4 009:731.560 - 0.359ms returns 2 (0x2)
T82D4 009:731.581 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:731.603   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 009:732.008   Data:  00 00
T82D4 009:732.039 - 0.467ms returns 2 (0x2)
T82D4 009:732.059 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:732.081   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 009:732.379   Data:  00 00
T82D4 009:732.410 - 0.359ms returns 2 (0x2)
T82D4 009:732.432 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:732.452   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 009:732.754   Data:  00 00
T82D4 009:732.786 - 0.364ms returns 2 (0x2)
T82D4 009:732.807 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:732.828   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 009:733.134   Data:  00 00
T82D4 009:733.168 - 0.369ms returns 2 (0x2)
T82D4 009:733.190 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:733.212   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 009:733.529   Data:  00 00
T82D4 009:733.560 - 0.378ms returns 2 (0x2)
T82D4 009:733.580 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:733.602   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 009:733.906   Data:  00 00
T82D4 009:733.937 - 0.366ms returns 2 (0x2)
T82D4 009:733.958 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:733.980   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 009:734.280   Data:  00 00
T82D4 009:734.311 - 0.361ms returns 2 (0x2)
T82D4 009:734.331 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:734.360   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 009:734.710   Data:  00 00
T82D4 009:734.784 - 0.472ms returns 2 (0x2)
T82D4 009:734.840 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:734.917   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 009:735.261   Data:  00 00
T82D4 009:735.300 - 0.470ms returns 2 (0x2)
T82D4 009:735.324 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:735.348   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 009:735.764   Data:  00 00
T82D4 009:735.806 - 0.491ms returns 2 (0x2)
T82D4 009:735.831 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:735.853   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 009:736.164   Data:  00 00
T82D4 009:736.195 - 0.373ms returns 2 (0x2)
T82D4 009:736.217 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:736.239   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 009:736.559   Data:  00 00
T82D4 009:736.600 - 0.397ms returns 2 (0x2)
T82D4 009:736.628 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:736.651   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 009:737.067   Data:  00 00
T82D4 009:737.098 - 0.477ms returns 2 (0x2)
T82D4 009:737.118 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:737.140   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 009:737.440   Data:  00 00
T82D4 009:737.471 - 0.361ms returns 2 (0x2)
T82D4 009:737.492 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:737.514   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 009:737.812   Data:  00 00
T82D4 009:737.844 - 0.361ms returns 2 (0x2)
T82D4 009:737.864 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:737.885   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 009:738.236   Data:  00 00
T82D4 009:738.266 - 0.410ms returns 2 (0x2)
T82D4 009:738.288 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:738.309   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 009:738.605   Data:  00 00
T82D4 009:738.635 - 0.356ms returns 2 (0x2)
T82D4 009:738.656 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:738.678   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 009:739.008   Data:  00 00
T82D4 009:739.039 - 0.392ms returns 2 (0x2)
T82D4 009:739.060 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:739.082   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 009:739.381   Data:  00 00
T82D4 009:739.412 - 0.361ms returns 2 (0x2)
T82D4 009:739.433 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:739.453   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 009:739.755   Data:  00 00
T82D4 009:739.785 - 0.361ms returns 2 (0x2)
T82D4 009:739.807 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:739.828   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 009:740.137   Data:  00 00
T82D4 009:740.173 - 0.374ms returns 2 (0x2)
T82D4 009:740.195 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:740.218   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 009:740.581   Data:  00 00
T82D4 009:740.616 - 0.429ms returns 2 (0x2)
T82D4 009:740.640 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:740.664   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 009:741.010   Data:  00 00
T82D4 009:741.045 - 0.413ms returns 2 (0x2)
T82D4 009:741.068 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:741.099   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 009:741.407   Data:  00 00
T82D4 009:741.438 - 0.378ms returns 2 (0x2)
T82D4 009:741.461 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:741.483   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 009:741.780   Data:  00 00
T82D4 009:741.841 - 0.389ms returns 2 (0x2)
T82D4 009:741.863 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:741.885   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 009:742.232   Data:  00 00
T82D4 009:742.263 - 0.410ms returns 2 (0x2)
T82D4 009:742.339 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:742.365   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 009:742.664   Data:  00 00
T82D4 009:742.694 - 0.363ms returns 2 (0x2)
T82D4 009:742.714 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:742.737   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 009:743.058   Data:  00 00
T82D4 009:743.089 - 0.383ms returns 2 (0x2)
T82D4 009:743.109 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:743.132   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 009:743.456   Data:  00 00
T82D4 009:743.487 - 0.386ms returns 2 (0x2)
T82D4 009:743.508 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:743.530   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 009:743.829   Data:  00 00
T82D4 009:743.861 - 0.362ms returns 2 (0x2)
T82D4 009:743.882 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:743.903   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 009:744.235   Data:  00 00
T82D4 009:744.266 - 0.394ms returns 2 (0x2)
T82D4 009:744.289 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:744.309   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 009:744.632   Data:  00 00
T82D4 009:744.668 - 0.389ms returns 2 (0x2)
T82D4 009:744.691 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:744.712   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 009:745.034   Data:  00 00
T82D4 009:745.065 - 0.382ms returns 2 (0x2)
T82D4 009:745.085 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:745.108   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 009:745.406   Data:  00 00
T82D4 009:745.439 - 0.362ms returns 2 (0x2)
T82D4 009:745.459 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:745.481   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 009:745.779   Data:  00 00
T82D4 009:745.811 - 0.361ms returns 2 (0x2)
T82D4 009:745.832 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:745.853   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 009:746.159   Data:  00 00
T82D4 009:746.189 - 0.367ms returns 2 (0x2)
T82D4 009:746.211 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:746.232   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 009:746.529   Data:  00 00
T82D4 009:746.560 - 0.357ms returns 2 (0x2)
T82D4 009:746.582 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:746.602   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 009:746.905   Data:  00 00
T82D4 009:746.936 - 0.362ms returns 2 (0x2)
T82D4 009:746.956 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:746.978   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 009:747.281   Data:  00 00
T82D4 009:747.312 - 0.364ms returns 2 (0x2)
T82D4 009:747.332 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:747.354   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 009:747.655   Data:  00 00
T82D4 009:747.687 - 0.364ms returns 2 (0x2)
T82D4 009:747.708 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:747.728   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 009:748.033   Data:  00 00
T82D4 009:748.064 - 0.364ms returns 2 (0x2)
T82D4 009:748.085 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:748.106   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 009:748.405   Data:  00 00
T82D4 009:748.435 - 0.358ms returns 2 (0x2)
T82D4 009:748.456 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:748.478   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 009:748.780   Data:  00 00
T82D4 009:748.812 - 0.365ms returns 2 (0x2)
T82D4 009:748.832 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:748.855   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 009:749.158   Data:  00 00
T82D4 009:749.190 - 0.366ms returns 2 (0x2)
T82D4 009:749.211 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:749.231   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 009:749.602   Data:  00 00
T82D4 009:749.659 - 0.456ms returns 2 (0x2)
T82D4 009:749.681 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:749.705   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 009:750.036   Data:  00 00
T82D4 009:750.069 - 0.396ms returns 2 (0x2)
T82D4 009:750.090 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:750.113   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 009:750.446   Data:  00 00
T82D4 009:750.514 - 0.434ms returns 2 (0x2)
T82D4 009:750.538 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:750.559   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 009:750.862   Data:  00 00
T82D4 009:750.893 - 0.364ms returns 2 (0x2)
T82D4 009:750.914 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:750.936   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 009:751.237   Data:  00 00
T82D4 009:751.269 - 0.364ms returns 2 (0x2)
T82D4 009:751.290 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:751.312   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 009:751.630   Data:  00 00
T82D4 009:751.662 - 0.381ms returns 2 (0x2)
T82D4 009:751.683 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:751.706   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 009:752.008   Data:  00 00
T82D4 009:752.041 - 0.366ms returns 2 (0x2)
T82D4 009:752.062 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:752.083   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 009:752.405   Data:  00 00
T82D4 009:752.437 - 0.383ms returns 2 (0x2)
T82D4 009:752.459 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:752.481   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 009:752.780   Data:  00 00
T82D4 009:752.811 - 0.361ms returns 2 (0x2)
T82D4 009:752.832 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:752.855   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 009:753.163   Data:  00 00
T82D4 009:753.195 - 0.371ms returns 2 (0x2)
T82D4 009:753.215 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:753.237   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 009:753.555   Data:  00 00
T82D4 009:753.587 - 0.380ms returns 2 (0x2)
T82D4 009:753.608 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:753.630   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 009:754.044   Data:  00 00
T82D4 009:754.098 - 0.499ms returns 2 (0x2)
T82D4 009:754.119 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:754.140   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 009:754.439   Data:  00 00
T82D4 009:754.469 - 0.359ms returns 2 (0x2)
T82D4 009:754.491 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:754.511   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 009:754.810   Data:  00 00
T82D4 009:754.841 - 0.358ms returns 2 (0x2)
T82D4 009:754.861 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:754.890   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 009:755.235   Data:  00 00
T82D4 009:755.266 - 0.413ms returns 2 (0x2)
T82D4 009:755.287 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:755.309   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 009:755.604   Data:  00 00
T82D4 009:755.634 - 0.356ms returns 2 (0x2)
T82D4 009:755.655 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:755.677   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 009:756.008   Data:  00 00
T82D4 009:756.039 - 0.393ms returns 2 (0x2)
T82D4 009:756.060 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:756.080   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 009:756.386   Data:  00 00
T82D4 009:756.421 - 0.370ms returns 2 (0x2)
T82D4 009:756.458 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:756.479   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 009:756.783   Data:  00 00
T82D4 009:756.861 - 0.413ms returns 2 (0x2)
T82D4 009:756.884 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:756.908   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 009:757.237   Data:  00 00
T82D4 009:757.271 - 0.397ms returns 2 (0x2)
T82D4 009:757.295 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:757.338   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 009:757.686   Data:  00 00
T82D4 009:757.717 - 0.430ms returns 2 (0x2)
T82D4 009:757.737 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:757.759   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 009:758.084   Data:  00 00
T82D4 009:758.115 - 0.386ms returns 2 (0x2)
T82D4 009:758.135 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:758.158   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 009:758.456   Data:  00 00
T82D4 009:758.488 - 0.361ms returns 2 (0x2)
T82D4 009:758.509 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:758.531   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 009:758.833   Data:  00 00
T82D4 009:758.865 - 0.364ms returns 2 (0x2)
T82D4 009:758.887 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:758.907   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 009:759.235   Data:  00 00
T82D4 009:759.266 - 0.387ms returns 2 (0x2)
T82D4 009:759.288 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:759.309   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 009:759.604   Data:  00 00
T82D4 009:759.635 - 0.355ms returns 2 (0x2)
T82D4 009:759.655 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:759.677   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 009:760.008   Data:  00 00
T82D4 009:760.039 - 0.392ms returns 2 (0x2)
T82D4 009:760.059 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:760.082   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 009:760.379   Data:  00 00
T82D4 009:760.411 - 0.360ms returns 2 (0x2)
T82D4 009:760.431 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:760.452   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 009:760.754   Data:  00 00
T82D4 009:760.785 - 0.363ms returns 2 (0x2)
T82D4 009:760.807 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:760.827   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 009:761.134   Data:  00 00
T82D4 009:761.164 - 0.365ms returns 2 (0x2)
T82D4 009:761.185 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:761.207   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 009:761.505   Data:  00 00
T82D4 009:761.536 - 0.359ms returns 2 (0x2)
T82D4 009:761.556 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:761.579   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 009:761.881   Data:  00 00
T82D4 009:761.912 - 0.364ms returns 2 (0x2)
T82D4 009:761.933 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:761.954   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 009:762.254   Data:  00 00
T82D4 009:762.285 - 0.362ms returns 2 (0x2)
T82D4 009:762.307 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:762.327   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 009:762.632   Data:  00 00
T82D4 009:762.689 - 0.391ms returns 2 (0x2)
T82D4 009:762.712 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:762.749   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 009:763.059   Data:  00 00
T82D4 009:763.090 - 0.386ms returns 2 (0x2)
T82D4 009:763.132 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:763.154   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 009:763.470   Data:  00 00
T82D4 009:763.506 - 0.383ms returns 2 (0x2)
T82D4 009:763.529 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:763.553   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 009:763.863   Data:  00 00
T82D4 009:763.895 - 0.374ms returns 2 (0x2)
T82D4 009:763.916 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:763.942   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 009:764.254   Data:  00 00
T82D4 009:764.286 - 0.378ms returns 2 (0x2)
T82D4 009:764.306 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:764.329   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 009:764.630   Data:  00 00
T82D4 009:764.662 - 0.365ms returns 2 (0x2)
T82D4 009:764.683 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:764.704   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 009:765.009   Data:  00 00
T82D4 009:765.039 - 0.366ms returns 2 (0x2)
T82D4 009:765.061 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:765.082   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 009:765.379   Data:  00 00
T82D4 009:765.410 - 0.356ms returns 2 (0x2)
T82D4 009:765.430 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:765.453   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 009:765.833   Data:  00 00
T82D4 009:765.868 - 0.446ms returns 2 (0x2)
T82D4 009:765.889 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:765.912   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 009:766.234   Data:  00 00
T82D4 009:766.266 - 0.385ms returns 2 (0x2)
T82D4 009:766.286 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:766.309   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 009:766.605   Data:  00 00
T82D4 009:766.637 - 0.359ms returns 2 (0x2)
T82D4 009:766.658 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:766.678   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 009:767.008   Data:  00 00
T82D4 009:767.039 - 0.389ms returns 2 (0x2)
T82D4 009:767.060 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:767.082   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 009:767.380   Data:  00 00
T82D4 009:767.411 - 0.359ms returns 2 (0x2)
T82D4 009:767.432 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:767.454   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 009:767.755   Data:  00 00
T82D4 009:767.786 - 0.362ms returns 2 (0x2)
T82D4 009:767.806 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:767.828   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 009:768.132   Data:  00 00
T82D4 009:768.164 - 0.366ms returns 2 (0x2)
T82D4 009:768.185 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:768.205   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 009:768.505   Data:  00 00
T82D4 009:768.535 - 0.360ms returns 2 (0x2)
T82D4 009:768.559 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:768.580   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 009:768.881   Data:  00 00
T82D4 009:768.912 - 0.361ms returns 2 (0x2)
T82D4 009:768.934 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:768.955   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 009:769.255   Data:  00 00
T82D4 009:769.285 - 0.360ms returns 2 (0x2)
T82D4 009:769.307 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:769.329   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 009:769.630   Data:  00 00
T82D4 009:769.660 - 0.362ms returns 2 (0x2)
T82D4 009:769.681 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:769.703   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 009:770.007   Data:  00 00
T82D4 009:770.038 - 0.365ms returns 2 (0x2)
T82D4 009:770.061 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:770.083   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 009:770.380   Data:  00 00
T82D4 009:770.411 - 0.359ms returns 2 (0x2)
T82D4 009:770.433 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:770.455   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 009:770.756   Data:  00 00
T82D4 009:770.787 - 0.362ms returns 2 (0x2)
T82D4 009:770.808 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:770.830   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 009:771.132   Data:  00 00
T82D4 009:771.164 - 0.364ms returns 2 (0x2)
T82D4 009:771.185 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:771.209   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 009:771.530   Data:  00 00
T82D4 009:771.562 - 0.385ms returns 2 (0x2)
T82D4 009:771.583 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:771.605   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 009:771.904   Data:  00 00
T82D4 009:771.935 - 0.361ms returns 2 (0x2)
T82D4 009:771.958 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:771.979   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 009:772.280   Data:  00 00
T82D4 009:772.310 - 0.360ms returns 2 (0x2)
T82D4 009:772.332 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:772.353   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 009:772.655   Data:  00 00
T82D4 009:772.685 - 0.361ms returns 2 (0x2)
T82D4 009:772.707 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:772.729   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 009:773.033   Data:  00 00
T82D4 009:773.063 - 0.365ms returns 2 (0x2)
T82D4 009:773.085 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:773.107   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 009:773.466   Data:  00 00
T82D4 009:773.527 - 0.451ms returns 2 (0x2)
T82D4 009:773.565 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:773.589   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 009:774.010   Data:  00 00
T82D4 009:774.045 - 0.488ms returns 2 (0x2)
T82D4 009:774.069 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:774.092   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 009:774.442   Data:  00 00
T82D4 009:774.474 - 0.413ms returns 2 (0x2)
T82D4 009:774.495 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:774.518   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 009:774.819   Data:  00 00
T82D4 009:774.852 - 0.365ms returns 2 (0x2)
T82D4 009:774.928 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:774.954   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 009:775.258   Data:  00 00
T82D4 009:775.289 - 0.370ms returns 2 (0x2)
T82D4 009:775.310 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:775.331   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 009:775.631   Data:  00 00
T82D4 009:775.661 - 0.360ms returns 2 (0x2)
T82D4 009:775.683 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:775.704   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 009:776.008   Data:  00 00
T82D4 009:776.038 - 0.363ms returns 2 (0x2)
T82D4 009:776.059 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:776.081   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 009:776.381   Data:  00 00
T82D4 009:776.411 - 0.361ms returns 2 (0x2)
T82D4 009:776.432 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:776.454   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 009:776.755   Data:  00 00
T82D4 009:776.787 - 0.363ms returns 2 (0x2)
T82D4 009:776.807 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:776.828   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 009:777.133   Data:  00 00
T82D4 009:777.164 - 0.366ms returns 2 (0x2)
T82D4 009:777.186 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:777.206   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 009:777.506   Data:  00 00
T82D4 009:777.574 - 0.417ms returns 2 (0x2)
T82D4 009:777.616 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:777.654   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 009:778.008   Data:  00 00
T82D4 009:778.077 - 0.470ms returns 2 (0x2)
T82D4 009:778.098 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:778.157   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 009:778.485   Data:  00 00
T82D4 009:778.524 - 0.436ms returns 2 (0x2)
T82D4 009:778.548 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:778.572   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 009:778.882   Data:  00 00
T82D4 009:778.917 - 0.377ms returns 2 (0x2)
T82D4 009:778.939 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:778.963   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 009:779.280   Data:  00 00
T82D4 009:779.319 - 0.388ms returns 2 (0x2)
T82D4 009:779.340 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:779.362   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 009:779.681   Data:  00 00
T82D4 009:779.713 - 0.383ms returns 2 (0x2)
T82D4 009:779.736 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:779.757   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 009:780.082   Data:  00 00
T82D4 009:780.114 - 0.386ms returns 2 (0x2)
T82D4 009:780.135 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:780.157   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 009:780.458   Data:  00 00
T82D4 009:780.490 - 0.364ms returns 2 (0x2)
T82D4 009:780.511 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:780.534   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 009:780.838   Data:  00 00
T82D4 009:780.873 - 0.370ms returns 2 (0x2)
T82D4 009:780.895 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:780.919   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 009:781.235   Data:  00 00
T82D4 009:781.266 - 0.380ms returns 2 (0x2)
T82D4 009:781.287 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:781.310   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 009:781.631   Data:  00 00
T82D4 009:781.664 - 0.386ms returns 2 (0x2)
T82D4 009:781.686 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:781.708   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 009:782.034   Data:  00 00
T82D4 009:782.066 - 0.389ms returns 2 (0x2)
T82D4 009:782.088 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:782.109   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 009:782.429   Data:  00 00
T82D4 009:782.460 - 0.380ms returns 2 (0x2)
T82D4 009:782.481 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:782.504   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 009:782.806   Data:  00 00
T82D4 009:782.838 - 0.366ms returns 2 (0x2)
T82D4 009:782.859 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:782.882   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 009:783.230   Data:  00 00
T82D4 009:783.263 - 0.412ms returns 2 (0x2)
T82D4 009:783.284 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:783.306   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 009:783.631   Data:  00 00
T82D4 009:783.663 - 0.389ms returns 2 (0x2)
T82D4 009:783.687 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:783.708   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 009:784.033   Data:  00 00
T82D4 009:784.064 - 0.386ms returns 2 (0x2)
T82D4 009:784.086 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:784.108   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 009:784.405   Data:  00 00
T82D4 009:784.436 - 0.359ms returns 2 (0x2)
T82D4 009:784.457 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:784.480   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 009:784.781   Data:  00 00
T82D4 009:784.814 - 0.366ms returns 2 (0x2)
T82D4 009:784.836 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 009:784.858   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 009:785.157   Data:  00 00
T82D4 009:785.190 - 0.363ms returns 2 (0x2)
T82D4 009:786.475 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 009:786.504   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 009:786.808   Data:  00 00 00 00
T82D4 009:786.841 - 0.375ms returns 4 (0x4)
T82D4 009:786.868 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 009:786.891   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 009:787.236   Data:  00 00 00 00
T82D4 009:787.268 - 0.408ms returns 4 (0x4)
T82D4 009:787.293 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 009:787.317   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 009:787.632   Data:  38 80 00 20
T82D4 009:787.663 - 0.379ms returns 4 (0x4)
T82D4 009:787.722 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 009:787.747   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 009:788.060   Data:  38 80 00 20
T82D4 009:788.092 - 0.379ms returns 4 (0x4)
T5E80 009:789.120 JLINK_IsHalted()
T5E80 009:789.454 - 0.348ms returns FALSE
T5E80 009:890.278 JLINK_HasError()
T5E80 009:890.352 JLINK_IsHalted()
T5E80 009:890.767 - 0.438ms returns FALSE
T5E80 009:990.820 JLINK_HasError()
T5E80 009:990.875 JLINK_IsHalted()
T5E80 009:991.283 - 0.421ms returns FALSE
T5E80 010:091.892 JLINK_HasError()
T5E80 010:092.096 JLINK_HasError()
T5E80 010:092.116 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 010:092.143   Data:  E5 A9 BD 08
T5E80 010:092.172   Debug reg: DWT_CYCCNT
T5E80 010:092.198 - 0.090ms returns 1 (0x1)
T82D4 010:093.606 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:093.645   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 010:094.247   Data:  00 00
T82D4 010:094.324 - 0.727ms returns 2 (0x2)
T82D4 010:094.359 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:094.391   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 010:094.848   Data:  00 00
T82D4 010:094.904 - 0.553ms returns 2 (0x2)
T82D4 010:094.933 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:094.961   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 010:095.430   Data:  00 00
T82D4 010:095.471 - 0.547ms returns 2 (0x2)
T82D4 010:095.495 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:095.519   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 010:096.190   Data:  00 00
T82D4 010:096.356 - 0.879ms returns 2 (0x2)
T82D4 010:096.440 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:096.526   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 010:097.053   Data:  00 00
T82D4 010:097.144 - 0.715ms returns 2 (0x2)
T82D4 010:097.178 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:097.206   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 010:097.908   Data:  00 00
T82D4 010:097.975 - 0.806ms returns 2 (0x2)
T82D4 010:098.007 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:098.037   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 010:098.766   Data:  00 00
T82D4 010:098.834 - 0.835ms returns 2 (0x2)
T82D4 010:098.866 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:098.894   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 010:099.614   Data:  00 00
T82D4 010:099.682 - 0.825ms returns 2 (0x2)
T82D4 010:099.714 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:099.742   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 010:100.149   Data:  00 00
T82D4 010:100.191 - 0.486ms returns 2 (0x2)
T82D4 010:100.215 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:100.239   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 010:100.582   Data:  00 00
T82D4 010:100.619 - 0.414ms returns 2 (0x2)
T82D4 010:100.641 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:100.663   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 010:101.008   Data:  00 00
T82D4 010:101.039 - 0.406ms returns 2 (0x2)
T82D4 010:101.060 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:101.082   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 010:101.499   Data:  00 00
T82D4 010:101.530 - 0.478ms returns 2 (0x2)
T82D4 010:101.550 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:101.573   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 010:101.933   Data:  00 00
T82D4 010:101.964 - 0.422ms returns 2 (0x2)
T82D4 010:101.984 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:102.021   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 010:102.357   Data:  00 00
T82D4 010:102.387 - 0.410ms returns 2 (0x2)
T82D4 010:102.408 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:102.429   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 010:102.788   Data:  00 00
T82D4 010:102.819 - 0.421ms returns 2 (0x2)
T82D4 010:102.840 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:102.860   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 010:103.230   Data:  00 00
T82D4 010:103.260 - 0.428ms returns 2 (0x2)
T82D4 010:103.279 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:103.300   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 010:103.662   Data:  00 00
T82D4 010:103.695 - 0.424ms returns 2 (0x2)
T82D4 010:103.717 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:103.739   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 010:104.112   Data:  00 00
T82D4 010:104.142 - 0.433ms returns 2 (0x2)
T82D4 010:104.163 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:104.185   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 010:104.531   Data:  00 00
T82D4 010:104.561 - 0.406ms returns 2 (0x2)
T82D4 010:104.581 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:104.603   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 010:105.009   Data:  00 00
T82D4 010:105.039 - 0.466ms returns 2 (0x2)
T82D4 010:105.060 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:105.080   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 010:105.379   Data:  00 00
T82D4 010:105.409 - 0.358ms returns 2 (0x2)
T82D4 010:105.431 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:105.464   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 010:105.781   Data:  00 00
T82D4 010:105.810 - 0.387ms returns 2 (0x2)
T82D4 010:105.831 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:105.852   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 010:106.157   Data:  00 00
T82D4 010:106.187 - 0.363ms returns 2 (0x2)
T82D4 010:106.207 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:106.228   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 010:106.530   Data:  00 00
T82D4 010:106.559 - 0.360ms returns 2 (0x2)
T82D4 010:106.580 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:106.601   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 010:107.021   Data:  00 00
T82D4 010:107.053 - 0.482ms returns 2 (0x2)
T82D4 010:107.092 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:107.139   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 010:107.457   Data:  00 00
T82D4 010:107.563 - 0.479ms returns 2 (0x2)
T82D4 010:107.606 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:107.661   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 010:108.009   Data:  00 00
T82D4 010:108.042 - 0.444ms returns 2 (0x2)
T82D4 010:108.064 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:108.086   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 010:108.483   Data:  00 00
T82D4 010:108.557 - 0.522ms returns 2 (0x2)
T82D4 010:108.750 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:108.804   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 010:109.255   Data:  00 00
T82D4 010:109.289 - 0.547ms returns 2 (0x2)
T82D4 010:109.310 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:109.333   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 010:109.654   Data:  00 00
T82D4 010:109.722 - 0.420ms returns 2 (0x2)
T82D4 010:109.743 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:109.763   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 010:110.083   Data:  00 00
T82D4 010:110.135 - 0.401ms returns 2 (0x2)
T82D4 010:110.157 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:110.181   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 010:110.505   Data:  00 00
T82D4 010:110.572 - 0.423ms returns 2 (0x2)
T82D4 010:110.592 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:110.614   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 010:110.933   Data:  00 00
T82D4 010:110.965 - 0.381ms returns 2 (0x2)
T82D4 010:110.985 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:111.008   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 010:111.311   Data:  00 00
T82D4 010:111.352 - 0.375ms returns 2 (0x2)
T82D4 010:111.375 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:111.400   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 010:111.711   Data:  00 00
T82D4 010:111.745 - 0.379ms returns 2 (0x2)
T82D4 010:111.768 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:111.792   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 010:112.109   Data:  00 00
T82D4 010:112.142 - 0.464ms returns 2 (0x2)
T82D4 010:112.275 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:112.377   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 010:112.854   Data:  00 00
T82D4 010:112.944 - 0.679ms returns 2 (0x2)
T82D4 010:112.968 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:112.993   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 010:113.332   Data:  00 00
T82D4 010:113.366 - 0.406ms returns 2 (0x2)
T82D4 010:113.388 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:113.409   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 010:113.715   Data:  00 00
T82D4 010:113.747 - 0.367ms returns 2 (0x2)
T82D4 010:113.768 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:113.791   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 010:114.110   Data:  00 00
T82D4 010:114.141 - 0.381ms returns 2 (0x2)
T82D4 010:114.163 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:114.185   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 010:114.506   Data:  00 00
T82D4 010:114.537 - 0.383ms returns 2 (0x2)
T82D4 010:114.558 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:114.581   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 010:114.906   Data:  00 00
T82D4 010:114.938 - 0.388ms returns 2 (0x2)
T82D4 010:114.959 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:114.980   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 010:115.280   Data:  00 00
T82D4 010:115.312 - 0.362ms returns 2 (0x2)
T82D4 010:115.334 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:115.355   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 010:115.656   Data:  00 00
T82D4 010:115.686 - 0.361ms returns 2 (0x2)
T82D4 010:115.707 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:115.730   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 010:116.033   Data:  00 00
T82D4 010:116.064 - 0.366ms returns 2 (0x2)
T82D4 010:116.085 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:116.108   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 010:116.406   Data:  00 00
T82D4 010:116.438 - 0.361ms returns 2 (0x2)
T82D4 010:116.458 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:116.480   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 010:116.781   Data:  00 00
T82D4 010:116.812 - 0.363ms returns 2 (0x2)
T82D4 010:116.834 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:116.855   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 010:117.158   Data:  00 00
T82D4 010:117.189 - 0.363ms returns 2 (0x2)
T82D4 010:117.210 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:117.232   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 010:117.530   Data:  00 00
T82D4 010:117.561 - 0.361ms returns 2 (0x2)
T82D4 010:117.583 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:117.606   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 010:117.931   Data:  00 00
T82D4 010:117.963 - 0.388ms returns 2 (0x2)
T82D4 010:117.984 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:118.006   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 010:118.306   Data:  00 00
T82D4 010:118.338 - 0.363ms returns 2 (0x2)
T82D4 010:118.360 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:118.381   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 010:118.685   Data:  00 00
T82D4 010:118.716 - 0.364ms returns 2 (0x2)
T82D4 010:118.738 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:118.760   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 010:119.058   Data:  00 00
T82D4 010:119.108 - 0.394ms returns 2 (0x2)
T82D4 010:119.143 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:119.165   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 010:119.461   Data:  00 00
T82D4 010:119.491 - 0.356ms returns 2 (0x2)
T82D4 010:119.511 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:119.533   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 010:119.830   Data:  00 00
T82D4 010:119.861 - 0.358ms returns 2 (0x2)
T82D4 010:119.881 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:119.903   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 010:120.237   Data:  00 00
T82D4 010:120.266 - 0.394ms returns 2 (0x2)
T82D4 010:120.287 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:120.308   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 010:120.607   Data:  00 00
T82D4 010:120.637 - 0.358ms returns 2 (0x2)
T82D4 010:120.657 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:120.678   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 010:121.008   Data:  00 00
T82D4 010:121.038 - 0.389ms returns 2 (0x2)
T82D4 010:121.058 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:121.079   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 010:121.380   Data:  00 00
T82D4 010:121.410 - 0.360ms returns 2 (0x2)
T82D4 010:121.430 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:121.449   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 010:121.754   Data:  00 00
T82D4 010:121.783 - 0.361ms returns 2 (0x2)
T82D4 010:121.804 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:121.825   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 010:122.132   Data:  00 00
T82D4 010:122.161 - 0.365ms returns 2 (0x2)
T82D4 010:122.181 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:122.202   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 010:122.505   Data:  00 00
T82D4 010:122.535 - 0.362ms returns 2 (0x2)
T82D4 010:122.555 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:122.576   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 010:122.880   Data:  00 00
T82D4 010:122.910 - 0.362ms returns 2 (0x2)
T82D4 010:122.930 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:122.950   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 010:123.340   Data:  00 00
T82D4 010:123.413 - 0.507ms returns 2 (0x2)
T82D4 010:123.449 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:123.486   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 010:123.855   Data:  00 00
T82D4 010:123.928 - 0.488ms returns 2 (0x2)
T82D4 010:123.951 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:124.002   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 010:124.356   Data:  00 00
T82D4 010:124.391 - 0.448ms returns 2 (0x2)
T82D4 010:124.412 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:124.435   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 010:124.757   Data:  00 00
T82D4 010:124.787 - 0.383ms returns 2 (0x2)
T82D4 010:124.807 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:124.827   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 010:125.133   Data:  00 00
T82D4 010:125.163 - 0.363ms returns 2 (0x2)
T82D4 010:125.183 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:125.203   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 010:125.506   Data:  00 00
T82D4 010:125.535 - 0.360ms returns 2 (0x2)
T82D4 010:125.555 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:125.576   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 010:125.879   Data:  00 00
T82D4 010:125.909 - 0.362ms returns 2 (0x2)
T82D4 010:125.929 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:125.950   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 010:126.255   Data:  00 00
T82D4 010:126.286 - 0.365ms returns 2 (0x2)
T82D4 010:126.305 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:126.326   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 010:126.630   Data:  00 00
T82D4 010:126.659 - 0.361ms returns 2 (0x2)
T82D4 010:126.679 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:126.700   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 010:127.007   Data:  00 00
T82D4 010:127.036 - 0.365ms returns 2 (0x2)
T82D4 010:127.056 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:127.077   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 010:127.380   Data:  00 00
T82D4 010:127.411 - 0.363ms returns 2 (0x2)
T82D4 010:127.431 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:127.452   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 010:127.755   Data:  00 00
T82D4 010:127.784 - 0.361ms returns 2 (0x2)
T82D4 010:127.804 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:127.825   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 010:128.132   Data:  00 00
T82D4 010:128.162 - 0.366ms returns 2 (0x2)
T82D4 010:128.182 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:128.208   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 010:128.596   Data:  00 00
T82D4 010:128.659 - 0.488ms returns 2 (0x2)
T82D4 010:128.693 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:128.730   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 010:129.092   Data:  00 00
T82D4 010:129.133 - 0.449ms returns 2 (0x2)
T82D4 010:129.157 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:129.221   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 010:129.547   Data:  00 00
T82D4 010:129.578 - 0.429ms returns 2 (0x2)
T82D4 010:129.599 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:129.619   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 010:130.008   Data:  00 00
T82D4 010:130.038 - 0.447ms returns 2 (0x2)
T82D4 010:130.059 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:130.079   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 010:130.379   Data:  00 00
T82D4 010:130.409 - 0.358ms returns 2 (0x2)
T82D4 010:130.429 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:130.450   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 010:130.756   Data:  00 00
T82D4 010:130.786 - 0.365ms returns 2 (0x2)
T82D4 010:130.806 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:130.827   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 010:131.132   Data:  00 00
T82D4 010:131.162 - 0.364ms returns 2 (0x2)
T82D4 010:131.182 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:131.201   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 010:131.505   Data:  00 00
T82D4 010:131.534 - 0.360ms returns 2 (0x2)
T82D4 010:131.554 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:131.575   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 010:131.879   Data:  00 00
T82D4 010:131.908 - 0.362ms returns 2 (0x2)
T82D4 010:131.928 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:131.948   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 010:132.254   Data:  00 00
T82D4 010:132.284 - 0.364ms returns 2 (0x2)
T82D4 010:132.304 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:132.325   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 010:132.629   Data:  00 00
T82D4 010:132.660 - 0.364ms returns 2 (0x2)
T82D4 010:132.679 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:132.699   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 010:133.008   Data:  00 00
T82D4 010:133.038 - 0.367ms returns 2 (0x2)
T82D4 010:133.059 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:133.079   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 010:133.498   Data:  00 00
T82D4 010:133.528 - 0.477ms returns 2 (0x2)
T82D4 010:133.547 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:133.568   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 010:133.880   Data:  00 00
T82D4 010:133.910 - 0.371ms returns 2 (0x2)
T82D4 010:133.932 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:133.953   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 010:134.255   Data:  00 00
T82D4 010:134.285 - 0.361ms returns 2 (0x2)
T82D4 010:134.305 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:134.327   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 010:134.630   Data:  00 00
T82D4 010:134.660 - 0.363ms returns 2 (0x2)
T82D4 010:134.681 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:134.701   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 010:135.008   Data:  00 00
T82D4 010:135.038 - 0.366ms returns 2 (0x2)
T82D4 010:135.060 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:135.080   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 010:135.381   Data:  00 00
T82D4 010:135.410 - 0.360ms returns 2 (0x2)
T82D4 010:135.435 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:135.457   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 010:135.756   Data:  00 00
T82D4 010:135.784 - 0.357ms returns 2 (0x2)
T82D4 010:135.805 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:135.826   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 010:136.132   Data:  00 00
T82D4 010:136.161 - 0.364ms returns 2 (0x2)
T82D4 010:136.181 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:136.203   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 010:136.505   Data:  00 00
T82D4 010:136.535 - 0.362ms returns 2 (0x2)
T82D4 010:136.555 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:136.577   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 010:136.880   Data:  00 00
T82D4 010:136.909 - 0.362ms returns 2 (0x2)
T82D4 010:136.930 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:136.951   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 010:137.256   Data:  00 00
T82D4 010:137.286 - 0.365ms returns 2 (0x2)
T82D4 010:137.307 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:137.328   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 010:137.629   Data:  00 00
T82D4 010:137.659 - 0.361ms returns 2 (0x2)
T82D4 010:137.680 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:137.701   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 010:138.008   Data:  00 00
T82D4 010:138.038 - 0.366ms returns 2 (0x2)
T82D4 010:138.060 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:138.079   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 010:138.380   Data:  00 00
T82D4 010:138.409 - 0.357ms returns 2 (0x2)
T82D4 010:138.430 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:138.451   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 010:138.754   Data:  00 00
T82D4 010:138.783 - 0.360ms returns 2 (0x2)
T82D4 010:138.803 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:138.824   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 010:139.133   Data:  00 00
T82D4 010:139.162 - 0.367ms returns 2 (0x2)
T82D4 010:139.183 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:139.232   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 010:139.563   Data:  00 00
T82D4 010:139.592 - 0.416ms returns 2 (0x2)
T82D4 010:139.612 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:139.633   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 010:140.098   Data:  00 00
T82D4 010:140.175 - 0.571ms returns 2 (0x2)
T82D4 010:140.220 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:140.246   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 010:140.577   Data:  00 00
T82D4 010:140.631 - 0.419ms returns 2 (0x2)
T82D4 010:140.711 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:140.738   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 010:141.058   Data:  00 00
T82D4 010:141.089 - 0.385ms returns 2 (0x2)
T82D4 010:141.109 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:141.130   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 010:141.444   Data:  00 00
T82D4 010:141.497 - 0.415ms returns 2 (0x2)
T82D4 010:141.535 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:141.556   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 010:141.881   Data:  00 00
T82D4 010:141.911 - 0.384ms returns 2 (0x2)
T82D4 010:141.931 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:141.952   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 010:142.255   Data:  00 00
T82D4 010:142.284 - 0.362ms returns 2 (0x2)
T82D4 010:142.305 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:142.324   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 010:142.629   Data:  00 00
T82D4 010:142.658 - 0.361ms returns 2 (0x2)
T82D4 010:142.678 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:142.699   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 010:143.008   Data:  00 00
T82D4 010:143.038 - 0.368ms returns 2 (0x2)
T82D4 010:143.057 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:143.101   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 010:143.430   Data:  00 00
T82D4 010:143.460 - 0.411ms returns 2 (0x2)
T82D4 010:143.480 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:143.501   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 010:143.822   Data:  00 00
T82D4 010:143.851 - 0.379ms returns 2 (0x2)
T82D4 010:143.871 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:143.891   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 010:144.273   Data:  00 00
T82D4 010:144.343 - 0.489ms returns 2 (0x2)
T82D4 010:144.386 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:144.425   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 010:144.783   Data:  00 00
T82D4 010:144.816 - 0.438ms returns 2 (0x2)
T82D4 010:144.838 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:144.858   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 010:145.289   Data:  00 00
T82D4 010:145.344 - 0.536ms returns 2 (0x2)
T82D4 010:145.389 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:145.442   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 010:145.794   Data:  00 00
T82D4 010:145.829 - 0.448ms returns 2 (0x2)
T82D4 010:145.850 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:145.871   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 010:146.230   Data:  00 00
T82D4 010:146.259 - 0.418ms returns 2 (0x2)
T82D4 010:146.280 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:146.300   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 010:146.598   Data:  00 00
T82D4 010:146.627 - 0.355ms returns 2 (0x2)
T82D4 010:146.647 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:146.668   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 010:147.064   Data:  00 00
T82D4 010:147.094 - 0.462ms returns 2 (0x2)
T82D4 010:147.121 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:147.142   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 010:147.438   Data:  00 00
T82D4 010:147.468 - 0.355ms returns 2 (0x2)
T82D4 010:147.487 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:147.508   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 010:147.830   Data:  00 00
T82D4 010:147.860 - 0.381ms returns 2 (0x2)
T82D4 010:147.881 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:147.900   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 010:148.233   Data:  00 00
T82D4 010:148.262 - 0.389ms returns 2 (0x2)
T82D4 010:148.282 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:148.303   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 010:148.599   Data:  00 00
T82D4 010:148.629 - 0.355ms returns 2 (0x2)
T82D4 010:148.649 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:148.670   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 010:149.018   Data:  00 00
T82D4 010:149.050 - 0.409ms returns 2 (0x2)
T82D4 010:149.070 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:149.105   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 010:149.404   Data:  00 00
T82D4 010:149.433 - 0.372ms returns 2 (0x2)
T82D4 010:149.454 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:149.474   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 010:149.780   Data:  00 00
T82D4 010:149.809 - 0.363ms returns 2 (0x2)
T82D4 010:149.829 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:149.850   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 010:150.159   Data:  00 00
T82D4 010:150.189 - 0.368ms returns 2 (0x2)
T82D4 010:150.210 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:150.230   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 010:150.530   Data:  00 00
T82D4 010:150.558 - 0.356ms returns 2 (0x2)
T82D4 010:150.578 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:150.599   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 010:150.905   Data:  00 00
T82D4 010:150.935 - 0.365ms returns 2 (0x2)
T82D4 010:150.954 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:150.976   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 010:151.279   Data:  00 00
T82D4 010:151.309 - 0.363ms returns 2 (0x2)
T82D4 010:151.329 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:151.348   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 010:151.655   Data:  00 00
T82D4 010:151.684 - 0.363ms returns 2 (0x2)
T82D4 010:151.705 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:151.725   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 010:152.032   Data:  00 00
T82D4 010:152.062 - 0.365ms returns 2 (0x2)
T82D4 010:152.081 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:152.103   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 010:152.405   Data:  00 00
T82D4 010:152.435 - 0.361ms returns 2 (0x2)
T82D4 010:152.454 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:152.476   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 010:152.781   Data:  00 00
T82D4 010:152.811 - 0.365ms returns 2 (0x2)
T82D4 010:152.831 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:152.850   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 010:153.158   Data:  00 00
T82D4 010:153.187 - 0.364ms returns 2 (0x2)
T82D4 010:153.208 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:153.228   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 010:153.529   Data:  00 00
T82D4 010:153.558 - 0.358ms returns 2 (0x2)
T82D4 010:153.578 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:153.599   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 010:153.904   Data:  00 00
T82D4 010:153.933 - 0.364ms returns 2 (0x2)
T82D4 010:153.953 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:153.974   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 010:154.282   Data:  00 00
T82D4 010:154.312 - 0.367ms returns 2 (0x2)
T82D4 010:154.332 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:154.351   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 010:154.655   Data:  00 00
T82D4 010:154.684 - 0.360ms returns 2 (0x2)
T82D4 010:154.704 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:154.725   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 010:155.032   Data:  00 00
T82D4 010:155.060 - 0.364ms returns 2 (0x2)
T82D4 010:155.080 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:155.101   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 010:155.405   Data:  00 00
T82D4 010:155.434 - 0.362ms returns 2 (0x2)
T82D4 010:155.454 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:155.475   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 010:155.779   Data:  00 00
T82D4 010:155.809 - 0.363ms returns 2 (0x2)
T82D4 010:155.829 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:155.848   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 010:156.159   Data:  00 00
T82D4 010:156.188 - 0.367ms returns 2 (0x2)
T82D4 010:156.208 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:156.229   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 010:156.535   Data:  00 00
T82D4 010:156.565 - 0.395ms returns 2 (0x2)
T82D4 010:156.616 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:156.653   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 010:157.010   Data:  00 00
T82D4 010:157.076 - 0.490ms returns 2 (0x2)
T82D4 010:157.122 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:157.181   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 010:157.492   Data:  00 00
T82D4 010:157.540 - 0.427ms returns 2 (0x2)
T82D4 010:157.563 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:157.584   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 010:157.905   Data:  00 00
T82D4 010:157.934 - 0.379ms returns 2 (0x2)
T82D4 010:157.954 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:158.025   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 010:158.331   Data:  00 00
T82D4 010:158.360 - 0.414ms returns 2 (0x2)
T82D4 010:158.380 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:158.401   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 010:158.698   Data:  00 00
T82D4 010:158.732 - 0.360ms returns 2 (0x2)
T82D4 010:158.751 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:158.772   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 010:159.082   Data:  00 00
T82D4 010:159.112 - 0.369ms returns 2 (0x2)
T82D4 010:159.133 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:159.153   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 010:159.464   Data:  00 00
T82D4 010:159.520 - 0.401ms returns 2 (0x2)
T82D4 010:159.555 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:159.591   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 010:159.933   Data:  00 00
T82D4 010:159.982 - 0.435ms returns 2 (0x2)
T82D4 010:160.024 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:160.095   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 010:160.551   Data:  00 00
T82D4 010:160.658 - 0.642ms returns 2 (0x2)
T82D4 010:160.678 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:160.721   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 010:161.135   Data:  00 00
T82D4 010:161.215 - 0.546ms returns 2 (0x2)
T82D4 010:161.239 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:161.264   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 010:161.577   Data:  00 00
T82D4 010:161.614 - 0.384ms returns 2 (0x2)
T82D4 010:161.638 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:161.660   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 010:162.009   Data:  00 00
T82D4 010:162.040 - 0.411ms returns 2 (0x2)
T82D4 010:162.062 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:162.084   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 010:162.405   Data:  00 00
T82D4 010:162.438 - 0.385ms returns 2 (0x2)
T82D4 010:162.459 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:162.483   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 010:162.780   Data:  00 00
T82D4 010:162.812 - 0.362ms returns 2 (0x2)
T82D4 010:162.833 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:162.855   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 010:163.159   Data:  00 00
T82D4 010:163.191 - 0.368ms returns 2 (0x2)
T82D4 010:163.213 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:163.234   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 010:163.554   Data:  00 00
T82D4 010:163.585 - 0.380ms returns 2 (0x2)
T82D4 010:163.606 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:163.629   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 010:164.008   Data:  00 00
T82D4 010:164.040 - 0.443ms returns 2 (0x2)
T82D4 010:164.061 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:164.083   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 010:164.380   Data:  00 00
T82D4 010:164.412 - 0.360ms returns 2 (0x2)
T82D4 010:164.433 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:164.455   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 010:164.755   Data:  00 00
T82D4 010:164.785 - 0.362ms returns 2 (0x2)
T82D4 010:164.808 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:164.829   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 010:165.133   Data:  00 00
T82D4 010:165.164 - 0.364ms returns 2 (0x2)
T82D4 010:165.185 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:165.207   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 010:165.505   Data:  00 00
T82D4 010:165.537 - 0.360ms returns 2 (0x2)
T82D4 010:165.560 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:165.583   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 010:165.906   Data:  00 00
T82D4 010:165.937 - 0.386ms returns 2 (0x2)
T82D4 010:166.017 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:166.043   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 010:166.342   Data:  00 00
T82D4 010:166.373 - 0.364ms returns 2 (0x2)
T82D4 010:166.394 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:166.416   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 010:166.716   Data:  00 00
T82D4 010:166.747 - 0.362ms returns 2 (0x2)
T82D4 010:166.768 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:166.794   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 010:167.110   Data:  00 00
T82D4 010:167.141 - 0.382ms returns 2 (0x2)
T82D4 010:167.163 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:167.184   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 010:167.505   Data:  00 00
T82D4 010:167.536 - 0.381ms returns 2 (0x2)
T82D4 010:167.564 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:167.587   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 010:167.905   Data:  00 00
T82D4 010:167.936 - 0.380ms returns 2 (0x2)
T82D4 010:167.957 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:167.979   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 010:168.279   Data:  00 00
T82D4 010:168.313 - 0.364ms returns 2 (0x2)
T82D4 010:168.333 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:168.356   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 010:168.656   Data:  00 00
T82D4 010:168.688 - 0.363ms returns 2 (0x2)
T82D4 010:168.709 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:168.730   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 010:169.034   Data:  00 00
T82D4 010:169.065 - 0.364ms returns 2 (0x2)
T82D4 010:169.086 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:169.108   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 010:169.405   Data:  00 00
T82D4 010:169.435 - 0.357ms returns 2 (0x2)
T82D4 010:169.456 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:169.479   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 010:169.781   Data:  00 00
T82D4 010:169.812 - 0.364ms returns 2 (0x2)
T82D4 010:169.832 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:169.855   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 010:170.158   Data:  00 00
T82D4 010:170.190 - 0.366ms returns 2 (0x2)
T82D4 010:170.211 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:170.232   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 010:170.532   Data:  00 00
T82D4 010:170.571 - 0.370ms returns 2 (0x2)
T82D4 010:170.596 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:170.620   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 010:170.935   Data:  00 00
T82D4 010:170.968 - 0.380ms returns 2 (0x2)
T82D4 010:170.989 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:171.010   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 010:171.325   Data:  00 00
T82D4 010:171.370 - 0.389ms returns 2 (0x2)
T82D4 010:171.391 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:171.411   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 010:171.709   Data:  00 00
T82D4 010:171.737 - 0.354ms returns 2 (0x2)
T82D4 010:171.757 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:171.778   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 010:172.082   Data:  00 00
T82D4 010:172.112 - 0.363ms returns 2 (0x2)
T82D4 010:172.132 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:172.153   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 010:172.450   Data:  00 00
T82D4 010:172.481 - 0.357ms returns 2 (0x2)
T82D4 010:172.500 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:172.521   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 010:172.843   Data:  00 00
T82D4 010:172.873 - 0.382ms returns 2 (0x2)
T82D4 010:172.894 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:172.914   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 010:173.284   Data:  00 00
T82D4 010:173.365 - 0.495ms returns 2 (0x2)
T82D4 010:173.404 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:173.429   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 010:173.872   Data:  00 00
T82D4 010:173.923 - 0.548ms returns 2 (0x2)
T82D4 010:173.966 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:174.004   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 010:174.344   Data:  00 00
T82D4 010:174.404 - 0.455ms returns 2 (0x2)
T82D4 010:174.437 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:174.462   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 010:174.802   Data:  00 00
T82D4 010:174.838 - 0.409ms returns 2 (0x2)
T82D4 010:174.861 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:174.882   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 010:175.235   Data:  00 00
T82D4 010:175.265 - 0.412ms returns 2 (0x2)
T82D4 010:175.286 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:175.337   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 010:175.654   Data:  00 00
T82D4 010:175.685 - 0.407ms returns 2 (0x2)
T82D4 010:175.705 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:175.726   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 010:176.034   Data:  00 00
T82D4 010:176.065 - 0.368ms returns 2 (0x2)
T82D4 010:176.085 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:176.106   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 010:176.405   Data:  00 00
T82D4 010:176.435 - 0.359ms returns 2 (0x2)
T82D4 010:176.457 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:176.477   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 010:176.780   Data:  00 00
T82D4 010:176.809 - 0.360ms returns 2 (0x2)
T82D4 010:176.829 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:176.851   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 010:177.158   Data:  00 00
T82D4 010:177.188 - 0.366ms returns 2 (0x2)
T82D4 010:177.207 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:177.229   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 010:177.530   Data:  00 00
T82D4 010:177.560 - 0.361ms returns 2 (0x2)
T82D4 010:177.580 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:177.601   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 010:177.905   Data:  00 00
T82D4 010:177.936 - 0.364ms returns 2 (0x2)
T82D4 010:177.957 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:177.977   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 010:178.282   Data:  00 00
T82D4 010:178.339 - 0.391ms returns 2 (0x2)
T82D4 010:179.708 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:179.738   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 010:180.061   Data:  00 00 00 00
T82D4 010:180.093 - 0.393ms returns 4 (0x4)
T82D4 010:180.121 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:180.144   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 010:180.450   Data:  00 00 00 00
T82D4 010:180.481 - 0.369ms returns 4 (0x4)
T82D4 010:180.505 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:180.526   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 010:180.851   Data:  38 80 00 20
T82D4 010:180.881 - 0.383ms returns 4 (0x4)
T82D4 010:180.944 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:180.968   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 010:181.282   Data:  38 80 00 20
T82D4 010:181.312 - 0.376ms returns 4 (0x4)
T5E80 010:182.284 JLINK_IsHalted()
T5E80 010:182.785 - 0.522ms returns FALSE
T5E80 010:283.125 JLINK_HasError()
T5E80 010:283.244 JLINK_IsHalted()
T5E80 010:283.760 - 0.619ms returns FALSE
T5E80 010:384.525 JLINK_HasError()
T5E80 010:384.728 JLINK_IsHalted()
T5E80 010:385.580 - 0.996ms returns FALSE
T5E80 010:486.016 JLINK_HasError()
T5E80 010:486.073 JLINK_IsHalted()
T5E80 010:486.477 - 0.423ms returns FALSE
T5E80 010:586.604 JLINK_HasError()
T5E80 010:586.743 JLINK_IsHalted()
T5E80 010:587.151 - 0.422ms returns FALSE
T5E80 010:688.113 JLINK_HasError()
T5E80 010:688.168 JLINK_HasError()
T5E80 010:688.187 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 010:688.213   Data:  E5 A9 BD 08
T5E80 010:688.239   Debug reg: DWT_CYCCNT
T5E80 010:688.265 - 0.106ms returns 1 (0x1)
T82D4 010:699.172 JLINK_ReadMemEx(0x20000EF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:699.228   CPU_ReadMem(2 bytes @ 0x20000EF8)
T82D4 010:699.647   Data:  00 00
T82D4 010:699.685 - 0.521ms returns 2 (0x2)
T82D4 010:699.709 JLINK_ReadMemEx(0x20000EFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:699.732   CPU_ReadMem(2 bytes @ 0x20000EFA)
T82D4 010:700.095   Data:  00 00
T82D4 010:700.156 - 0.468ms returns 2 (0x2)
T82D4 010:700.201 JLINK_ReadMemEx(0x20000EFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:700.237   CPU_ReadMem(2 bytes @ 0x20000EFC)
T82D4 010:700.651   Data:  00 00
T82D4 010:700.688 - 0.495ms returns 2 (0x2)
T82D4 010:700.710 JLINK_ReadMemEx(0x20000EFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:700.733   CPU_ReadMem(2 bytes @ 0x20000EFE)
T82D4 010:701.161   Data:  00 00
T82D4 010:701.198 - 0.496ms returns 2 (0x2)
T82D4 010:701.221 JLINK_ReadMemEx(0x20000F00, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:701.244   CPU_ReadMem(2 bytes @ 0x20000F00)
T82D4 010:701.652   Data:  00 00
T82D4 010:701.688 - 0.475ms returns 2 (0x2)
T82D4 010:701.711 JLINK_ReadMemEx(0x20000F02, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:701.735   CPU_ReadMem(2 bytes @ 0x20000F02)
T82D4 010:702.119   Data:  00 00
T82D4 010:702.156 - 0.453ms returns 2 (0x2)
T82D4 010:702.180 JLINK_ReadMemEx(0x20000F04, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:702.203   CPU_ReadMem(2 bytes @ 0x20000F04)
T82D4 010:702.598   Data:  00 00
T82D4 010:702.628 - 0.457ms returns 2 (0x2)
T82D4 010:702.650 JLINK_ReadMemEx(0x20000F06, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:702.671   CPU_ReadMem(2 bytes @ 0x20000F06)
T82D4 010:703.007   Data:  00 00
T82D4 010:703.037 - 0.396ms returns 2 (0x2)
T82D4 010:703.059 JLINK_ReadMemEx(0x20000F08, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:703.079   CPU_ReadMem(2 bytes @ 0x20000F08)
T82D4 010:703.454   Data:  00 00
T82D4 010:703.483 - 0.432ms returns 2 (0x2)
T82D4 010:703.504 JLINK_ReadMemEx(0x20000F0A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:703.525   CPU_ReadMem(2 bytes @ 0x20000F0A)
T82D4 010:703.887   Data:  00 00
T82D4 010:703.915 - 0.419ms returns 2 (0x2)
T82D4 010:703.936 JLINK_ReadMemEx(0x20000F0C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:703.957   CPU_ReadMem(2 bytes @ 0x20000F0C)
T82D4 010:704.373   Data:  00 00
T82D4 010:704.402 - 0.474ms returns 2 (0x2)
T82D4 010:704.422 JLINK_ReadMemEx(0x20000F0E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:704.443   CPU_ReadMem(2 bytes @ 0x20000F0E)
T82D4 010:704.806   Data:  00 00
T82D4 010:704.835 - 0.421ms returns 2 (0x2)
T82D4 010:704.856 JLINK_ReadMemEx(0x20000F10, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:704.877   CPU_ReadMem(2 bytes @ 0x20000F10)
T82D4 010:705.234   Data:  00 00
T82D4 010:705.263 - 0.416ms returns 2 (0x2)
T82D4 010:705.284 JLINK_ReadMemEx(0x20000F12, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:705.306   CPU_ReadMem(2 bytes @ 0x20000F12)
T82D4 010:705.665   Data:  00 00
T82D4 010:705.694 - 0.418ms returns 2 (0x2)
T82D4 010:705.714 JLINK_ReadMemEx(0x20000F14, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:705.736   CPU_ReadMem(2 bytes @ 0x20000F14)
T82D4 010:706.109   Data:  00 00
T82D4 010:706.138 - 0.432ms returns 2 (0x2)
T82D4 010:706.159 JLINK_ReadMemEx(0x20000F16, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:706.180   CPU_ReadMem(2 bytes @ 0x20000F16)
T82D4 010:706.540   Data:  00 00
T82D4 010:706.581 - 0.446ms returns 2 (0x2)
T82D4 010:706.620 JLINK_ReadMemEx(0x20000F18, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:706.671   CPU_ReadMem(2 bytes @ 0x20000F18)
T82D4 010:707.069   Data:  00 00
T82D4 010:707.112 - 0.501ms returns 2 (0x2)
T82D4 010:707.280 JLINK_ReadMemEx(0x20000F1A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:707.316   CPU_ReadMem(2 bytes @ 0x20000F1A)
T82D4 010:707.631   Data:  00 00
T82D4 010:707.664 - 0.392ms returns 2 (0x2)
T82D4 010:707.708 JLINK_ReadMemEx(0x20000F1C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:707.731   CPU_ReadMem(2 bytes @ 0x20000F1C)
T82D4 010:708.112   Data:  00 00
T82D4 010:708.143 - 0.442ms returns 2 (0x2)
T82D4 010:708.163 JLINK_ReadMemEx(0x20000F1E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:708.183   CPU_ReadMem(2 bytes @ 0x20000F1E)
T82D4 010:708.555   Data:  00 00
T82D4 010:708.586 - 0.431ms returns 2 (0x2)
T82D4 010:708.605 JLINK_ReadMemEx(0x20000F20, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:708.634   CPU_ReadMem(2 bytes @ 0x20000F20)
T82D4 010:709.008   Data:  00 00
T82D4 010:709.037 - 0.441ms returns 2 (0x2)
T82D4 010:709.059 JLINK_ReadMemEx(0x20000F22, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:709.082   CPU_ReadMem(2 bytes @ 0x20000F22)
T82D4 010:709.430   Data:  00 00
T82D4 010:709.460 - 0.409ms returns 2 (0x2)
T82D4 010:709.481 JLINK_ReadMemEx(0x20000F24, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:709.502   CPU_ReadMem(2 bytes @ 0x20000F24)
T82D4 010:709.800   Data:  00 00
T82D4 010:709.830 - 0.358ms returns 2 (0x2)
T82D4 010:709.851 JLINK_ReadMemEx(0x20000F26, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:709.873   CPU_ReadMem(2 bytes @ 0x20000F26)
T82D4 010:710.232   Data:  00 00
T82D4 010:710.262 - 0.420ms returns 2 (0x2)
T82D4 010:710.282 JLINK_ReadMemEx(0x20000F28, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:710.303   CPU_ReadMem(2 bytes @ 0x20000F28)
T82D4 010:710.604   Data:  00 00
T82D4 010:710.634 - 0.360ms returns 2 (0x2)
T82D4 010:710.656 JLINK_ReadMemEx(0x20000F2A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:710.676   CPU_ReadMem(2 bytes @ 0x20000F2A)
T82D4 010:711.008   Data:  00 00
T82D4 010:711.038 - 0.390ms returns 2 (0x2)
T82D4 010:711.058 JLINK_ReadMemEx(0x20000F2C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:711.079   CPU_ReadMem(2 bytes @ 0x20000F2C)
T82D4 010:711.379   Data:  00 00
T82D4 010:711.410 - 0.360ms returns 2 (0x2)
T82D4 010:711.430 JLINK_ReadMemEx(0x20000F2E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:711.451   CPU_ReadMem(2 bytes @ 0x20000F2E)
T82D4 010:711.754   Data:  00 00
T82D4 010:711.784 - 0.362ms returns 2 (0x2)
T82D4 010:711.804 JLINK_ReadMemEx(0x20000F30, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:711.825   CPU_ReadMem(2 bytes @ 0x20000F30)
T82D4 010:712.137   Data:  00 00
T82D4 010:712.178 - 0.382ms returns 2 (0x2)
T82D4 010:712.201 JLINK_ReadMemEx(0x20000F32, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:712.225   CPU_ReadMem(2 bytes @ 0x20000F32)
T82D4 010:712.564   Data:  00 00
T82D4 010:712.595 - 0.402ms returns 2 (0x2)
T82D4 010:712.617 JLINK_ReadMemEx(0x20000F34, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:712.638   CPU_ReadMem(2 bytes @ 0x20000F34)
T82D4 010:713.007   Data:  00 00
T82D4 010:713.036 - 0.427ms returns 2 (0x2)
T82D4 010:713.056 JLINK_ReadMemEx(0x20000F36, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:713.077   CPU_ReadMem(2 bytes @ 0x20000F36)
T82D4 010:713.481   Data:  00 00
T82D4 010:713.511 - 0.463ms returns 2 (0x2)
T82D4 010:713.531 JLINK_ReadMemEx(0x20000F38, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:713.552   CPU_ReadMem(2 bytes @ 0x20000F38)
T82D4 010:713.879   Data:  00 00
T82D4 010:713.910 - 0.387ms returns 2 (0x2)
T82D4 010:713.929 JLINK_ReadMemEx(0x20000F3A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:713.950   CPU_ReadMem(2 bytes @ 0x20000F3A)
T82D4 010:714.255   Data:  00 00
T82D4 010:714.285 - 0.364ms returns 2 (0x2)
T82D4 010:714.305 JLINK_ReadMemEx(0x20000F3C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:714.326   CPU_ReadMem(2 bytes @ 0x20000F3C)
T82D4 010:714.629   Data:  00 00
T82D4 010:714.659 - 0.362ms returns 2 (0x2)
T82D4 010:714.680 JLINK_ReadMemEx(0x20000F3E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:714.699   CPU_ReadMem(2 bytes @ 0x20000F3E)
T82D4 010:715.018   Data:  00 00
T82D4 010:715.046 - 0.374ms returns 2 (0x2)
T82D4 010:715.066 JLINK_ReadMemEx(0x20000F40, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:715.087   CPU_ReadMem(2 bytes @ 0x20000F40)
T82D4 010:715.405   Data:  00 00
T82D4 010:715.434 - 0.377ms returns 2 (0x2)
T82D4 010:715.454 JLINK_ReadMemEx(0x20000F42, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:715.475   CPU_ReadMem(2 bytes @ 0x20000F42)
T82D4 010:715.779   Data:  00 00
T82D4 010:715.809 - 0.362ms returns 2 (0x2)
T82D4 010:715.828 JLINK_ReadMemEx(0x20000F44, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:715.849   CPU_ReadMem(2 bytes @ 0x20000F44)
T82D4 010:716.169   Data:  00 00
T82D4 010:716.224 - 0.409ms returns 2 (0x2)
T82D4 010:716.256 JLINK_ReadMemEx(0x20000F46, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:716.304   CPU_ReadMem(2 bytes @ 0x20000F46)
T82D4 010:716.722   Data:  00 00
T82D4 010:716.791 - 0.543ms returns 2 (0x2)
T82D4 010:716.813 JLINK_ReadMemEx(0x20000F48, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:716.870   CPU_ReadMem(2 bytes @ 0x20000F48)
T82D4 010:717.232   Data:  00 00
T82D4 010:717.273 - 0.469ms returns 2 (0x2)
T82D4 010:717.297 JLINK_ReadMemEx(0x20000F4A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:717.322   CPU_ReadMem(2 bytes @ 0x20000F4A)
T82D4 010:717.632   Data:  00 00
T82D4 010:717.666 - 0.377ms returns 2 (0x2)
T82D4 010:717.689 JLINK_ReadMemEx(0x20000F4C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:717.712   CPU_ReadMem(2 bytes @ 0x20000F4C)
T82D4 010:718.034   Data:  00 00
T82D4 010:718.066 - 0.386ms returns 2 (0x2)
T82D4 010:718.087 JLINK_ReadMemEx(0x20000F4E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:718.110   CPU_ReadMem(2 bytes @ 0x20000F4E)
T82D4 010:718.431   Data:  00 00
T82D4 010:718.464 - 0.385ms returns 2 (0x2)
T82D4 010:718.485 JLINK_ReadMemEx(0x20000F50, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:718.506   CPU_ReadMem(2 bytes @ 0x20000F50)
T82D4 010:718.808   Data:  00 00
T82D4 010:718.839 - 0.363ms returns 2 (0x2)
T82D4 010:718.861 JLINK_ReadMemEx(0x20000F52, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:718.884   CPU_ReadMem(2 bytes @ 0x20000F52)
T82D4 010:719.237   Data:  00 00
T82D4 010:719.268 - 0.415ms returns 2 (0x2)
T82D4 010:719.289 JLINK_ReadMemEx(0x20000F54, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:719.312   CPU_ReadMem(2 bytes @ 0x20000F54)
T82D4 010:719.631   Data:  00 00
T82D4 010:719.663 - 0.382ms returns 2 (0x2)
T82D4 010:719.684 JLINK_ReadMemEx(0x20000F56, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:719.706   CPU_ReadMem(2 bytes @ 0x20000F56)
T82D4 010:720.008   Data:  00 00
T82D4 010:720.041 - 0.366ms returns 2 (0x2)
T82D4 010:720.062 JLINK_ReadMemEx(0x20000F58, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:720.084   CPU_ReadMem(2 bytes @ 0x20000F58)
T82D4 010:720.406   Data:  00 00
T82D4 010:720.437 - 0.385ms returns 2 (0x2)
T82D4 010:720.460 JLINK_ReadMemEx(0x20000F5A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:720.481   CPU_ReadMem(2 bytes @ 0x20000F5A)
T82D4 010:720.805   Data:  00 00
T82D4 010:720.835 - 0.384ms returns 2 (0x2)
T82D4 010:720.857 JLINK_ReadMemEx(0x20000F5C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:720.880   CPU_ReadMem(2 bytes @ 0x20000F5C)
T82D4 010:721.234   Data:  00 00
T82D4 010:721.266 - 0.418ms returns 2 (0x2)
T82D4 010:721.287 JLINK_ReadMemEx(0x20000F5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:721.309   CPU_ReadMem(2 bytes @ 0x20000F5E)
T82D4 010:721.631   Data:  00 00
T82D4 010:721.663 - 0.384ms returns 2 (0x2)
T82D4 010:721.684 JLINK_ReadMemEx(0x20000F60, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:721.707   CPU_ReadMem(2 bytes @ 0x20000F60)
T82D4 010:722.009   Data:  00 00
T82D4 010:722.040 - 0.365ms returns 2 (0x2)
T82D4 010:722.061 JLINK_ReadMemEx(0x20000F62, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:722.081   CPU_ReadMem(2 bytes @ 0x20000F62)
T82D4 010:722.380   Data:  00 00
T82D4 010:722.409 - 0.357ms returns 2 (0x2)
T82D4 010:722.430 JLINK_ReadMemEx(0x20000F64, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:722.451   CPU_ReadMem(2 bytes @ 0x20000F64)
T82D4 010:722.754   Data:  00 00
T82D4 010:722.783 - 0.361ms returns 2 (0x2)
T82D4 010:722.803 JLINK_ReadMemEx(0x20000F66, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:722.825   CPU_ReadMem(2 bytes @ 0x20000F66)
T82D4 010:723.162   Data:  00 00
T82D4 010:723.241 - 0.447ms returns 2 (0x2)
T82D4 010:723.265 JLINK_ReadMemEx(0x20000F68, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:723.290   CPU_ReadMem(2 bytes @ 0x20000F68)
T82D4 010:723.632   Data:  00 00
T82D4 010:723.706 - 0.450ms returns 2 (0x2)
T82D4 010:723.744 JLINK_ReadMemEx(0x20000F6A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:723.769   CPU_ReadMem(2 bytes @ 0x20000F6A)
T82D4 010:724.090   Data:  00 00
T82D4 010:724.127 - 0.391ms returns 2 (0x2)
T82D4 010:724.152 JLINK_ReadMemEx(0x20000F6C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:724.177   CPU_ReadMem(2 bytes @ 0x20000F6C)
T82D4 010:724.507   Data:  00 00
T82D4 010:724.537 - 0.394ms returns 2 (0x2)
T82D4 010:724.583 JLINK_ReadMemEx(0x20000F6E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:724.604   CPU_ReadMem(2 bytes @ 0x20000F6E)
T82D4 010:724.947   Data:  00 00
T82D4 010:724.976 - 0.403ms returns 2 (0x2)
T82D4 010:725.000 JLINK_ReadMemEx(0x20000F70, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:725.022   CPU_ReadMem(2 bytes @ 0x20000F70)
T82D4 010:725.340   Data:  00 00
T82D4 010:725.369 - 0.377ms returns 2 (0x2)
T82D4 010:725.389 JLINK_ReadMemEx(0x20000F72, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:725.410   CPU_ReadMem(2 bytes @ 0x20000F72)
T82D4 010:725.734   Data:  00 00
T82D4 010:725.763 - 0.383ms returns 2 (0x2)
T82D4 010:725.783 JLINK_ReadMemEx(0x20000F74, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:725.803   CPU_ReadMem(2 bytes @ 0x20000F74)
T82D4 010:726.109   Data:  00 00
T82D4 010:726.138 - 0.364ms returns 2 (0x2)
T82D4 010:726.159 JLINK_ReadMemEx(0x20000F76, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:726.179   CPU_ReadMem(2 bytes @ 0x20000F76)
T82D4 010:726.481   Data:  00 00
T82D4 010:726.510 - 0.358ms returns 2 (0x2)
T82D4 010:726.529 JLINK_ReadMemEx(0x20000F78, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:726.551   CPU_ReadMem(2 bytes @ 0x20000F78)
T82D4 010:726.871   Data:  00 00
T82D4 010:726.900 - 0.378ms returns 2 (0x2)
T82D4 010:726.919 JLINK_ReadMemEx(0x20000F7A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:726.941   CPU_ReadMem(2 bytes @ 0x20000F7A)
T82D4 010:727.239   Data:  00 00
T82D4 010:727.268 - 0.357ms returns 2 (0x2)
T82D4 010:727.288 JLINK_ReadMemEx(0x20000F7C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:727.309   CPU_ReadMem(2 bytes @ 0x20000F7C)
T82D4 010:727.606   Data:  00 00
T82D4 010:727.636 - 0.356ms returns 2 (0x2)
T82D4 010:727.656 JLINK_ReadMemEx(0x20000F7E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:727.676   CPU_ReadMem(2 bytes @ 0x20000F7E)
T82D4 010:728.007   Data:  00 00
T82D4 010:728.037 - 0.390ms returns 2 (0x2)
T82D4 010:728.057 JLINK_ReadMemEx(0x20000F80, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:728.077   CPU_ReadMem(2 bytes @ 0x20000F80)
T82D4 010:728.379   Data:  00 00
T82D4 010:728.408 - 0.359ms returns 2 (0x2)
T82D4 010:728.428 JLINK_ReadMemEx(0x20000F82, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:728.449   CPU_ReadMem(2 bytes @ 0x20000F82)
T82D4 010:728.754   Data:  00 00
T82D4 010:728.783 - 0.362ms returns 2 (0x2)
T82D4 010:728.802 JLINK_ReadMemEx(0x20000F84, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:728.823   CPU_ReadMem(2 bytes @ 0x20000F84)
T82D4 010:729.149   Data:  00 00
T82D4 010:729.205 - 0.411ms returns 2 (0x2)
T82D4 010:729.227 JLINK_ReadMemEx(0x20000F86, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:729.259   CPU_ReadMem(2 bytes @ 0x20000F86)
T82D4 010:729.567   Data:  00 00
T82D4 010:729.599 - 0.380ms returns 2 (0x2)
T82D4 010:729.620 JLINK_ReadMemEx(0x20000F88, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:729.642   CPU_ReadMem(2 bytes @ 0x20000F88)
T82D4 010:730.057   Data:  00 00
T82D4 010:730.087 - 0.476ms returns 2 (0x2)
T82D4 010:730.108 JLINK_ReadMemEx(0x20000F8A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:730.127   CPU_ReadMem(2 bytes @ 0x20000F8A)
T82D4 010:730.430   Data:  00 00
T82D4 010:730.459 - 0.359ms returns 2 (0x2)
T82D4 010:730.479 JLINK_ReadMemEx(0x20000F8C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:730.500   CPU_ReadMem(2 bytes @ 0x20000F8C)
T82D4 010:730.796   Data:  00 00
T82D4 010:730.825 - 0.353ms returns 2 (0x2)
T82D4 010:730.844 JLINK_ReadMemEx(0x20000F8E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:730.865   CPU_ReadMem(2 bytes @ 0x20000F8E)
T82D4 010:731.234   Data:  00 00
T82D4 010:731.263 - 0.427ms returns 2 (0x2)
T82D4 010:731.283 JLINK_ReadMemEx(0x20000F90, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:731.304   CPU_ReadMem(2 bytes @ 0x20000F90)
T82D4 010:731.606   Data:  00 00
T82D4 010:731.636 - 0.361ms returns 2 (0x2)
T82D4 010:731.656 JLINK_ReadMemEx(0x20000F92, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:731.676   CPU_ReadMem(2 bytes @ 0x20000F92)
T82D4 010:732.007   Data:  00 00
T82D4 010:732.083 - 0.440ms returns 2 (0x2)
T82D4 010:732.240 JLINK_ReadMemEx(0x20000F94, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:732.317   CPU_ReadMem(2 bytes @ 0x20000F94)
T82D4 010:732.656   Data:  00 00
T82D4 010:732.688 - 0.456ms returns 2 (0x2)
T82D4 010:732.709 JLINK_ReadMemEx(0x20000F96, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:732.734   CPU_ReadMem(2 bytes @ 0x20000F96)
T82D4 010:733.058   Data:  00 00
T82D4 010:733.088 - 0.387ms returns 2 (0x2)
T82D4 010:733.108 JLINK_ReadMemEx(0x20000F98, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:733.150   CPU_ReadMem(2 bytes @ 0x20000F98)
T82D4 010:733.468   Data:  00 00
T82D4 010:733.498 - 0.397ms returns 2 (0x2)
T82D4 010:733.517 JLINK_ReadMemEx(0x20000F9A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:733.538   CPU_ReadMem(2 bytes @ 0x20000F9A)
T82D4 010:733.862   Data:  00 00
T82D4 010:733.892 - 0.383ms returns 2 (0x2)
T82D4 010:733.912 JLINK_ReadMemEx(0x20000F9C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:733.932   CPU_ReadMem(2 bytes @ 0x20000F9C)
T82D4 010:734.229   Data:  00 00
T82D4 010:734.259 - 0.356ms returns 2 (0x2)
T82D4 010:734.280 JLINK_ReadMemEx(0x20000F9E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:734.299   CPU_ReadMem(2 bytes @ 0x20000F9E)
T82D4 010:734.597   Data:  00 00
T82D4 010:734.626 - 0.354ms returns 2 (0x2)
T82D4 010:734.645 JLINK_ReadMemEx(0x20000FA0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:734.667   CPU_ReadMem(2 bytes @ 0x20000FA0)
T82D4 010:735.060   Data:  00 00
T82D4 010:735.090 - 0.452ms returns 2 (0x2)
T82D4 010:735.110 JLINK_ReadMemEx(0x20000FA2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:735.130   CPU_ReadMem(2 bytes @ 0x20000FA2)
T82D4 010:735.429   Data:  00 00
T82D4 010:735.459 - 0.357ms returns 2 (0x2)
T82D4 010:735.478 JLINK_ReadMemEx(0x20000FA4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:735.499   CPU_ReadMem(2 bytes @ 0x20000FA4)
T82D4 010:735.796   Data:  00 00
T82D4 010:735.825 - 0.355ms returns 2 (0x2)
T82D4 010:735.846 JLINK_ReadMemEx(0x20000FA6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:735.865   CPU_ReadMem(2 bytes @ 0x20000FA6)
T82D4 010:736.230   Data:  00 00
T82D4 010:736.258 - 0.421ms returns 2 (0x2)
T82D4 010:736.279 JLINK_ReadMemEx(0x20000FA8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:736.299   CPU_ReadMem(2 bytes @ 0x20000FA8)
T82D4 010:736.596   Data:  00 00
T82D4 010:736.625 - 0.354ms returns 2 (0x2)
T82D4 010:736.645 JLINK_ReadMemEx(0x20000FAA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:736.666   CPU_ReadMem(2 bytes @ 0x20000FAA)
T82D4 010:737.007   Data:  00 00
T82D4 010:737.038 - 0.401ms returns 2 (0x2)
T82D4 010:737.058 JLINK_ReadMemEx(0x20000FAC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:737.079   CPU_ReadMem(2 bytes @ 0x20000FAC)
T82D4 010:737.379   Data:  00 00
T82D4 010:737.410 - 0.360ms returns 2 (0x2)
T82D4 010:737.429 JLINK_ReadMemEx(0x20000FAE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:737.449   CPU_ReadMem(2 bytes @ 0x20000FAE)
T82D4 010:737.755   Data:  00 00
T82D4 010:737.784 - 0.363ms returns 2 (0x2)
T82D4 010:737.805 JLINK_ReadMemEx(0x20000FB0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:737.825   CPU_ReadMem(2 bytes @ 0x20000FB0)
T82D4 010:738.132   Data:  00 00
T82D4 010:738.161 - 0.365ms returns 2 (0x2)
T82D4 010:738.181 JLINK_ReadMemEx(0x20000FB2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:738.202   CPU_ReadMem(2 bytes @ 0x20000FB2)
T82D4 010:738.504   Data:  00 00
T82D4 010:738.534 - 0.361ms returns 2 (0x2)
T82D4 010:738.554 JLINK_ReadMemEx(0x20000FB4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:738.575   CPU_ReadMem(2 bytes @ 0x20000FB4)
T82D4 010:738.878   Data:  00 00
T82D4 010:738.908 - 0.363ms returns 2 (0x2)
T82D4 010:738.928 JLINK_ReadMemEx(0x20000FB6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:738.948   CPU_ReadMem(2 bytes @ 0x20000FB6)
T82D4 010:739.254   Data:  00 00
T82D4 010:739.284 - 0.364ms returns 2 (0x2)
T82D4 010:739.305 JLINK_ReadMemEx(0x20000FB8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:739.325   CPU_ReadMem(2 bytes @ 0x20000FB8)
T82D4 010:739.713   Data:  00 00
T82D4 010:739.787 - 0.511ms returns 2 (0x2)
T82D4 010:739.831 JLINK_ReadMemEx(0x20000FBA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:739.855   CPU_ReadMem(2 bytes @ 0x20000FBA)
T82D4 010:740.240   Data:  00 00
T82D4 010:740.308 - 0.485ms returns 2 (0x2)
T82D4 010:740.329 JLINK_ReadMemEx(0x20000FBC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:740.351   CPU_ReadMem(2 bytes @ 0x20000FBC)
T82D4 010:740.681   Data:  00 00
T82D4 010:740.712 - 0.391ms returns 2 (0x2)
T82D4 010:740.732 JLINK_ReadMemEx(0x20000FBE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:740.754   CPU_ReadMem(2 bytes @ 0x20000FBE)
T82D4 010:741.067   Data:  00 00
T82D4 010:741.128 - 0.421ms returns 2 (0x2)
T82D4 010:741.165 JLINK_ReadMemEx(0x20000FC0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:741.185   CPU_ReadMem(2 bytes @ 0x20000FC0)
T82D4 010:741.491   Data:  00 00
T82D4 010:741.520 - 0.364ms returns 2 (0x2)
T82D4 010:741.542 JLINK_ReadMemEx(0x20000FC2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:741.562   CPU_ReadMem(2 bytes @ 0x20000FC2)
T82D4 010:741.880   Data:  00 00
T82D4 010:741.910 - 0.376ms returns 2 (0x2)
T82D4 010:741.929 JLINK_ReadMemEx(0x20000FC4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:741.951   CPU_ReadMem(2 bytes @ 0x20000FC4)
T82D4 010:742.254   Data:  00 00
T82D4 010:742.284 - 0.363ms returns 2 (0x2)
T82D4 010:742.303 JLINK_ReadMemEx(0x20000FC6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:742.325   CPU_ReadMem(2 bytes @ 0x20000FC6)
T82D4 010:742.630   Data:  00 00
T82D4 010:742.660 - 0.364ms returns 2 (0x2)
T82D4 010:742.679 JLINK_ReadMemEx(0x20000FC8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:742.699   CPU_ReadMem(2 bytes @ 0x20000FC8)
T82D4 010:743.008   Data:  00 00
T82D4 010:743.037 - 0.367ms returns 2 (0x2)
T82D4 010:743.059 JLINK_ReadMemEx(0x20000FCA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:743.078   CPU_ReadMem(2 bytes @ 0x20000FCA)
T82D4 010:743.479   Data:  00 00
T82D4 010:743.508 - 0.457ms returns 2 (0x2)
T82D4 010:743.527 JLINK_ReadMemEx(0x20000FCC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:743.548   CPU_ReadMem(2 bytes @ 0x20000FCC)
T82D4 010:743.856   Data:  00 00
T82D4 010:743.885 - 0.366ms returns 2 (0x2)
T82D4 010:743.906 JLINK_ReadMemEx(0x20000FCE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:743.927   CPU_ReadMem(2 bytes @ 0x20000FCE)
T82D4 010:744.233   Data:  00 00
T82D4 010:744.263 - 0.365ms returns 2 (0x2)
T82D4 010:744.283 JLINK_ReadMemEx(0x20000FD0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:744.303   CPU_ReadMem(2 bytes @ 0x20000FD0)
T82D4 010:744.600   Data:  00 00
T82D4 010:744.630 - 0.356ms returns 2 (0x2)
T82D4 010:744.651 JLINK_ReadMemEx(0x20000FD2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:744.670   CPU_ReadMem(2 bytes @ 0x20000FD2)
T82D4 010:745.040   Data:  00 00
T82D4 010:745.112 - 0.485ms returns 2 (0x2)
T82D4 010:745.170 JLINK_ReadMemEx(0x20000FD4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:745.227   CPU_ReadMem(2 bytes @ 0x20000FD4)
T82D4 010:745.580   Data:  00 00
T82D4 010:745.612 - 0.450ms returns 2 (0x2)
T82D4 010:745.633 JLINK_ReadMemEx(0x20000FD6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:745.654   CPU_ReadMem(2 bytes @ 0x20000FD6)
T82D4 010:746.008   Data:  00 00
T82D4 010:746.038 - 0.414ms returns 2 (0x2)
T82D4 010:746.060 JLINK_ReadMemEx(0x20000FD8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:746.079   CPU_ReadMem(2 bytes @ 0x20000FD8)
T82D4 010:746.379   Data:  00 00
T82D4 010:746.408 - 0.356ms returns 2 (0x2)
T82D4 010:746.428 JLINK_ReadMemEx(0x20000FDA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:746.448   CPU_ReadMem(2 bytes @ 0x20000FDA)
T82D4 010:746.755   Data:  00 00
T82D4 010:746.784 - 0.364ms returns 2 (0x2)
T82D4 010:746.804 JLINK_ReadMemEx(0x20000FDC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:746.825   CPU_ReadMem(2 bytes @ 0x20000FDC)
T82D4 010:747.131   Data:  00 00
T82D4 010:747.161 - 0.365ms returns 2 (0x2)
T82D4 010:747.181 JLINK_ReadMemEx(0x20000FDE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:747.202   CPU_ReadMem(2 bytes @ 0x20000FDE)
T82D4 010:747.504   Data:  00 00
T82D4 010:747.534 - 0.361ms returns 2 (0x2)
T82D4 010:747.554 JLINK_ReadMemEx(0x20000FE0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:747.573   CPU_ReadMem(2 bytes @ 0x20000FE0)
T82D4 010:747.879   Data:  00 00
T82D4 010:747.915 - 0.413ms returns 2 (0x2)
T82D4 010:747.990 JLINK_ReadMemEx(0x20000FE2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:748.044   CPU_ReadMem(2 bytes @ 0x20000FE2)
T82D4 010:748.380   Data:  00 00
T82D4 010:748.413 - 0.431ms returns 2 (0x2)
T82D4 010:748.435 JLINK_ReadMemEx(0x20000FE4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:748.455   CPU_ReadMem(2 bytes @ 0x20000FE4)
T82D4 010:748.780   Data:  00 00
T82D4 010:748.809 - 0.382ms returns 2 (0x2)
T82D4 010:748.828 JLINK_ReadMemEx(0x20000FE6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:748.849   CPU_ReadMem(2 bytes @ 0x20000FE6)
T82D4 010:749.156   Data:  00 00
T82D4 010:749.186 - 0.366ms returns 2 (0x2)
T82D4 010:749.206 JLINK_ReadMemEx(0x20000FE8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:749.227   CPU_ReadMem(2 bytes @ 0x20000FE8)
T82D4 010:749.529   Data:  00 00
T82D4 010:749.559 - 0.361ms returns 2 (0x2)
T82D4 010:749.578 JLINK_ReadMemEx(0x20000FEA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:749.599   CPU_ReadMem(2 bytes @ 0x20000FEA)
T82D4 010:749.905   Data:  00 00
T82D4 010:749.935 - 0.365ms returns 2 (0x2)
T82D4 010:749.955 JLINK_ReadMemEx(0x20000FEC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:749.976   CPU_ReadMem(2 bytes @ 0x20000FEC)
T82D4 010:750.279   Data:  00 00
T82D4 010:750.309 - 0.362ms returns 2 (0x2)
T82D4 010:750.329 JLINK_ReadMemEx(0x20000FEE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:750.350   CPU_ReadMem(2 bytes @ 0x20000FEE)
T82D4 010:750.655   Data:  00 00
T82D4 010:750.684 - 0.364ms returns 2 (0x2)
T82D4 010:750.705 JLINK_ReadMemEx(0x20000FF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:750.725   CPU_ReadMem(2 bytes @ 0x20000FF0)
T82D4 010:751.033   Data:  00 00
T82D4 010:751.061 - 0.364ms returns 2 (0x2)
T82D4 010:751.081 JLINK_ReadMemEx(0x20000FF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:751.102   CPU_ReadMem(2 bytes @ 0x20000FF2)
T82D4 010:751.404   Data:  00 00
T82D4 010:751.433 - 0.360ms returns 2 (0x2)
T82D4 010:751.453 JLINK_ReadMemEx(0x20000FF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:751.474   CPU_ReadMem(2 bytes @ 0x20000FF4)
T82D4 010:751.780   Data:  00 00
T82D4 010:751.809 - 0.364ms returns 2 (0x2)
T82D4 010:751.829 JLINK_ReadMemEx(0x20000FF6, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:751.849   CPU_ReadMem(2 bytes @ 0x20000FF6)
T82D4 010:752.233   Data:  00 00
T82D4 010:752.263 - 0.442ms returns 2 (0x2)
T82D4 010:752.283 JLINK_ReadMemEx(0x20000FF8, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:752.302   CPU_ReadMem(2 bytes @ 0x20000FF8)
T82D4 010:752.606   Data:  00 00
T82D4 010:752.635 - 0.360ms returns 2 (0x2)
T82D4 010:752.655 JLINK_ReadMemEx(0x20000FFA, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:752.676   CPU_ReadMem(2 bytes @ 0x20000FFA)
T82D4 010:753.008   Data:  00 00
T82D4 010:753.037 - 0.389ms returns 2 (0x2)
T82D4 010:753.056 JLINK_ReadMemEx(0x20000FFC, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:753.077   CPU_ReadMem(2 bytes @ 0x20000FFC)
T82D4 010:753.379   Data:  00 00
T82D4 010:753.408 - 0.360ms returns 2 (0x2)
T82D4 010:753.428 JLINK_ReadMemEx(0x20000FFE, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:753.449   CPU_ReadMem(2 bytes @ 0x20000FFE)
T82D4 010:753.755   Data:  00 00
T82D4 010:753.785 - 0.365ms returns 2 (0x2)
T82D4 010:753.805 JLINK_ReadMemEx(0x20001000, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:753.824   CPU_ReadMem(2 bytes @ 0x20001000)
T82D4 010:754.133   Data:  00 00
T82D4 010:754.162 - 0.364ms returns 2 (0x2)
T82D4 010:754.182 JLINK_ReadMemEx(0x20001002, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:754.202   CPU_ReadMem(2 bytes @ 0x20001002)
T82D4 010:754.505   Data:  00 00
T82D4 010:754.533 - 0.359ms returns 2 (0x2)
T82D4 010:754.553 JLINK_ReadMemEx(0x20001004, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:754.574   CPU_ReadMem(2 bytes @ 0x20001004)
T82D4 010:754.880   Data:  00 00
T82D4 010:754.909 - 0.364ms returns 2 (0x2)
T82D4 010:754.928 JLINK_ReadMemEx(0x20001006, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:754.949   CPU_ReadMem(2 bytes @ 0x20001006)
T82D4 010:755.253   Data:  00 00
T82D4 010:755.284 - 0.363ms returns 2 (0x2)
T82D4 010:755.303 JLINK_ReadMemEx(0x20001008, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:755.323   CPU_ReadMem(2 bytes @ 0x20001008)
T82D4 010:755.630   Data:  00 00
T82D4 010:755.658 - 0.363ms returns 2 (0x2)
T82D4 010:755.679 JLINK_ReadMemEx(0x2000100A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:755.755   CPU_ReadMem(2 bytes @ 0x2000100A)
T82D4 010:756.109   Data:  00 00
T82D4 010:756.139 - 0.468ms returns 2 (0x2)
T82D4 010:756.159 JLINK_ReadMemEx(0x2000100C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:756.181   CPU_ReadMem(2 bytes @ 0x2000100C)
T82D4 010:756.513   Data:  00 00
T82D4 010:756.571 - 0.435ms returns 2 (0x2)
T82D4 010:756.673 JLINK_ReadMemEx(0x2000100E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:756.697   CPU_ReadMem(2 bytes @ 0x2000100E)
T82D4 010:757.033   Data:  00 00
T82D4 010:757.063 - 0.398ms returns 2 (0x2)
T82D4 010:757.083 JLINK_ReadMemEx(0x20001010, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:757.104   CPU_ReadMem(2 bytes @ 0x20001010)
T82D4 010:757.498   Data:  00 00
T82D4 010:757.530 - 0.456ms returns 2 (0x2)
T82D4 010:757.551 JLINK_ReadMemEx(0x20001012, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:757.571   CPU_ReadMem(2 bytes @ 0x20001012)
T82D4 010:757.881   Data:  00 00
T82D4 010:757.911 - 0.368ms returns 2 (0x2)
T82D4 010:757.933 JLINK_ReadMemEx(0x20001014, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:757.954   CPU_ReadMem(2 bytes @ 0x20001014)
T82D4 010:758.254   Data:  00 00
T82D4 010:758.283 - 0.358ms returns 2 (0x2)
T82D4 010:758.303 JLINK_ReadMemEx(0x20001016, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:758.325   CPU_ReadMem(2 bytes @ 0x20001016)
T82D4 010:758.630   Data:  00 00
T82D4 010:758.658 - 0.363ms returns 2 (0x2)
T82D4 010:758.679 JLINK_ReadMemEx(0x20001018, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:758.700   CPU_ReadMem(2 bytes @ 0x20001018)
T82D4 010:759.008   Data:  00 00
T82D4 010:759.038 - 0.367ms returns 2 (0x2)
T82D4 010:759.058 JLINK_ReadMemEx(0x2000101A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:759.079   CPU_ReadMem(2 bytes @ 0x2000101A)
T82D4 010:759.379   Data:  00 00
T82D4 010:759.409 - 0.358ms returns 2 (0x2)
T82D4 010:759.429 JLINK_ReadMemEx(0x2000101C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:759.451   CPU_ReadMem(2 bytes @ 0x2000101C)
T82D4 010:759.755   Data:  00 00
T82D4 010:759.785 - 0.363ms returns 2 (0x2)
T82D4 010:759.805 JLINK_ReadMemEx(0x2000101E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:759.827   CPU_ReadMem(2 bytes @ 0x2000101E)
T82D4 010:760.132   Data:  00 00
T82D4 010:760.162 - 0.366ms returns 2 (0x2)
T82D4 010:760.183 JLINK_ReadMemEx(0x20001020, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:760.204   CPU_ReadMem(2 bytes @ 0x20001020)
T82D4 010:760.505   Data:  00 00
T82D4 010:760.534 - 0.360ms returns 2 (0x2)
T82D4 010:760.557 JLINK_ReadMemEx(0x20001022, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:760.576   CPU_ReadMem(2 bytes @ 0x20001022)
T82D4 010:760.879   Data:  00 00
T82D4 010:760.909 - 0.362ms returns 2 (0x2)
T82D4 010:760.932 JLINK_ReadMemEx(0x20001024, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:760.951   CPU_ReadMem(2 bytes @ 0x20001024)
T82D4 010:761.254   Data:  00 00
T82D4 010:761.283 - 0.359ms returns 2 (0x2)
T82D4 010:761.304 JLINK_ReadMemEx(0x20001026, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:761.325   CPU_ReadMem(2 bytes @ 0x20001026)
T82D4 010:761.630   Data:  00 00
T82D4 010:761.659 - 0.363ms returns 2 (0x2)
T82D4 010:761.679 JLINK_ReadMemEx(0x20001028, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:761.700   CPU_ReadMem(2 bytes @ 0x20001028)
T82D4 010:762.033   Data:  00 00
T82D4 010:762.071 - 0.400ms returns 2 (0x2)
T82D4 010:762.095 JLINK_ReadMemEx(0x2000102A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:762.154   CPU_ReadMem(2 bytes @ 0x2000102A)
T82D4 010:762.505   Data:  00 00
T82D4 010:762.538 - 0.451ms returns 2 (0x2)
T82D4 010:762.559 JLINK_ReadMemEx(0x2000102C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:762.582   CPU_ReadMem(2 bytes @ 0x2000102C)
T82D4 010:762.879   Data:  00 00
T82D4 010:762.909 - 0.357ms returns 2 (0x2)
T82D4 010:762.930 JLINK_ReadMemEx(0x2000102E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:762.972   CPU_ReadMem(2 bytes @ 0x2000102E)
T82D4 010:763.304   Data:  00 00
T82D4 010:763.333 - 0.412ms returns 2 (0x2)
T82D4 010:763.354 JLINK_ReadMemEx(0x20001030, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:763.376   CPU_ReadMem(2 bytes @ 0x20001030)
T82D4 010:763.704   Data:  00 00
T82D4 010:763.734 - 0.388ms returns 2 (0x2)
T82D4 010:763.807 JLINK_ReadMemEx(0x20001032, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:763.831   CPU_ReadMem(2 bytes @ 0x20001032)
T82D4 010:764.142   Data:  00 00
T82D4 010:764.200 - 0.402ms returns 2 (0x2)
T82D4 010:764.224 JLINK_ReadMemEx(0x20001034, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:764.248   CPU_ReadMem(2 bytes @ 0x20001034)
T82D4 010:764.575   Data:  00 00
T82D4 010:764.609 - 0.394ms returns 2 (0x2)
T82D4 010:764.645 JLINK_ReadMemEx(0x20001036, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:764.668   CPU_ReadMem(2 bytes @ 0x20001036)
T82D4 010:765.008   Data:  00 00
T82D4 010:765.038 - 0.401ms returns 2 (0x2)
T82D4 010:765.058 JLINK_ReadMemEx(0x20001038, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:765.079   CPU_ReadMem(2 bytes @ 0x20001038)
T82D4 010:765.379   Data:  00 00
T82D4 010:765.409 - 0.360ms returns 2 (0x2)
T82D4 010:765.430 JLINK_ReadMemEx(0x2000103A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:765.449   CPU_ReadMem(2 bytes @ 0x2000103A)
T82D4 010:765.756   Data:  00 00
T82D4 010:765.785 - 0.363ms returns 2 (0x2)
T82D4 010:765.804 JLINK_ReadMemEx(0x2000103C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:765.826   CPU_ReadMem(2 bytes @ 0x2000103C)
T82D4 010:766.132   Data:  00 00
T82D4 010:766.161 - 0.365ms returns 2 (0x2)
T82D4 010:766.181 JLINK_ReadMemEx(0x2000103E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:766.202   CPU_ReadMem(2 bytes @ 0x2000103E)
T82D4 010:766.504   Data:  00 00
T82D4 010:766.534 - 0.361ms returns 2 (0x2)
T82D4 010:766.554 JLINK_ReadMemEx(0x20001040, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:766.574   CPU_ReadMem(2 bytes @ 0x20001040)
T82D4 010:766.878   Data:  00 00
T82D4 010:766.930 - 0.385ms returns 2 (0x2)
T82D4 010:766.965 JLINK_ReadMemEx(0x20001042, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:766.984   CPU_ReadMem(2 bytes @ 0x20001042)
T82D4 010:767.280   Data:  00 00
T82D4 010:767.309 - 0.352ms returns 2 (0x2)
T82D4 010:767.329 JLINK_ReadMemEx(0x20001044, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:767.349   CPU_ReadMem(2 bytes @ 0x20001044)
T82D4 010:767.655   Data:  00 00
T82D4 010:767.684 - 0.363ms returns 2 (0x2)
T82D4 010:767.704 JLINK_ReadMemEx(0x20001046, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:767.725   CPU_ReadMem(2 bytes @ 0x20001046)
T82D4 010:768.032   Data:  00 00
T82D4 010:768.062 - 0.366ms returns 2 (0x2)
T82D4 010:768.082 JLINK_ReadMemEx(0x20001048, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:768.103   CPU_ReadMem(2 bytes @ 0x20001048)
T82D4 010:768.405   Data:  00 00
T82D4 010:768.435 - 0.361ms returns 2 (0x2)
T82D4 010:768.455 JLINK_ReadMemEx(0x2000104A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:768.474   CPU_ReadMem(2 bytes @ 0x2000104A)
T82D4 010:768.780   Data:  00 00
T82D4 010:768.808 - 0.362ms returns 2 (0x2)
T82D4 010:768.829 JLINK_ReadMemEx(0x2000104C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:768.849   CPU_ReadMem(2 bytes @ 0x2000104C)
T82D4 010:769.158   Data:  00 00
T82D4 010:769.186 - 0.365ms returns 2 (0x2)
T82D4 010:769.206 JLINK_ReadMemEx(0x2000104E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:769.227   CPU_ReadMem(2 bytes @ 0x2000104E)
T82D4 010:769.530   Data:  00 00
T82D4 010:769.560 - 0.362ms returns 2 (0x2)
T82D4 010:769.580 JLINK_ReadMemEx(0x20001050, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:769.601   CPU_ReadMem(2 bytes @ 0x20001050)
T82D4 010:769.905   Data:  00 00
T82D4 010:769.934 - 0.363ms returns 2 (0x2)
T82D4 010:769.954 JLINK_ReadMemEx(0x20001052, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:769.974   CPU_ReadMem(2 bytes @ 0x20001052)
T82D4 010:770.280   Data:  00 00
T82D4 010:770.309 - 0.362ms returns 2 (0x2)
T82D4 010:770.329 JLINK_ReadMemEx(0x20001054, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:770.349   CPU_ReadMem(2 bytes @ 0x20001054)
T82D4 010:770.654   Data:  00 00
T82D4 010:770.683 - 0.362ms returns 2 (0x2)
T82D4 010:770.702 JLINK_ReadMemEx(0x20001056, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:770.723   CPU_ReadMem(2 bytes @ 0x20001056)
T82D4 010:771.032   Data:  00 00
T82D4 010:771.066 - 0.371ms returns 2 (0x2)
T82D4 010:771.086 JLINK_ReadMemEx(0x20001058, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:771.107   CPU_ReadMem(2 bytes @ 0x20001058)
T82D4 010:771.404   Data:  00 00
T82D4 010:771.434 - 0.356ms returns 2 (0x2)
T82D4 010:771.454 JLINK_ReadMemEx(0x2000105A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:771.475   CPU_ReadMem(2 bytes @ 0x2000105A)
T82D4 010:771.778   Data:  00 00
T82D4 010:771.808 - 0.363ms returns 2 (0x2)
T82D4 010:771.829 JLINK_ReadMemEx(0x2000105C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:771.850   CPU_ReadMem(2 bytes @ 0x2000105C)
T82D4 010:772.158   Data:  00 00
T82D4 010:772.194 - 0.374ms returns 2 (0x2)
T82D4 010:772.214 JLINK_ReadMemEx(0x2000105E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:772.233   CPU_ReadMem(2 bytes @ 0x2000105E)
T82D4 010:772.529   Data:  00 00
T82D4 010:772.558 - 0.352ms returns 2 (0x2)
T82D4 010:772.578 JLINK_ReadMemEx(0x20001060, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:772.599   CPU_ReadMem(2 bytes @ 0x20001060)
T82D4 010:772.946   Data:  00 00
T82D4 010:773.006 - 0.437ms returns 2 (0x2)
T82D4 010:773.028 JLINK_ReadMemEx(0x20001062, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:773.051   CPU_ReadMem(2 bytes @ 0x20001062)
T82D4 010:773.494   Data:  00 00
T82D4 010:773.547 - 0.528ms returns 2 (0x2)
T82D4 010:773.569 JLINK_ReadMemEx(0x20001064, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:773.607   CPU_ReadMem(2 bytes @ 0x20001064)
T82D4 010:774.022   Data:  00 00
T82D4 010:774.074 - 0.513ms returns 2 (0x2)
T82D4 010:774.109 JLINK_ReadMemEx(0x20001066, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:774.130   CPU_ReadMem(2 bytes @ 0x20001066)
T82D4 010:774.431   Data:  00 00
T82D4 010:774.461 - 0.361ms returns 2 (0x2)
T82D4 010:774.481 JLINK_ReadMemEx(0x20001068, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:774.523   CPU_ReadMem(2 bytes @ 0x20001068)
T82D4 010:774.905   Data:  00 00
T82D4 010:774.934 - 0.461ms returns 2 (0x2)
T82D4 010:774.955 JLINK_ReadMemEx(0x2000106A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:774.976   CPU_ReadMem(2 bytes @ 0x2000106A)
T82D4 010:775.274   Data:  00 00
T82D4 010:775.302 - 0.355ms returns 2 (0x2)
T82D4 010:775.322 JLINK_ReadMemEx(0x2000106C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:775.343   CPU_ReadMem(2 bytes @ 0x2000106C)
T82D4 010:775.655   Data:  00 00
T82D4 010:775.685 - 0.371ms returns 2 (0x2)
T82D4 010:775.704 JLINK_ReadMemEx(0x2000106E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:775.725   CPU_ReadMem(2 bytes @ 0x2000106E)
T82D4 010:776.032   Data:  00 00
T82D4 010:776.063 - 0.366ms returns 2 (0x2)
T82D4 010:776.082 JLINK_ReadMemEx(0x20001070, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:776.101   CPU_ReadMem(2 bytes @ 0x20001070)
T82D4 010:776.404   Data:  00 00
T82D4 010:776.433 - 0.359ms returns 2 (0x2)
T82D4 010:776.454 JLINK_ReadMemEx(0x20001072, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:776.474   CPU_ReadMem(2 bytes @ 0x20001072)
T82D4 010:776.779   Data:  00 00
T82D4 010:776.808 - 0.363ms returns 2 (0x2)
T82D4 010:776.828 JLINK_ReadMemEx(0x20001074, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:776.850   CPU_ReadMem(2 bytes @ 0x20001074)
T82D4 010:777.236   Data:  00 00
T82D4 010:777.266 - 0.445ms returns 2 (0x2)
T82D4 010:777.285 JLINK_ReadMemEx(0x20001076, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:777.306   CPU_ReadMem(2 bytes @ 0x20001076)
T82D4 010:777.606   Data:  00 00
T82D4 010:777.636 - 0.359ms returns 2 (0x2)
T82D4 010:777.656 JLINK_ReadMemEx(0x20001078, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:777.677   CPU_ReadMem(2 bytes @ 0x20001078)
T82D4 010:778.009   Data:  00 00
T82D4 010:778.042 - 0.394ms returns 2 (0x2)
T82D4 010:778.063 JLINK_ReadMemEx(0x2000107A, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:778.085   CPU_ReadMem(2 bytes @ 0x2000107A)
T82D4 010:778.380   Data:  00 00
T82D4 010:778.410 - 0.355ms returns 2 (0x2)
T82D4 010:778.429 JLINK_ReadMemEx(0x2000107C, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:778.451   CPU_ReadMem(2 bytes @ 0x2000107C)
T82D4 010:778.754   Data:  00 00
T82D4 010:778.784 - 0.363ms returns 2 (0x2)
T82D4 010:778.804 JLINK_ReadMemEx(0x2000107E, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:778.827   CPU_ReadMem(2 bytes @ 0x2000107E)
T82D4 010:779.136   Data:  00 00
T82D4 010:779.186 - 0.391ms returns 2 (0x2)
T82D4 010:779.208 JLINK_ReadMemEx(0x20001080, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:779.252   CPU_ReadMem(2 bytes @ 0x20001080)
T82D4 010:779.609   Data:  00 00
T82D4 010:779.638 - 0.439ms returns 2 (0x2)
T82D4 010:779.658 JLINK_ReadMemEx(0x20001082, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:779.680   CPU_ReadMem(2 bytes @ 0x20001082)
T82D4 010:780.016   Data:  00 00
T82D4 010:780.074 - 0.431ms returns 2 (0x2)
T82D4 010:780.112 JLINK_ReadMemEx(0x20001084, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:780.142   CPU_ReadMem(2 bytes @ 0x20001084)
T82D4 010:780.515   Data:  00 00
T82D4 010:780.568 - 0.464ms returns 2 (0x2)
T82D4 010:780.589 JLINK_ReadMemEx(0x20001086, 0x2 Bytes, Flags = 0x02000000)
T82D4 010:780.650   CPU_ReadMem(2 bytes @ 0x20001086)
T82D4 010:781.028   Data:  00 00
T82D4 010:781.096 - 0.516ms returns 2 (0x2)
T82D4 010:786.301 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:786.337   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 010:786.658   Data:  00 00 00 00
T82D4 010:786.691 - 0.399ms returns 4 (0x4)
T82D4 010:786.718 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:786.741   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 010:787.060   Data:  00 00 00 00
T82D4 010:787.091 - 0.381ms returns 4 (0x4)
T82D4 010:787.116 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:787.140   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 010:787.442   Data:  38 80 00 20
T82D4 010:787.472 - 0.364ms returns 4 (0x4)
T82D4 010:787.692 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 010:787.723   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 010:788.036   Data:  38 80 00 20
T82D4 010:788.067 - 0.383ms returns 4 (0x4)
T5E80 010:799.899 JLINK_IsHalted()
T5E80 010:800.313 - 0.435ms returns FALSE
T5E80 010:900.793 JLINK_HasError()
T5E80 010:901.004 JLINK_IsHalted()
T5E80 010:901.746 - 0.885ms returns FALSE
T5E80 011:002.544 JLINK_HasError()
T5E80 011:002.685 JLINK_IsHalted()
T5E80 011:003.069 - 0.404ms returns FALSE
T5E80 011:104.260 JLINK_HasError()
T5E80 011:104.462 JLINK_IsHalted()
T5E80 011:105.152 - 0.853ms returns FALSE
T5E80 011:206.008 JLINK_HasError()
T5E80 011:206.097 JLINK_HasError()
T5E80 011:206.118 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 011:206.151   Data:  E5 A9 BD 08
T5E80 011:206.184   Debug reg: DWT_CYCCNT
T5E80 011:206.217 - 0.110ms returns 1 (0x1)
T82D4 011:211.816 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:211.887   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 011:212.503   Data:  00 00 00 00
T82D4 011:212.575 - 0.769ms returns 4 (0x4)
T82D4 011:212.626 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:212.657   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 011:213.111   Data:  00 00 00 00
T82D4 011:213.164 - 0.547ms returns 4 (0x4)
T82D4 011:213.203 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:213.236   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 011:213.987   Data:  38 80 00 20
T82D4 011:214.154 - 0.966ms returns 4 (0x4)
T82D4 011:215.220 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:215.285   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 011:215.783   Data:  38 80 00 20
T82D4 011:215.839 - 0.627ms returns 4 (0x4)
T5E80 011:226.737 JLINK_IsHalted()
T5E80 011:227.316 - 0.608ms returns FALSE
T5E80 011:328.240 JLINK_HasError()
T5E80 011:328.374 JLINK_IsHalted()
T5E80 011:328.862 - 0.508ms returns FALSE
T5E80 011:429.329 JLINK_HasError()
T5E80 011:429.481 JLINK_IsHalted()
T5E80 011:429.856 - 0.388ms returns FALSE
T5E80 011:530.811 JLINK_HasError()
T5E80 011:530.960 JLINK_IsHalted()
T5E80 011:531.348 - 0.403ms returns FALSE
T5E80 011:631.436 JLINK_HasError()
T5E80 011:631.580 JLINK_IsHalted()
T5E80 011:631.948 - 0.382ms returns FALSE
T5E80 011:732.949 JLINK_HasError()
T5E80 011:733.100 JLINK_HasError()
T5E80 011:733.119 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 011:733.152   Data:  E5 A9 BD 08
T5E80 011:733.180   Debug reg: DWT_CYCCNT
T5E80 011:733.204 - 0.093ms returns 1 (0x1)
T82D4 011:737.882 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:737.936   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 011:738.352   Data:  00 00 00 00
T82D4 011:738.405 - 0.531ms returns 4 (0x4)
T82D4 011:738.432 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:738.455   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 011:738.825   Data:  00 00 00 00
T82D4 011:738.904 - 0.481ms returns 4 (0x4)
T82D4 011:738.946 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:738.968   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 011:739.338   Data:  38 80 00 20
T82D4 011:739.394 - 0.477ms returns 4 (0x4)
T82D4 011:739.858 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 011:739.926   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 011:740.393   Data:  38 80 00 20
T82D4 011:740.430 - 0.580ms returns 4 (0x4)
T5E80 011:753.943 JLINK_IsHalted()
T5E80 011:754.355 - 0.428ms returns FALSE
T5E80 011:854.728 JLINK_HasError()
T5E80 011:854.779 JLINK_IsHalted()
T5E80 011:855.204 - 0.444ms returns FALSE
T5E80 011:955.559 JLINK_HasError()
T5E80 011:955.624 JLINK_IsHalted()
T5E80 011:956.119 - 0.539ms returns FALSE
T5E80 012:057.067 JLINK_HasError()
T5E80 012:057.126 JLINK_IsHalted()
T5E80 012:057.593 - 0.485ms returns FALSE
T5E80 012:158.446 JLINK_HasError()
T5E80 012:158.642 JLINK_IsHalted()
T5E80 012:159.406 - 0.864ms returns FALSE
T5E80 012:260.286 JLINK_HasError()
T5E80 012:260.379 JLINK_HasError()
T5E80 012:260.421 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 012:260.454   Data:  E5 A9 BD 08
T5E80 012:260.483   Debug reg: DWT_CYCCNT
T5E80 012:260.511 - 0.099ms returns 1 (0x1)
T82D4 012:265.626 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:265.676   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 012:266.013   Data:  00 00 00 00
T82D4 012:266.046 - 0.429ms returns 4 (0x4)
T82D4 012:266.075 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:266.099   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 012:266.411   Data:  00 00 00 00
T82D4 012:266.448 - 0.383ms returns 4 (0x4)
T82D4 012:266.477 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:266.502   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 012:266.809   Data:  38 80 00 20
T82D4 012:266.842 - 0.373ms returns 4 (0x4)
T82D4 012:267.070 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:267.102   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 012:267.410   Data:  38 80 00 20
T82D4 012:267.443 - 0.382ms returns 4 (0x4)
T5E80 012:279.516 JLINK_IsHalted()
T5E80 012:279.909 - 0.407ms returns FALSE
T5E80 012:380.867 JLINK_HasError()
T5E80 012:380.938 JLINK_IsHalted()
T5E80 012:381.338 - 0.415ms returns FALSE
T5E80 012:481.635 JLINK_HasError()
T5E80 012:481.791 JLINK_IsHalted()
T5E80 012:482.134 - 0.378ms returns FALSE
T5E80 012:582.979 JLINK_HasError()
T5E80 012:583.114 JLINK_IsHalted()
T5E80 012:583.499 - 0.411ms returns FALSE
T5E80 012:684.124 JLINK_HasError()
T5E80 012:684.173 JLINK_IsHalted()
T5E80 012:684.531 - 0.371ms returns FALSE
T5E80 012:785.374 JLINK_HasError()
T5E80 012:785.433 JLINK_HasError()
T5E80 012:785.454 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 012:785.484   Data:  E5 A9 BD 08
T5E80 012:785.513   Debug reg: DWT_CYCCNT
T5E80 012:785.539 - 0.094ms returns 1 (0x1)
T82D4 012:791.108 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:791.165   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 012:791.527   Data:  00 00 00 00
T82D4 012:791.569 - 0.472ms returns 4 (0x4)
T82D4 012:791.600 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:791.625   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 012:792.010   Data:  00 00 00 00
T82D4 012:792.042 - 0.450ms returns 4 (0x4)
T82D4 012:792.068 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:792.092   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 012:792.406   Data:  38 80 00 20
T82D4 012:792.445 - 0.385ms returns 4 (0x4)
T82D4 012:792.690 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 012:792.723   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 012:793.038   Data:  38 80 00 20
T82D4 012:793.077 - 0.396ms returns 4 (0x4)
T5E80 012:804.915 JLINK_IsHalted()
T5E80 012:805.385 - 0.489ms returns FALSE
T5E80 012:905.589 JLINK_HasError()
T5E80 012:905.808 JLINK_IsHalted()
T5E80 012:906.541 - 0.854ms returns FALSE
T5E80 013:007.556 JLINK_HasError()
T5E80 013:007.761 JLINK_IsHalted()
T5E80 013:008.407 - 0.753ms returns FALSE
T5E80 013:109.369 JLINK_HasError()
T5E80 013:109.455 JLINK_IsHalted()
T5E80 013:109.861 - 0.441ms returns FALSE
T5E80 013:210.930 JLINK_HasError()
T5E80 013:211.078 JLINK_IsHalted()
T5E80 013:211.499 - 0.440ms returns FALSE
T5E80 013:312.136 JLINK_HasError()
T5E80 013:312.199 JLINK_HasError()
T5E80 013:312.219 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 013:312.249   Data:  E5 A9 BD 08
T5E80 013:312.277   Debug reg: DWT_CYCCNT
T5E80 013:312.304 - 0.094ms returns 1 (0x1)
T82D4 013:318.080 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:318.143   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 013:318.707   Data:  00 00 00 00
T82D4 013:318.763 - 0.693ms returns 4 (0x4)
T82D4 013:318.805 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:318.834   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 013:319.605   Data:  00 00 00 00
T82D4 013:319.673 - 0.878ms returns 4 (0x4)
T82D4 013:319.724 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:319.753   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 013:320.885   Data:  38 80 00 20
T82D4 013:320.953 - 1.238ms returns 4 (0x4)
T82D4 013:321.344 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:321.391   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 013:321.854   Data:  38 80 00 20
T82D4 013:321.903 - 0.571ms returns 4 (0x4)
T5E80 013:333.745 JLINK_IsHalted()
T5E80 013:334.394 - 0.685ms returns FALSE
T5E80 013:434.599 JLINK_HasError()
T5E80 013:434.802 JLINK_IsHalted()
T5E80 013:435.495 - 0.797ms returns FALSE
T5E80 013:536.330 JLINK_HasError()
T5E80 013:536.512 JLINK_IsHalted()
T5E80 013:537.117 - 0.625ms returns FALSE
T5E80 013:637.316 JLINK_HasError()
T5E80 013:637.403 JLINK_IsHalted()
T5E80 013:637.779 - 0.390ms returns FALSE
T5E80 013:738.045 JLINK_HasError()
T5E80 013:738.103 JLINK_IsHalted()
T5E80 013:738.529 - 0.449ms returns FALSE
T5E80 013:838.630 JLINK_HasError()
T5E80 013:838.827 JLINK_HasError()
T5E80 013:838.923 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 013:839.040   Data:  E5 A9 BD 08
T5E80 013:839.170   Debug reg: DWT_CYCCNT
T5E80 013:839.301 - 0.420ms returns 1 (0x1)
T82D4 013:849.867 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:849.923   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 013:850.288   Data:  00 00 00 00
T82D4 013:850.328 - 0.470ms returns 4 (0x4)
T82D4 013:850.358 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:850.383   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 013:850.719   Data:  00 00 00 00
T82D4 013:850.758 - 0.409ms returns 4 (0x4)
T82D4 013:850.787 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:850.812   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 013:851.162   Data:  38 80 00 20
T82D4 013:851.201 - 0.423ms returns 4 (0x4)
T82D4 013:851.440 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 013:851.472   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 013:851.809   Data:  38 80 00 20
T82D4 013:851.849 - 0.417ms returns 4 (0x4)
T5E80 013:865.072 JLINK_IsHalted()
T5E80 013:865.502 - 0.448ms returns FALSE
T5E80 013:965.771 JLINK_HasError()
T5E80 013:965.828 JLINK_IsHalted()
T5E80 013:966.235 - 0.427ms returns FALSE
T5E80 014:066.543 JLINK_HasError()
T5E80 014:066.697 JLINK_IsHalted()
T5E80 014:067.104 - 0.426ms returns FALSE
T5E80 014:167.457 JLINK_HasError()
T5E80 014:167.515 JLINK_IsHalted()
T5E80 014:167.881 - 0.379ms returns FALSE
T5E80 014:268.786 JLINK_HasError()
T5E80 014:268.997 JLINK_IsHalted()
T5E80 014:269.703 - 0.808ms returns FALSE
T5E80 014:370.552 JLINK_HasError()
T5E80 014:370.743 JLINK_HasError()
T5E80 014:370.836 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 014:370.957   Data:  E5 A9 BD 08
T5E80 014:371.090   Debug reg: DWT_CYCCNT
T5E80 014:371.219 - 0.426ms returns 1 (0x1)
T82D4 014:378.329 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:378.381   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 014:378.741   Data:  00 00 00 00
T82D4 014:378.796 - 0.476ms returns 4 (0x4)
T82D4 014:378.849 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:378.876   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 014:379.266   Data:  00 00 00 00
T82D4 014:379.319 - 0.478ms returns 4 (0x4)
T82D4 014:379.346 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:379.369   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 014:379.703   Data:  38 80 00 20
T82D4 014:379.749 - 0.411ms returns 4 (0x4)
T82D4 014:379.970 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:380.000   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 014:380.391   Data:  38 80 00 20
T82D4 014:380.438 - 0.477ms returns 4 (0x4)
T5E80 014:391.893 JLINK_IsHalted()
T5E80 014:392.339 - 0.467ms returns FALSE
T5E80 014:493.004 JLINK_HasError()
T5E80 014:493.189 JLINK_IsHalted()
T5E80 014:493.703 - 0.538ms returns FALSE
T5E80 014:593.836 JLINK_HasError()
T5E80 014:593.983 JLINK_IsHalted()
T5E80 014:594.347 - 0.378ms returns FALSE
T5E80 014:694.783 JLINK_HasError()
T5E80 014:694.977 JLINK_IsHalted()
T5E80 014:695.463 - 0.506ms returns FALSE
T5E80 014:795.928 JLINK_HasError()
T5E80 014:796.088 JLINK_IsHalted()
T5E80 014:796.464 - 0.390ms returns FALSE
T5E80 014:896.742 JLINK_HasError()
T5E80 014:896.858 JLINK_HasError()
T5E80 014:896.895 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 014:896.947   Data:  E5 A9 BD 08
T5E80 014:896.997   Debug reg: DWT_CYCCNT
T5E80 014:897.027 - 0.141ms returns 1 (0x1)
T82D4 014:902.008 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:902.060   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 014:902.479   Data:  00 00 00 00
T82D4 014:902.517 - 0.517ms returns 4 (0x4)
T82D4 014:902.545 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:902.569   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 014:902.986   Data:  00 00 00 00
T82D4 014:903.024 - 0.487ms returns 4 (0x4)
T82D4 014:903.051 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:903.073   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 014:903.506   Data:  38 80 00 20
T82D4 014:903.542 - 0.500ms returns 4 (0x4)
T82D4 014:903.768 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 014:903.799   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 014:904.232   Data:  38 80 00 20
T82D4 014:904.268 - 0.508ms returns 4 (0x4)
T5E80 014:918.529 JLINK_IsHalted()
T5E80 014:918.937 - 0.424ms returns FALSE
T5E80 015:019.916 JLINK_HasError()
T5E80 015:020.098 JLINK_IsHalted()
T5E80 015:020.772 - 0.779ms returns FALSE
T5E80 015:120.994 JLINK_HasError()
T5E80 015:121.085 JLINK_IsHalted()
T5E80 015:121.578 - 0.514ms returns FALSE
T5E80 015:221.691 JLINK_HasError()
T5E80 015:221.753 JLINK_IsHalted()
T5E80 015:222.294 - 0.560ms returns FALSE
T5E80 015:322.828 JLINK_HasError()
T5E80 015:322.885 JLINK_IsHalted()
T5E80 015:323.251 - 0.384ms returns FALSE
T5E80 015:423.963 JLINK_HasError()
T5E80 015:424.019 JLINK_HasError()
T5E80 015:424.038 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 015:424.065   Data:  E5 A9 BD 08
T5E80 015:424.091   Debug reg: DWT_CYCCNT
T5E80 015:424.118 - 0.088ms returns 1 (0x1)
T82D4 015:429.858 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:429.908   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 015:430.322   Data:  00 00 00 00
T82D4 015:430.359 - 0.509ms returns 4 (0x4)
T82D4 015:430.387 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:430.410   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 015:430.807   Data:  00 00 00 00
T82D4 015:430.844 - 0.466ms returns 4 (0x4)
T82D4 015:430.874 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:430.900   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 015:431.273   Data:  38 80 00 20
T82D4 015:431.309 - 0.443ms returns 4 (0x4)
T82D4 015:431.536 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:431.566   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 015:431.943   Data:  38 80 00 20
T82D4 015:431.980 - 0.451ms returns 4 (0x4)
T5E80 015:445.023 JLINK_IsHalted()
T5E80 015:445.567 - 0.575ms returns FALSE
T5E80 015:545.927 JLINK_HasError()
T5E80 015:546.004 JLINK_IsHalted()
T5E80 015:546.517 - 0.538ms returns FALSE
T5E80 015:646.652 JLINK_HasError()
T5E80 015:646.765 JLINK_IsHalted()
T5E80 015:647.123 - 0.379ms returns FALSE
T5E80 015:748.231 JLINK_HasError()
T5E80 015:748.299 JLINK_IsHalted()
T5E80 015:749.415 - 1.167ms returns FALSE
T5E80 015:850.484 JLINK_HasError()
T5E80 015:850.545 JLINK_IsHalted()
T5E80 015:850.974 - 0.452ms returns FALSE
T5E80 015:951.387 JLINK_HasError()
T5E80 015:951.524 JLINK_HasError()
T5E80 015:951.582 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 015:951.676   Data:  E5 A9 BD 08
T5E80 015:951.740   Debug reg: DWT_CYCCNT
T5E80 015:951.788 - 0.235ms returns 1 (0x1)
T82D4 015:961.829 JLINK_HasError()
T82D4 015:961.892 JLINK_SetBPEx(Addr = 0x08007538, Type = 0xFFFFFFF2)
T82D4 015:961.918   CPU is running
T82D4 015:961.950   CPU is running
T82D4 015:961.997   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 015:962.412   CPU is running
T82D4 015:962.456   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 015:962.780 - 0.902ms returns 0x00000002
T82D4 015:969.045 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:969.090   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 015:969.408   Data:  00 00 00 00
T82D4 015:969.440 - 0.404ms returns 4 (0x4)
T82D4 015:969.469 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:969.494   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 015:969.805   Data:  00 00 00 00
T82D4 015:969.837 - 0.377ms returns 4 (0x4)
T82D4 015:969.863 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:969.887   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 015:970.235   Data:  38 80 00 20
T82D4 015:970.266 - 0.413ms returns 4 (0x4)
T82D4 015:970.491 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 015:970.523   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 015:970.838   Data:  38 80 00 20
T82D4 015:970.881 - 0.399ms returns 4 (0x4)
T5E80 015:983.493 JLINK_IsHalted()
T5E80 015:983.902 - 0.425ms returns FALSE
T5E80 016:084.523 JLINK_HasError()
T5E80 016:084.576 JLINK_IsHalted()
T5E80 016:085.080 - 0.523ms returns FALSE
T5E80 016:185.941 JLINK_HasError()
T5E80 016:185.995 JLINK_IsHalted()
T5E80 016:186.447 - 0.471ms returns FALSE
T5E80 016:286.554 JLINK_HasError()
T5E80 016:286.691 JLINK_IsHalted()
T5E80 016:287.094 - 0.440ms returns FALSE
T5E80 016:388.095 JLINK_HasError()
T5E80 016:388.190 JLINK_IsHalted()
T5E80 016:388.565 - 0.399ms returns FALSE
T5E80 016:488.791 JLINK_HasError()
T5E80 016:488.852 JLINK_HasError()
T5E80 016:488.872 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 016:488.900   Data:  E5 A9 BD 08
T5E80 016:488.927   Debug reg: DWT_CYCCNT
T5E80 016:488.955 - 0.091ms returns 1 (0x1)
T82D4 016:493.739 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 016:493.789   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 016:494.163   Data:  00 00 00 00
T82D4 016:494.195 - 0.465ms returns 4 (0x4)
T82D4 016:494.223 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 016:494.246   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 016:494.585   Data:  00 00 00 00
T82D4 016:494.615 - 0.402ms returns 4 (0x4)
T82D4 016:494.641 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 016:494.662   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 016:495.108   Data:  38 80 00 20
T82D4 016:495.196 - 0.568ms returns 4 (0x4)
T82D4 016:495.564 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 016:495.597   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 016:495.965   Data:  38 80 00 20
T82D4 016:496.009 - 0.454ms returns 4 (0x4)
T5E80 016:508.711 JLINK_IsHalted()
T5E80 016:509.287 - 0.595ms returns FALSE
T5E80 016:609.794 JLINK_HasError()
T5E80 016:609.851 JLINK_IsHalted()
T5E80 016:610.346 - 0.519ms returns FALSE
T5E80 016:711.221 JLINK_HasError()
T5E80 016:711.362 JLINK_IsHalted()
T5E80 016:711.746 - 0.400ms returns FALSE
T82D4 016:764.083 JLINK_HasError()
T82D4 016:764.166 JLINK_ClrBPEx(BPHandle = 0x00000002)
T82D4 016:764.540   CPU is running
T82D4 016:764.580   CPU is running
T82D4 016:764.607   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 016:764.935   CPU is running
T82D4 016:764.971   CPU_WriteMem(4 bytes @ 0xE000200C)
T82D4 016:765.434   CPU is running
T82D4 016:765.465   CPU_WriteMem(4 bytes @ 0xE0002010)
T82D4 016:765.814   CPU is running
T82D4 016:765.845   CPU_WriteMem(4 bytes @ 0xE0002014)
T82D4 016:766.189   CPU is running
T82D4 016:766.220   CPU_WriteMem(4 bytes @ 0xE0002018)
T82D4 016:766.528   CPU is running
T82D4 016:766.557   CPU_WriteMem(4 bytes @ 0xE000201C)
T82D4 016:766.878 - 2.725ms returns 0x00
T5E80 016:812.252 JLINK_HasError()
T5E80 016:812.300 JLINK_IsHalted()
T5E80 016:812.694 - 0.414ms returns FALSE
T5E80 016:913.534 JLINK_HasError()
T5E80 016:913.620 JLINK_IsHalted()
T5E80 016:914.041 - 0.434ms returns FALSE
T5E80 017:014.910 JLINK_HasError()
T5E80 017:014.960 JLINK_HasError()
T5E80 017:014.980 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 017:015.007   Data:  E5 A9 BD 08
T5E80 017:015.033   Debug reg: DWT_CYCCNT
T5E80 017:015.059 - 0.087ms returns 1 (0x1)
T82D4 017:019.700 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:019.740   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 017:020.196   Data:  00 00 00 00
T82D4 017:020.261 - 0.569ms returns 4 (0x4)
T82D4 017:020.308 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:020.369   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 017:020.819   Data:  00 00 00 00
T82D4 017:020.858 - 0.558ms returns 4 (0x4)
T82D4 017:020.885 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:020.909   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 017:021.362   Data:  38 80 00 20
T82D4 017:021.400 - 0.524ms returns 4 (0x4)
T82D4 017:021.653 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:021.715   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 017:022.135   Data:  38 80 00 20
T82D4 017:022.165 - 0.520ms returns 4 (0x4)
T5E80 017:035.137 JLINK_IsHalted()
T5E80 017:035.566 - 0.444ms returns FALSE
T5E80 017:135.950 JLINK_HasError()
T5E80 017:136.134 JLINK_IsHalted()
T5E80 017:136.750 - 0.717ms returns FALSE
T5E80 017:237.010 JLINK_HasError()
T5E80 017:237.232 JLINK_IsHalted()
T5E80 017:237.917 - 0.820ms returns FALSE
T5E80 017:338.811 JLINK_HasError()
T5E80 017:339.080 JLINK_IsHalted()
T5E80 017:339.841 - 0.857ms returns FALSE
T5E80 017:440.846 JLINK_HasError()
T5E80 017:440.978 JLINK_IsHalted()
T5E80 017:441.386 - 0.427ms returns FALSE
T5E80 017:542.210 JLINK_HasError()
T5E80 017:542.269 JLINK_HasError()
T5E80 017:542.288 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 017:542.316   Data:  E5 A9 BD 08
T5E80 017:542.342   Debug reg: DWT_CYCCNT
T5E80 017:542.367 - 0.087ms returns 1 (0x1)
T82D4 017:547.294 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:547.334   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 017:547.744   Data:  00 00 00 00
T82D4 017:547.774 - 0.489ms returns 4 (0x4)
T82D4 017:547.801 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:547.823   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 017:548.184   Data:  00 00 00 00
T82D4 017:548.214 - 0.422ms returns 4 (0x4)
T82D4 017:548.239 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:548.260   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 017:548.622   Data:  38 80 00 20
T82D4 017:548.651 - 0.420ms returns 4 (0x4)
T82D4 017:548.860 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 017:548.888   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 017:549.272   Data:  38 80 00 20
T82D4 017:549.309 - 0.461ms returns 4 (0x4)
T5E80 017:562.412 JLINK_IsHalted()
T5E80 017:563.103 - 0.739ms returns FALSE
T5E80 017:663.225 JLINK_HasError()
T5E80 017:663.296 JLINK_IsHalted()
T5E80 017:663.756 - 0.488ms returns FALSE
T5E80 017:763.864 JLINK_HasError()
T5E80 017:763.945 JLINK_IsHalted()
T5E80 017:764.404 - 0.490ms returns FALSE
T5E80 017:865.316 JLINK_HasError()
T5E80 017:865.524 JLINK_IsHalted()
T5E80 017:866.176 - 0.754ms returns FALSE
T5E80 017:967.108 JLINK_HasError()
T5E80 017:967.252 JLINK_IsHalted()
T5E80 017:967.678 - 0.446ms returns FALSE
T5E80 018:068.610 JLINK_HasError()
T5E80 018:068.677 JLINK_HasError()
T5E80 018:068.699 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 018:068.729   Data:  E5 A9 BD 08
T5E80 018:068.760   Debug reg: DWT_CYCCNT
T5E80 018:068.789 - 0.099ms returns 1 (0x1)
T82D4 018:073.866 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:073.910   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 018:074.258   Data:  00 00 00 00
T82D4 018:074.294 - 0.436ms returns 4 (0x4)
T82D4 018:074.322 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:074.346   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 018:074.748   Data:  00 00 00 00
T82D4 018:074.779 - 0.465ms returns 4 (0x4)
T82D4 018:074.803 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:074.824   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 018:075.248   Data:  38 80 00 20
T82D4 018:075.278 - 0.483ms returns 4 (0x4)
T82D4 018:075.501 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:075.531   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 018:075.924   Data:  38 80 00 20
T82D4 018:075.969 - 0.476ms returns 4 (0x4)
T5E80 018:088.360 JLINK_IsHalted()
T5E80 018:088.936 - 0.596ms returns FALSE
T5E80 018:189.186 JLINK_HasError()
T5E80 018:189.276 JLINK_IsHalted()
T5E80 018:189.796 - 0.561ms returns FALSE
T5E80 018:289.974 JLINK_HasError()
T5E80 018:290.120 JLINK_IsHalted()
T5E80 018:290.504 - 0.396ms returns FALSE
T5E80 018:390.717 JLINK_HasError()
T5E80 018:390.915 JLINK_IsHalted()
T5E80 018:391.528 - 0.634ms returns FALSE
T5E80 018:491.679 JLINK_HasError()
T5E80 018:491.811 JLINK_IsHalted()
T5E80 018:492.222 - 0.425ms returns FALSE
T5E80 018:592.403 JLINK_HasError()
T5E80 018:592.607 JLINK_HasError()
T5E80 018:592.702 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 018:592.818   Data:  E5 A9 BD 08
T5E80 018:592.947   Debug reg: DWT_CYCCNT
T5E80 018:593.079 - 0.419ms returns 1 (0x1)
T82D4 018:612.745 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:612.803   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 018:613.315   Data:  00 00 00 00
T82D4 018:613.363 - 0.627ms returns 4 (0x4)
T82D4 018:613.392 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:613.415   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 018:613.816   Data:  00 00 00 00
T82D4 018:613.847 - 0.462ms returns 4 (0x4)
T82D4 018:613.870 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:613.892   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 018:614.269   Data:  38 80 00 20
T82D4 018:614.299 - 0.437ms returns 4 (0x4)
T82D4 018:614.524 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 018:614.553   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 018:614.919   Data:  38 80 00 20
T82D4 018:614.950 - 0.435ms returns 4 (0x4)
T5E80 018:628.133 JLINK_IsHalted()
T5E80 018:628.698 - 0.606ms returns FALSE
T5E80 018:728.957 JLINK_HasError()
T5E80 018:729.022 JLINK_IsHalted()
T5E80 018:730.144 - 1.171ms returns FALSE
T5E80 018:830.902 JLINK_HasError()
T5E80 018:831.045 JLINK_IsHalted()
T5E80 018:831.431 - 0.405ms returns FALSE
T5E80 018:932.441 JLINK_HasError()
T5E80 018:932.579 JLINK_IsHalted()
T5E80 018:932.969 - 0.404ms returns FALSE
T5E80 019:033.088 JLINK_HasError()
T5E80 019:033.166 JLINK_IsHalted()
T5E80 019:033.853 - 0.741ms returns FALSE
T5E80 019:134.152 JLINK_HasError()
T5E80 019:134.199 JLINK_HasError()
T5E80 019:134.218 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 019:134.245   Data:  E5 A9 BD 08
T5E80 019:134.271   Debug reg: DWT_CYCCNT
T5E80 019:134.306 - 0.095ms returns 1 (0x1)
T82D4 019:140.639 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:140.702   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 019:141.067   Data:  00 00 00 00
T82D4 019:141.114 - 0.483ms returns 4 (0x4)
T82D4 019:141.141 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:141.164   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 019:141.496   Data:  00 00 00 00
T82D4 019:141.542 - 0.409ms returns 4 (0x4)
T82D4 019:141.566 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:141.589   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 019:141.918   Data:  38 80 00 20
T82D4 019:141.965 - 0.407ms returns 4 (0x4)
T82D4 019:142.181 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:142.211   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 019:142.562   Data:  38 80 00 20
T82D4 019:142.608 - 0.436ms returns 4 (0x4)
T5E80 019:155.510 JLINK_IsHalted()
T5E80 019:156.112 - 0.655ms returns FALSE
T5E80 019:257.183 JLINK_HasError()
T5E80 019:257.327 JLINK_IsHalted()
T5E80 019:257.752 - 0.438ms returns FALSE
T5E80 019:358.241 JLINK_HasError()
T5E80 019:358.294 JLINK_IsHalted()
T5E80 019:358.697 - 0.417ms returns FALSE
T5E80 019:459.573 JLINK_HasError()
T5E80 019:459.644 JLINK_IsHalted()
T5E80 019:460.053 - 0.441ms returns FALSE
T5E80 019:561.150 JLINK_HasError()
T5E80 019:561.361 JLINK_IsHalted()
T5E80 019:562.162 - 0.922ms returns FALSE
T5E80 019:662.358 JLINK_HasError()
T5E80 019:662.414 JLINK_HasError()
T5E80 019:662.432 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 019:662.461   Data:  E5 A9 BD 08
T5E80 019:662.487   Debug reg: DWT_CYCCNT
T5E80 019:662.530 - 0.107ms returns 1 (0x1)
T82D4 019:667.969 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:668.040   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 019:668.442   Data:  00 00 00 00
T82D4 019:668.473 - 0.514ms returns 4 (0x4)
T82D4 019:668.502 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:668.524   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 019:668.911   Data:  00 00 00 00
T82D4 019:668.983 - 0.490ms returns 4 (0x4)
T82D4 019:669.010 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:669.085   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 019:669.571   Data:  38 80 00 20
T82D4 019:669.628 - 0.627ms returns 4 (0x4)
T82D4 019:669.976 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 019:670.007   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 019:670.374   Data:  38 80 00 20
T82D4 019:670.462 - 0.496ms returns 4 (0x4)
T5E80 019:681.718 JLINK_IsHalted()
T5E80 019:682.338 - 0.661ms returns FALSE
T5E80 019:783.237 JLINK_HasError()
T5E80 019:783.307 JLINK_IsHalted()
T5E80 019:784.020 - 0.765ms returns FALSE
T5E80 019:885.143 JLINK_HasError()
T5E80 019:885.190 JLINK_IsHalted()
T5E80 019:885.570 - 0.401ms returns FALSE
T5E80 019:985.673 JLINK_HasError()
T5E80 019:985.759 JLINK_IsHalted()
T5E80 019:986.247 - 0.535ms returns FALSE
T5E80 020:086.952 JLINK_HasError()
T5E80 020:087.001 JLINK_IsHalted()
T5E80 020:087.448 - 0.467ms returns FALSE
T5E80 020:188.495 JLINK_HasError()
T5E80 020:188.563 JLINK_HasError()
T5E80 020:188.584 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 020:188.612   Data:  E5 A9 BD 08
T5E80 020:188.640   Debug reg: DWT_CYCCNT
T5E80 020:188.668 - 0.092ms returns 1 (0x1)
T82D4 020:194.364 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:194.416   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 020:194.924   Data:  00 00 00 00
T82D4 020:194.972 - 0.616ms returns 4 (0x4)
T82D4 020:195.003 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:195.030   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 020:195.733   Data:  00 00 00 00
T82D4 020:195.812 - 0.818ms returns 4 (0x4)
T82D4 020:195.863 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:195.896   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 020:196.428   Data:  38 80 00 20
T82D4 020:196.487 - 0.633ms returns 4 (0x4)
T82D4 020:196.880 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:196.930   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 020:197.613   Data:  38 80 00 20
T82D4 020:197.681 - 0.810ms returns 4 (0x4)
T5E80 020:211.149 JLINK_IsHalted()
T5E80 020:211.612 - 0.483ms returns FALSE
T5E80 020:311.860 JLINK_HasError()
T5E80 020:312.060 JLINK_IsHalted()
T5E80 020:312.719 - 0.767ms returns FALSE
T5E80 020:413.362 JLINK_HasError()
T5E80 020:413.419 JLINK_IsHalted()
T5E80 020:413.792 - 0.386ms returns FALSE
T5E80 020:514.501 JLINK_HasError()
T5E80 020:514.567 JLINK_IsHalted()
T5E80 020:514.919 - 0.366ms returns FALSE
T5E80 020:615.492 JLINK_HasError()
T5E80 020:615.552 JLINK_IsHalted()
T5E80 020:615.909 - 0.371ms returns FALSE
T5E80 020:716.177 JLINK_HasError()
T5E80 020:716.232 JLINK_HasError()
T5E80 020:716.251 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 020:716.279   Data:  E5 A9 BD 08
T5E80 020:716.305   Debug reg: DWT_CYCCNT
T5E80 020:716.330 - 0.087ms returns 1 (0x1)
T82D4 020:721.437 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:721.484   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 020:721.956   Data:  00 00 00 00
T82D4 020:721.993 - 0.565ms returns 4 (0x4)
T82D4 020:722.021 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:722.044   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 020:722.443   Data:  00 00 00 00
T82D4 020:722.518 - 0.510ms returns 4 (0x4)
T82D4 020:722.646 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:722.698   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 020:723.113   Data:  38 80 00 20
T82D4 020:723.184 - 0.546ms returns 4 (0x4)
T82D4 020:723.464 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 020:723.496   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 020:723.925   Data:  38 80 00 20
T82D4 020:723.962 - 0.506ms returns 4 (0x4)
T5E80 020:734.732 JLINK_IsHalted()
T5E80 020:735.179 - 0.489ms returns FALSE
T5E80 020:835.275 JLINK_HasError()
T5E80 020:835.363 JLINK_IsHalted()
T5E80 020:835.790 - 0.444ms returns FALSE
T5E80 020:935.907 JLINK_HasError()
T5E80 020:935.965 JLINK_IsHalted()
T5E80 020:936.523 - 0.579ms returns FALSE
T5E80 021:037.043 JLINK_HasError()
T5E80 021:037.246 JLINK_IsHalted()
T5E80 021:038.012 - 0.866ms returns FALSE
T5E80 021:138.731 JLINK_HasError()
T5E80 021:138.809 JLINK_IsHalted()
T5E80 021:139.284 - 0.498ms returns FALSE
T5E80 021:240.083 JLINK_HasError()
T5E80 021:240.288 JLINK_HasError()
T5E80 021:240.383 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 021:240.500   Data:  E5 A9 BD 08
T5E80 021:240.630   Debug reg: DWT_CYCCNT
T5E80 021:240.761 - 0.420ms returns 1 (0x1)
T82D4 021:266.992 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:267.053   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 021:267.426   Data:  00 00 00 00
T82D4 021:267.488 - 0.505ms returns 4 (0x4)
T82D4 021:267.521 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:267.562   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 021:267.916   Data:  00 00 00 00
T82D4 021:267.947 - 0.434ms returns 4 (0x4)
T82D4 021:267.973 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:267.997   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 021:268.431   Data:  38 80 00 20
T82D4 021:268.485 - 0.520ms returns 4 (0x4)
T82D4 021:268.860 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:268.893   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 021:269.259   Data:  38 80 00 20
T82D4 021:269.318 - 0.467ms returns 4 (0x4)
T5E80 021:280.880 JLINK_IsHalted()
T5E80 021:281.387 - 0.526ms returns FALSE
T5E80 021:381.520 JLINK_HasError()
T5E80 021:381.646 JLINK_IsHalted()
T5E80 021:382.050 - 0.418ms returns FALSE
T5E80 021:483.141 JLINK_HasError()
T5E80 021:483.344 JLINK_IsHalted()
T5E80 021:484.030 - 0.806ms returns FALSE
T5E80 021:585.051 JLINK_HasError()
T5E80 021:585.129 JLINK_IsHalted()
T5E80 021:585.513 - 0.399ms returns FALSE
T5E80 021:685.698 JLINK_HasError()
T5E80 021:685.902 JLINK_IsHalted()
T5E80 021:686.588 - 0.788ms returns FALSE
T82D4 021:714.082 JLINK_HasError()
T82D4 021:714.264 JLINK_SetBPEx(Addr = 0x0800753A, Type = 0xFFFFFFF2)
T82D4 021:714.288   CPU is running
T82D4 021:714.317   CPU is running
T82D4 021:714.345   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 021:714.720   CPU is running
T82D4 021:714.776   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 021:715.148 - 0.914ms returns 0x00000003
T5E80 021:787.372 JLINK_HasError()
T5E80 021:787.445 JLINK_HasError()
T5E80 021:787.463 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 021:787.491   Data:  E5 A9 BD 08
T5E80 021:787.517   Debug reg: DWT_CYCCNT
T5E80 021:787.543 - 0.088ms returns 1 (0x1)
T82D4 021:792.302 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:792.343   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 021:792.703   Data:  00 00 00 00
T82D4 021:792.734 - 0.440ms returns 4 (0x4)
T82D4 021:792.760 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:792.783   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 021:793.118   Data:  00 00 00 00
T82D4 021:793.147 - 0.396ms returns 4 (0x4)
T82D4 021:793.173 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:793.193   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 021:793.538   Data:  38 80 00 20
T82D4 021:793.567 - 0.402ms returns 4 (0x4)
T82D4 021:793.778 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 021:793.806   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 021:794.148   Data:  38 80 00 20
T82D4 021:794.177 - 0.407ms returns 4 (0x4)
T5E80 021:805.925 JLINK_IsHalted()
T5E80 021:806.397 - 0.492ms returns FALSE
T5E80 021:907.314 JLINK_HasError()
T5E80 021:907.676 JLINK_IsHalted()
T5E80 021:908.470 - 0.906ms returns FALSE
T5E80 022:008.763 JLINK_HasError()
T5E80 022:008.893 JLINK_IsHalted()
T5E80 022:009.322 - 0.448ms returns FALSE
T5E80 022:109.708 JLINK_HasError()
T5E80 022:109.912 JLINK_IsHalted()
T5E80 022:110.627 - 0.820ms returns FALSE
T5E80 022:210.985 JLINK_HasError()
T5E80 022:211.041 JLINK_IsHalted()
T5E80 022:211.470 - 0.449ms returns FALSE
T5E80 022:311.946 JLINK_HasError()
T5E80 022:312.302 JLINK_HasError()
T5E80 022:312.443 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 022:312.565   Data:  E5 A9 BD 08
T5E80 022:312.709   Debug reg: DWT_CYCCNT
T5E80 022:312.830 - 0.433ms returns 1 (0x1)
T82D4 022:328.449 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:328.502   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 022:328.936   Data:  00 00 00 00
T82D4 022:328.995 - 0.555ms returns 4 (0x4)
T82D4 022:329.056 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:329.095   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 022:329.571   Data:  00 00 00 00
T82D4 022:329.610 - 0.562ms returns 4 (0x4)
T82D4 022:329.637 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:329.661   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 022:330.044   Data:  38 80 00 20
T82D4 022:330.075 - 0.446ms returns 4 (0x4)
T82D4 022:330.299 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:330.329   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 022:330.772   Data:  38 80 00 20
T82D4 022:330.802 - 0.512ms returns 4 (0x4)
T5E80 022:342.477 JLINK_IsHalted()
T5E80 022:343.763 - 1.307ms returns FALSE
T5E80 022:444.768 JLINK_HasError()
T5E80 022:444.982 JLINK_IsHalted()
T5E80 022:445.610 - 0.751ms returns FALSE
T5E80 022:545.938 JLINK_HasError()
T5E80 022:546.156 JLINK_IsHalted()
T5E80 022:546.865 - 0.881ms returns FALSE
T5E80 022:647.855 JLINK_HasError()
T5E80 022:647.916 JLINK_IsHalted()
T5E80 022:648.354 - 0.462ms returns FALSE
T5E80 022:748.542 JLINK_HasError()
T5E80 022:748.735 JLINK_IsHalted()
T5E80 022:749.388 - 0.757ms returns FALSE
T5E80 022:849.814 JLINK_HasError()
T5E80 022:850.021 JLINK_HasError()
T5E80 022:850.116 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 022:850.234   Data:  E5 A9 BD 08
T5E80 022:850.364   Debug reg: DWT_CYCCNT
T5E80 022:850.494 - 0.420ms returns 1 (0x1)
T82D4 022:862.568 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:862.624   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 022:863.080   Data:  00 00 00 00
T82D4 022:863.123 - 0.564ms returns 4 (0x4)
T82D4 022:863.151 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:863.175   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 022:863.596   Data:  00 00 00 00
T82D4 022:863.633 - 0.491ms returns 4 (0x4)
T82D4 022:863.660 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:863.683   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 022:864.185   Data:  38 80 00 20
T82D4 022:864.222 - 0.571ms returns 4 (0x4)
T82D4 022:864.448 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 022:864.478   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 022:864.910   Data:  38 80 00 20
T82D4 022:864.947 - 0.507ms returns 4 (0x4)
T5E80 022:876.164 JLINK_IsHalted()
T5E80 022:876.784 - 0.656ms returns FALSE
T5E80 022:976.952 JLINK_HasError()
T5E80 022:977.150 JLINK_IsHalted()
T5E80 022:977.843 - 0.825ms returns FALSE
T5E80 023:078.432 JLINK_HasError()
T5E80 023:078.645 JLINK_IsHalted()
T5E80 023:079.422 - 0.900ms returns FALSE
T5E80 023:180.247 JLINK_HasError()
T5E80 023:180.303 JLINK_IsHalted()
T5E80 023:180.748 - 0.464ms returns FALSE
T5E80 023:280.923 JLINK_HasError()
T5E80 023:281.119 JLINK_IsHalted()
T5E80 023:281.796 - 0.780ms returns FALSE
T5E80 023:382.345 JLINK_HasError()
T5E80 023:382.549 JLINK_HasError()
T5E80 023:382.645 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 023:382.761   Data:  E5 A9 BD 08
T5E80 023:382.895   Debug reg: DWT_CYCCNT
T5E80 023:383.019 - 0.415ms returns 1 (0x1)
T82D4 023:394.163 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:394.219   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 023:394.578   Data:  00 00 00 00
T82D4 023:394.610 - 0.455ms returns 4 (0x4)
T82D4 023:394.638 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:394.662   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 023:395.051   Data:  00 00 00 00
T82D4 023:395.083 - 0.454ms returns 4 (0x4)
T82D4 023:395.108 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:395.133   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 023:395.437   Data:  38 80 00 20
T82D4 023:395.473 - 0.373ms returns 4 (0x4)
T82D4 023:395.710 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:395.742   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 023:396.058   Data:  38 80 00 20
T82D4 023:396.092 - 0.390ms returns 4 (0x4)
T5E80 023:407.784 JLINK_IsHalted()
T5E80 023:408.185 - 0.416ms returns FALSE
T5E80 023:508.475 JLINK_HasError()
T5E80 023:508.681 JLINK_IsHalted()
T5E80 023:509.544 - 1.005ms returns FALSE
T5E80 023:610.067 JLINK_HasError()
T5E80 023:610.274 JLINK_IsHalted()
T5E80 023:610.975 - 0.812ms returns FALSE
T5E80 023:711.735 JLINK_HasError()
T5E80 023:711.795 JLINK_IsHalted()
T5E80 023:712.204 - 0.428ms returns FALSE
T5E80 023:813.240 JLINK_HasError()
T5E80 023:813.302 JLINK_IsHalted()
T5E80 023:813.714 - 0.432ms returns FALSE
T5E80 023:914.632 JLINK_HasError()
T5E80 023:914.828 JLINK_HasError()
T5E80 023:914.864 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 023:914.893   Data:  E5 A9 BD 08
T5E80 023:914.921   Debug reg: DWT_CYCCNT
T5E80 023:914.947 - 0.092ms returns 1 (0x1)
T82D4 023:920.264 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:920.313   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 023:920.688   Data:  00 00 00 00
T82D4 023:920.736 - 0.480ms returns 4 (0x4)
T82D4 023:920.763 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:920.786   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 023:921.141   Data:  00 00 00 00
T82D4 023:921.187 - 0.433ms returns 4 (0x4)
T82D4 023:921.214 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:921.235   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 023:921.587   Data:  38 80 00 20
T82D4 023:921.618 - 0.413ms returns 4 (0x4)
T82D4 023:921.851 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 023:921.881   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 023:922.236   Data:  38 80 00 20
T82D4 023:922.268 - 0.426ms returns 4 (0x4)
T5E80 023:934.928 JLINK_IsHalted()
T5E80 023:935.451 - 0.564ms returns FALSE
T5E80 024:036.416 JLINK_HasError()
T5E80 024:036.487 JLINK_IsHalted()
T5E80 024:036.905 - 0.438ms returns FALSE
T5E80 024:138.173 JLINK_HasError()
T5E80 024:138.246 JLINK_IsHalted()
T5E80 024:139.366 - 1.174ms returns FALSE
T5E80 024:239.657 JLINK_HasError()
T5E80 024:239.713 JLINK_IsHalted()
T5E80 024:240.151 - 0.457ms returns FALSE
T5E80 024:340.284 JLINK_HasError()
T5E80 024:340.410 JLINK_IsHalted()
T5E80 024:340.797 - 0.400ms returns FALSE
T5E80 024:441.170 JLINK_HasError()
T5E80 024:441.233 JLINK_HasError()
T5E80 024:441.252 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 024:441.279   Data:  E5 A9 BD 08
T5E80 024:441.306   Debug reg: DWT_CYCCNT
T5E80 024:441.332 - 0.088ms returns 1 (0x1)
T82D4 024:446.043 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:446.093   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 024:446.605   Data:  00 00 00 00
T82D4 024:446.645 - 0.610ms returns 4 (0x4)
T82D4 024:446.674 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:446.699   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 024:447.255   Data:  00 00 00 00
T82D4 024:447.307 - 0.641ms returns 4 (0x4)
T82D4 024:447.347 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:447.374   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 024:448.458   Data:  38 80 00 20
T82D4 024:448.528 - 1.190ms returns 4 (0x4)
T82D4 024:449.652 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:449.706   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 024:450.113   Data:  38 80 00 20
T82D4 024:450.154 - 0.510ms returns 4 (0x4)
T5E80 024:461.381 JLINK_IsHalted()
T5E80 024:461.825 - 0.474ms returns FALSE
T5E80 024:562.469 JLINK_HasError()
T5E80 024:562.676 JLINK_IsHalted()
T5E80 024:563.337 - 0.772ms returns FALSE
T5E80 024:663.587 JLINK_HasError()
T5E80 024:663.655 JLINK_IsHalted()
T5E80 024:664.039 - 0.404ms returns FALSE
T5E80 024:764.210 JLINK_HasError()
T5E80 024:764.273 JLINK_IsHalted()
T5E80 024:764.679 - 0.429ms returns FALSE
T5E80 024:865.112 JLINK_HasError()
T5E80 024:865.165 JLINK_IsHalted()
T5E80 024:865.603 - 0.458ms returns FALSE
T5E80 024:966.169 JLINK_HasError()
T5E80 024:966.226 JLINK_HasError()
T5E80 024:966.246 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 024:966.273   Data:  E5 A9 BD 08
T5E80 024:966.301   Debug reg: DWT_CYCCNT
T5E80 024:966.326 - 0.089ms returns 1 (0x1)
T82D4 024:971.149 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:971.198   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 024:971.560   Data:  00 00 00 00
T82D4 024:971.613 - 0.473ms returns 4 (0x4)
T82D4 024:971.647 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:971.671   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 024:972.036   Data:  00 00 00 00
T82D4 024:972.081 - 0.442ms returns 4 (0x4)
T82D4 024:972.105 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:972.131   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 024:972.471   Data:  38 80 00 20
T82D4 024:972.515 - 0.418ms returns 4 (0x4)
T82D4 024:972.726 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 024:972.755   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 024:973.092   Data:  38 80 00 20
T82D4 024:973.138 - 0.420ms returns 4 (0x4)
T5E80 024:986.046 JLINK_IsHalted()
T5E80 024:986.459 - 0.428ms returns FALSE
T5E80 025:086.621 JLINK_HasError()
T5E80 025:086.763 JLINK_IsHalted()
T5E80 025:087.184 - 0.441ms returns FALSE
T5E80 025:187.397 JLINK_HasError()
T5E80 025:187.602 JLINK_IsHalted()
T5E80 025:188.444 - 0.983ms returns FALSE
T5E80 025:288.799 JLINK_HasError()
T5E80 025:288.984 JLINK_IsHalted()
T5E80 025:289.670 - 0.790ms returns FALSE
T5E80 025:390.225 JLINK_HasError()
T5E80 025:390.431 JLINK_IsHalted()
T5E80 025:391.253 - 0.964ms returns FALSE
T5E80 025:492.181 JLINK_HasError()
T5E80 025:492.233 JLINK_HasError()
T5E80 025:492.252 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 025:492.279   Data:  E5 A9 BD 08
T5E80 025:492.307   Debug reg: DWT_CYCCNT
T5E80 025:492.331 - 0.087ms returns 1 (0x1)
T82D4 025:497.536 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 025:497.588   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 025:498.171   Data:  00 00 00 00
T82D4 025:498.220 - 0.693ms returns 4 (0x4)
T82D4 025:498.257 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 025:498.281   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 025:498.835   Data:  00 00 00 00
T82D4 025:498.898 - 0.649ms returns 4 (0x4)
T82D4 025:498.947 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 025:498.977   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 025:499.369   Data:  38 80 00 20
T82D4 025:499.414 - 0.479ms returns 4 (0x4)
T82D4 025:500.050 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 025:500.147   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 025:500.595   Data:  38 80 00 20
T82D4 025:500.634 - 0.592ms returns 4 (0x4)
T5E80 025:513.490 JLINK_IsHalted()
T5E80 025:513.919 - 0.449ms returns FALSE
T5E80 025:614.970 JLINK_HasError()
T5E80 025:615.023 JLINK_IsHalted()
T5E80 025:615.441 - 0.438ms returns FALSE
T5E80 025:715.803 JLINK_HasError()
T5E80 025:715.867 JLINK_IsHalted()
T5E80 025:716.357 - 0.533ms returns FALSE
T5E80 025:817.148 JLINK_HasError()
T5E80 025:817.350 JLINK_IsHalted()
T5E80 025:818.055 - 0.810ms returns FALSE
T5E80 025:918.308 JLINK_HasError()
T5E80 025:918.449 JLINK_IsHalted()
T5E80 025:918.863 - 0.427ms returns FALSE
T5E80 026:019.827 JLINK_HasError()
T5E80 026:019.902 JLINK_HasError()
T5E80 026:019.928 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 026:019.964   Data:  E5 A9 BD 08
T5E80 026:020.000   Debug reg: DWT_CYCCNT
T5E80 026:020.035 - 0.118ms returns 1 (0x1)
T82D4 026:026.770 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:026.823   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 026:027.246   Data:  00 00 00 00
T82D4 026:027.284 - 0.522ms returns 4 (0x4)
T82D4 026:027.312 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:027.339   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 026:027.818   Data:  00 00 00 00
T82D4 026:027.856 - 0.552ms returns 4 (0x4)
T82D4 026:027.882 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:027.905   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 026:028.261   Data:  38 80 00 20
T82D4 026:028.305 - 0.432ms returns 4 (0x4)
T82D4 026:028.663 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:028.694   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 026:029.041   Data:  38 80 00 20
T82D4 026:029.071 - 0.417ms returns 4 (0x4)
T5E80 026:040.577 JLINK_IsHalted()
T5E80 026:041.046 - 0.483ms returns FALSE
T5E80 026:142.186 JLINK_HasError()
T5E80 026:142.245 JLINK_IsHalted()
T5E80 026:142.688 - 0.464ms returns FALSE
T5E80 026:243.359 JLINK_HasError()
T5E80 026:243.413 JLINK_IsHalted()
T5E80 026:244.058 - 0.693ms returns FALSE
T5E80 026:344.368 JLINK_HasError()
T5E80 026:344.443 JLINK_IsHalted()
T5E80 026:344.953 - 0.548ms returns FALSE
T5E80 026:445.263 JLINK_HasError()
T5E80 026:445.337 JLINK_IsHalted()
T5E80 026:445.900 - 0.611ms returns FALSE
T5E80 026:546.223 JLINK_HasError()
T5E80 026:546.403 JLINK_HasError()
T5E80 026:546.436 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 026:546.483   Data:  E5 A9 BD 08
T5E80 026:546.523   Debug reg: DWT_CYCCNT
T5E80 026:546.555 - 0.128ms returns 1 (0x1)
T82D4 026:552.513 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:552.566   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 026:552.981   Data:  00 00 00 00
T82D4 026:553.027 - 0.523ms returns 4 (0x4)
T82D4 026:553.055 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:553.076   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 026:553.475   Data:  00 00 00 00
T82D4 026:553.505 - 0.458ms returns 4 (0x4)
T82D4 026:553.528 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:553.551   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 026:553.966   Data:  38 80 00 20
T82D4 026:553.995 - 0.475ms returns 4 (0x4)
T82D4 026:554.210 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 026:554.239   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 026:554.701   Data:  38 80 00 20
T82D4 026:554.731 - 0.529ms returns 4 (0x4)
T5E80 026:566.082 JLINK_IsHalted()
T5E80 026:566.645 - 0.584ms returns FALSE
T5E80 026:666.911 JLINK_HasError()
T5E80 026:666.986 JLINK_IsHalted()
T5E80 026:667.371 - 0.399ms returns FALSE
T5E80 026:767.622 JLINK_HasError()
T5E80 026:767.814 JLINK_IsHalted()
T5E80 026:768.493 - 0.925ms returns FALSE
T5E80 026:869.418 JLINK_HasError()
T5E80 026:869.475 JLINK_IsHalted()
T5E80 026:869.883 - 0.427ms returns FALSE
T5E80 026:970.145 JLINK_HasError()
T5E80 026:970.216 JLINK_IsHalted()
T5E80 026:970.597 - 0.408ms returns FALSE
T5E80 027:070.800 JLINK_HasError()
T5E80 027:071.002 JLINK_HasError()
T5E80 027:071.094 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 027:071.216   Data:  E5 A9 BD 08
T5E80 027:071.347   Debug reg: DWT_CYCCNT
T5E80 027:071.472 - 0.422ms returns 1 (0x1)
T82D4 027:087.326 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:087.379   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 027:087.780   Data:  00 00 00 00
T82D4 027:087.811 - 0.493ms returns 4 (0x4)
T82D4 027:087.837 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:087.860   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 027:088.271   Data:  00 00 00 00
T82D4 027:088.301 - 0.472ms returns 4 (0x4)
T82D4 027:088.324 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:088.346   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 027:088.740   Data:  38 80 00 20
T82D4 027:088.770 - 0.454ms returns 4 (0x4)
T82D4 027:088.988 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:089.017   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 027:089.352   Data:  38 80 00 20
T82D4 027:089.382 - 0.402ms returns 4 (0x4)
T5E80 027:101.247 JLINK_IsHalted()
T5E80 027:101.686 - 0.458ms returns FALSE
T5E80 027:201.859 JLINK_HasError()
T5E80 027:201.924 JLINK_IsHalted()
T5E80 027:202.376 - 0.471ms returns FALSE
T5E80 027:303.019 JLINK_HasError()
T5E80 027:303.095 JLINK_IsHalted()
T5E80 027:303.447 - 0.377ms returns FALSE
T5E80 027:403.914 JLINK_HasError()
T5E80 027:403.970 JLINK_IsHalted()
T5E80 027:404.344 - 0.395ms returns FALSE
T5E80 027:505.044 JLINK_HasError()
T5E80 027:505.241 JLINK_IsHalted()
T5E80 027:505.947 - 0.827ms returns FALSE
T82D4 027:575.919 JLINK_HasError()
T82D4 027:575.976 JLINK_ClrBPEx(BPHandle = 0x00000003)
T82D4 027:576.558   CPU is running
T82D4 027:576.619   CPU is running
T82D4 027:576.649   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 027:577.319   CPU is running
T82D4 027:577.390   CPU_WriteMem(4 bytes @ 0xE000200C)
T82D4 027:577.804   CPU is running
T82D4 027:577.851   CPU_WriteMem(4 bytes @ 0xE0002010)
T82D4 027:578.292   CPU is running
T82D4 027:578.336   CPU_WriteMem(4 bytes @ 0xE0002014)
T82D4 027:578.739   CPU is running
T82D4 027:578.779   CPU_WriteMem(4 bytes @ 0xE0002018)
T82D4 027:579.137   CPU is running
T82D4 027:579.199   CPU_WriteMem(4 bytes @ 0xE000201C)
T82D4 027:579.563 - 3.608ms returns 0x00
T5E80 027:606.373 JLINK_HasError()
T5E80 027:606.427 JLINK_HasError()
T5E80 027:606.446 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 027:606.473   Data:  E5 A9 BD 08
T5E80 027:606.499   Debug reg: DWT_CYCCNT
T5E80 027:606.525 - 0.087ms returns 1 (0x1)
T82D4 027:612.551 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:612.616   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 027:613.041   Data:  00 00 00 00
T82D4 027:613.097 - 0.555ms returns 4 (0x4)
T82D4 027:613.126 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:613.151   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 027:613.519   Data:  00 00 00 00
T82D4 027:613.566 - 0.448ms returns 4 (0x4)
T82D4 027:613.591 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:613.613   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 027:613.922   Data:  38 80 00 20
T82D4 027:613.954 - 0.373ms returns 4 (0x4)
T82D4 027:614.201 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 027:614.233   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 027:614.555   Data:  38 80 00 20
T82D4 027:614.586 - 0.393ms returns 4 (0x4)
T5E80 027:627.319 JLINK_IsHalted()
T5E80 027:627.776 - 0.479ms returns FALSE
T5E80 027:728.191 JLINK_HasError()
T5E80 027:728.402 JLINK_IsHalted()
T5E80 027:729.158 - 0.876ms returns FALSE
T5E80 027:829.956 JLINK_HasError()
T5E80 027:830.032 JLINK_IsHalted()
T5E80 027:830.448 - 0.435ms returns FALSE
T5E80 027:930.588 JLINK_HasError()
T5E80 027:930.719 JLINK_IsHalted()
T5E80 027:931.103 - 0.397ms returns FALSE
T82D4 027:937.793 JLINK_HasError()
T82D4 027:937.856 JLINK_SetBPEx(Addr = 0x0800753A, Type = 0xFFFFFFF2)
T82D4 027:937.889   CPU is running
T82D4 027:937.928   CPU is running
T82D4 027:937.961   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 027:938.476   CPU is running
T82D4 027:938.517   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 027:938.918 - 1.077ms returns 0x00000004
T5E80 028:031.326 JLINK_HasError()
T5E80 028:031.520 JLINK_IsHalted()
T5E80 028:032.181 - 0.776ms returns FALSE
T5E80 028:133.046 JLINK_HasError()
T5E80 028:133.092 JLINK_HasError()
T5E80 028:133.112 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 028:133.140   Data:  E5 A9 BD 08
T5E80 028:133.166   Debug reg: DWT_CYCCNT
T5E80 028:133.193 - 0.089ms returns 1 (0x1)
T82D4 028:137.881 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:137.934   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 028:138.315   Data:  00 00 00 00
T82D4 028:138.353 - 0.481ms returns 4 (0x4)
T82D4 028:138.381 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:138.407   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 028:138.801   Data:  00 00 00 00
T82D4 028:138.832 - 0.459ms returns 4 (0x4)
T82D4 028:138.856 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:138.877   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 028:139.252   Data:  38 80 00 20
T82D4 028:139.317 - 0.471ms returns 4 (0x4)
T82D4 028:140.245 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:140.279   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 028:140.633   Data:  38 80 00 20
T82D4 028:140.666 - 0.430ms returns 4 (0x4)
T5E80 028:151.809 JLINK_IsHalted()
T5E80 028:152.290 - 0.501ms returns FALSE
T5E80 028:253.064 JLINK_HasError()
T5E80 028:253.132 JLINK_IsHalted()
T5E80 028:253.556 - 0.450ms returns FALSE
T5E80 028:354.612 JLINK_HasError()
T5E80 028:354.802 JLINK_IsHalted()
T5E80 028:355.519 - 0.851ms returns FALSE
T5E80 028:456.783 JLINK_HasError()
T5E80 028:456.903 JLINK_IsHalted()
T5E80 028:457.284 - 0.394ms returns FALSE
T5E80 028:557.452 JLINK_HasError()
T5E80 028:557.516 JLINK_IsHalted()
T5E80 028:558.212 - 0.745ms returns FALSE
T5E80 028:658.609 JLINK_HasError()
T5E80 028:658.671 JLINK_HasError()
T5E80 028:658.690 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 028:658.718   Data:  E5 A9 BD 08
T5E80 028:658.745   Debug reg: DWT_CYCCNT
T5E80 028:658.769 - 0.087ms returns 1 (0x1)
T82D4 028:665.217 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:665.323   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 028:665.689   Data:  00 00 00 00
T82D4 028:665.726 - 0.517ms returns 4 (0x4)
T82D4 028:665.755 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:665.778   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 028:666.111   Data:  00 00 00 00
T82D4 028:666.158 - 0.411ms returns 4 (0x4)
T82D4 028:666.182 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:666.225   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 028:666.529   Data:  38 80 00 20
T82D4 028:666.559 - 0.385ms returns 4 (0x4)
T82D4 028:666.821 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 028:666.849   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 028:667.158   Data:  38 80 00 20
T82D4 028:667.188 - 0.376ms returns 4 (0x4)
T5E80 028:679.573 JLINK_IsHalted()
T5E80 028:680.114 - 0.561ms returns FALSE
T5E80 028:780.424 JLINK_HasError()
T5E80 028:780.485 JLINK_IsHalted()
T5E80 028:781.090 - 0.652ms returns FALSE
T5E80 028:881.900 JLINK_HasError()
T5E80 028:882.053 JLINK_IsHalted()
T5E80 028:882.466 - 0.433ms returns FALSE
T5E80 028:983.007 JLINK_HasError()
T5E80 028:983.069 JLINK_IsHalted()
T5E80 028:983.432 - 0.380ms returns FALSE
T5E80 029:083.526 JLINK_HasError()
T5E80 029:083.575 JLINK_IsHalted()
T5E80 029:084.072 - 0.516ms returns FALSE
T5E80 029:184.257 JLINK_HasError()
T5E80 029:184.348 JLINK_HasError()
T5E80 029:184.368 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 029:184.394   Data:  E5 A9 BD 08
T5E80 029:184.422   Debug reg: DWT_CYCCNT
T5E80 029:184.447 - 0.087ms returns 1 (0x1)
T82D4 029:189.790 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:189.832   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 029:190.217   Data:  00 00 00 00
T82D4 029:190.252 - 0.470ms returns 4 (0x4)
T82D4 029:190.280 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:190.304   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 029:190.698   Data:  00 00 00 00
T82D4 029:190.729 - 0.456ms returns 4 (0x4)
T82D4 029:190.752 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:190.773   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 029:191.166   Data:  38 80 00 20
T82D4 029:191.270 - 0.531ms returns 4 (0x4)
T82D4 029:191.636 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:191.667   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 029:192.043   Data:  38 80 00 20
T82D4 029:192.089 - 0.461ms returns 4 (0x4)
T5E80 029:204.081 JLINK_IsHalted()
T5E80 029:204.516 - 0.455ms returns FALSE
T5E80 029:304.885 JLINK_HasError()
T5E80 029:304.929 JLINK_IsHalted()
T5E80 029:305.363 - 0.453ms returns FALSE
T5E80 029:406.294 JLINK_HasError()
T5E80 029:406.345 JLINK_IsHalted()
T5E80 029:406.956 - 0.656ms returns FALSE
T5E80 029:507.992 JLINK_HasError()
T5E80 029:508.139 JLINK_IsHalted()
T5E80 029:508.480 - 0.354ms returns FALSE
T5E80 029:609.370 JLINK_HasError()
T5E80 029:609.428 JLINK_IsHalted()
T5E80 029:609.810 - 0.419ms returns FALSE
T82D4 029:689.628 JLINK_ReadMemEx(0x0800753A, 0x2 Bytes, Flags = 0x02000000)
T82D4 029:689.687   CPU_ReadMem(2 bytes @ 0x0800753A)
T82D4 029:690.064   Data:  00 20
T82D4 029:690.110 - 0.490ms returns 2 (0x2)
T82D4 029:690.130 JLINK_ReadMemEx(0x0800753C, 0x2 Bytes, Flags = 0x02000000)
T82D4 029:690.150   CPU_ReadMem(2 bytes @ 0x0800753C)
T82D4 029:690.487   Data:  01 90
T82D4 029:690.535 - 0.413ms returns 2 (0x2)
T82D4 029:690.559 JLINK_ReadMemEx(0x0800753C, 0x2 Bytes, Flags = 0x02000000)
T82D4 029:690.581   CPU_ReadMem(2 bytes @ 0x0800753C)
T82D4 029:690.884   Data:  01 90
T82D4 029:690.929 - 0.379ms returns 2 (0x2)
T82D4 029:690.950 JLINK_ReadMemEx(0x0800753E, 0x2 Bytes, Flags = 0x02000000)
T82D4 029:690.972   CPU_ReadMem(2 bytes @ 0x0800753E)
T82D4 029:691.281   Data:  FD F7
T82D4 029:691.316 - 0.374ms returns 2 (0x2)
T5E80 029:710.712 JLINK_HasError()
T5E80 029:710.781 JLINK_HasError()
T5E80 029:710.800 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 029:710.829   Data:  E5 A9 BD 08
T5E80 029:710.857   Debug reg: DWT_CYCCNT
T5E80 029:710.882 - 0.091ms returns 1 (0x1)
T82D4 029:716.413 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:716.473   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 029:716.892   Data:  00 00 00 00
T82D4 029:716.931 - 0.527ms returns 4 (0x4)
T82D4 029:716.961 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:716.984   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 029:717.391   Data:  00 00 00 00
T82D4 029:717.429 - 0.478ms returns 4 (0x4)
T82D4 029:717.458 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:717.481   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 029:717.883   Data:  38 80 00 20
T82D4 029:717.921 - 0.472ms returns 4 (0x4)
T82D4 029:718.158 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 029:718.188   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 029:718.570   Data:  38 80 00 20
T82D4 029:718.608 - 0.458ms returns 4 (0x4)
T5E80 029:732.080 JLINK_IsHalted()
T5E80 029:732.709 - 0.675ms returns FALSE
T5E80 029:833.233 JLINK_HasError()
T5E80 029:833.294 JLINK_IsHalted()
T5E80 029:833.696 - 0.424ms returns FALSE
T5E80 029:934.261 JLINK_HasError()
T5E80 029:934.410 JLINK_IsHalted()
T5E80 029:934.862 - 0.473ms returns FALSE
T5E80 030:035.842 JLINK_HasError()
T5E80 030:036.034 JLINK_IsHalted()
T5E80 030:036.653 - 0.719ms returns FALSE
T5E80 030:137.184 JLINK_HasError()
T5E80 030:137.253 JLINK_IsHalted()
T5E80 030:138.361 - 1.159ms returns FALSE
T5E80 030:238.610 JLINK_HasError()
T5E80 030:238.665 JLINK_HasError()
T5E80 030:238.685 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 030:238.710   Data:  E5 A9 BD 08
T5E80 030:238.738   Debug reg: DWT_CYCCNT
T5E80 030:238.762 - 0.085ms returns 1 (0x1)
T82D4 030:243.399 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:243.438   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 030:243.875   Data:  00 00 00 00
T82D4 030:243.912 - 0.522ms returns 4 (0x4)
T82D4 030:243.940 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:243.962   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 030:244.391   Data:  00 00 00 00
T82D4 030:244.428 - 0.496ms returns 4 (0x4)
T82D4 030:244.454 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:244.477   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 030:244.914   Data:  38 80 00 20
T82D4 030:244.950 - 0.504ms returns 4 (0x4)
T82D4 030:245.175 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:245.205   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 030:245.667   Data:  38 80 00 20
T82D4 030:245.738 - 0.571ms returns 4 (0x4)
T5E80 030:258.221 JLINK_IsHalted()
T5E80 030:258.646 - 0.444ms returns FALSE
T5E80 030:358.796 JLINK_HasError()
T5E80 030:358.914 JLINK_IsHalted()
T5E80 030:359.332 - 0.437ms returns FALSE
T5E80 030:460.250 JLINK_HasError()
T5E80 030:460.301 JLINK_IsHalted()
T5E80 030:460.695 - 0.414ms returns FALSE
T5E80 030:561.101 JLINK_HasError()
T5E80 030:561.311 JLINK_IsHalted()
T5E80 030:561.956 - 0.774ms returns FALSE
T5E80 030:663.049 JLINK_HasError()
T5E80 030:663.179 JLINK_IsHalted()
T5E80 030:663.533 - 0.378ms returns FALSE
T5E80 030:763.975 JLINK_HasError()
T5E80 030:764.107 JLINK_HasError()
T5E80 030:764.141 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 030:764.168   Data:  E5 A9 BD 08
T5E80 030:764.194   Debug reg: DWT_CYCCNT
T5E80 030:764.220 - 0.087ms returns 1 (0x1)
T82D4 030:770.412 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:770.464   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 030:770.881   Data:  00 00 00 00
T82D4 030:770.935 - 0.531ms returns 4 (0x4)
T82D4 030:770.963 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:770.986   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 030:771.367   Data:  00 00 00 00
T82D4 030:771.404 - 0.449ms returns 4 (0x4)
T82D4 030:771.432 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:771.455   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 030:771.849   Data:  38 80 00 20
T82D4 030:771.924 - 0.514ms returns 4 (0x4)
T82D4 030:772.165 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 030:772.194   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 030:772.692   Data:  38 80 00 20
T82D4 030:772.729 - 0.572ms returns 4 (0x4)
T5E80 030:785.496 JLINK_IsHalted()
T5E80 030:785.916 - 0.441ms returns FALSE
T5E80 030:886.391 JLINK_HasError()
T5E80 030:886.453 JLINK_IsHalted()
T5E80 030:886.876 - 0.444ms returns FALSE
T5E80 030:987.647 JLINK_HasError()
T5E80 030:987.747 JLINK_IsHalted()
T5E80 030:988.159 - 0.437ms returns FALSE
T5E80 031:089.286 JLINK_HasError()
T5E80 031:089.480 JLINK_IsHalted()
T5E80 031:090.160 - 0.779ms returns FALSE
T5E80 031:190.423 JLINK_HasError()
T5E80 031:190.558 JLINK_IsHalted()
T5E80 031:190.972 - 0.427ms returns FALSE
T5E80 031:291.036 JLINK_HasError()
T5E80 031:291.092 JLINK_HasError()
T5E80 031:291.111 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 031:291.137   Data:  E5 A9 BD 08
T5E80 031:291.164   Debug reg: DWT_CYCCNT
T5E80 031:291.188 - 0.085ms returns 1 (0x1)
T82D4 031:296.498 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:296.551   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 031:296.952   Data:  00 00 00 00
T82D4 031:297.012 - 0.538ms returns 4 (0x4)
T82D4 031:297.088 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:297.125   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 031:297.561   Data:  00 00 00 00
T82D4 031:297.627 - 0.567ms returns 4 (0x4)
T82D4 031:297.675 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:297.731   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 031:298.195   Data:  38 80 00 20
T82D4 031:298.234 - 0.567ms returns 4 (0x4)
T82D4 031:298.954 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:298.991   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 031:299.391   Data:  38 80 00 20
T82D4 031:299.422 - 0.476ms returns 4 (0x4)
T5E80 031:310.265 JLINK_IsHalted()
T5E80 031:310.731 - 0.541ms returns FALSE
T5E80 031:411.066 JLINK_HasError()
T5E80 031:411.217 JLINK_IsHalted()
T5E80 031:411.597 - 0.394ms returns FALSE
T5E80 031:512.195 JLINK_HasError()
T5E80 031:512.307 JLINK_IsHalted()
T5E80 031:512.683 - 0.399ms returns FALSE
T5E80 031:612.955 JLINK_HasError()
T5E80 031:613.122 JLINK_IsHalted()
T5E80 031:613.470 - 0.370ms returns FALSE
T5E80 031:713.818 JLINK_HasError()
T5E80 031:713.984 JLINK_IsHalted()
T5E80 031:714.385 - 0.463ms returns FALSE
T5E80 031:815.258 JLINK_HasError()
T5E80 031:815.320 JLINK_HasError()
T5E80 031:815.339 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 031:815.366   Data:  E5 A9 BD 08
T5E80 031:815.393   Debug reg: DWT_CYCCNT
T5E80 031:815.420 - 0.089ms returns 1 (0x1)
T82D4 031:820.254 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:820.295   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 031:820.631   Data:  00 00 00 00
T82D4 031:820.662 - 0.416ms returns 4 (0x4)
T82D4 031:820.689 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:820.712   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 031:821.034   Data:  00 00 00 00
T82D4 031:821.064 - 0.383ms returns 4 (0x4)
T82D4 031:821.088 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:821.110   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 031:821.429   Data:  38 80 00 20
T82D4 031:821.459 - 0.379ms returns 4 (0x4)
T82D4 031:821.673 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 031:821.702   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 031:822.009   Data:  38 80 00 20
T82D4 031:822.040 - 0.376ms returns 4 (0x4)
T5E80 031:833.634 JLINK_IsHalted()
T5E80 031:834.072 - 0.455ms returns FALSE
T5E80 031:935.017 JLINK_HasError()
T5E80 031:935.222 JLINK_IsHalted()
T5E80 031:935.692 - 0.501ms returns FALSE
T5E80 032:035.953 JLINK_HasError()
T5E80 032:036.011 JLINK_IsHalted()
T5E80 032:036.387 - 0.389ms returns FALSE
T5E80 032:137.269 JLINK_HasError()
T5E80 032:137.468 JLINK_IsHalted()
T5E80 032:138.193 - 0.823ms returns FALSE
T5E80 032:238.682 JLINK_HasError()
T5E80 032:238.888 JLINK_IsHalted()
T5E80 032:239.710 - 0.966ms returns FALSE
T5E80 032:340.550 JLINK_HasError()
T5E80 032:340.619 JLINK_HasError()
T5E80 032:340.646 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 032:340.675   Data:  E5 A9 BD 08
T5E80 032:340.703   Debug reg: DWT_CYCCNT
T5E80 032:340.729 - 0.092ms returns 1 (0x1)
T82D4 032:346.135 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:346.187   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 032:346.566   Data:  00 00 00 00
T82D4 032:346.605 - 0.479ms returns 4 (0x4)
T82D4 032:346.639 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:346.666   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 032:347.025   Data:  00 00 00 00
T82D4 032:347.079 - 0.449ms returns 4 (0x4)
T82D4 032:347.112 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:347.137   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 032:347.485   Data:  38 80 00 20
T82D4 032:347.527 - 0.424ms returns 4 (0x4)
T82D4 032:347.880 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:347.927   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 032:348.352   Data:  38 80 00 20
T82D4 032:348.404 - 0.535ms returns 4 (0x4)
T5E80 032:360.091 JLINK_IsHalted()
T5E80 032:360.463 - 0.391ms returns FALSE
T82D4 032:423.151 JLINK_ReadMemEx(0x0800753C, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:423.215   CPU_ReadMem(2 bytes @ 0x0800753C)
T82D4 032:423.583   Data:  01 90
T82D4 032:423.624 - 0.481ms returns 2 (0x2)
T82D4 032:423.645 JLINK_ReadMemEx(0x0800753E, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:423.665   CPU_ReadMem(2 bytes @ 0x0800753E)
T82D4 032:424.049   Data:  FD F7
T82D4 032:424.080 - 0.444ms returns 2 (0x2)
T82D4 032:437.492 JLINK_ReadMemEx(0x0800753E, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:437.545   CPU_ReadMem(2 bytes @ 0x0800753E)
T82D4 032:437.881   Data:  FD F7
T82D4 032:437.914 - 0.430ms returns 2 (0x2)
T82D4 032:437.935 JLINK_ReadMemEx(0x08007540, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:437.955   CPU_ReadMem(2 bytes @ 0x08007540)
T82D4 032:438.280   Data:  0D FE
T82D4 032:438.311 - 0.384ms returns 2 (0x2)
T5E80 032:460.557 JLINK_HasError()
T5E80 032:460.691 JLINK_IsHalted()
T5E80 032:461.033 - 0.356ms returns FALSE
T82D4 032:495.896 JLINK_ReadMemEx(0x08007542, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:495.957   CPU_ReadMem(2 bytes @ 0x08007542)
T82D4 032:496.339   Data:  00 20
T82D4 032:496.419 - 0.532ms returns 2 (0x2)
T82D4 032:496.441 JLINK_ReadMemEx(0x08007544, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:496.478   CPU_ReadMem(2 bytes @ 0x08007544)
T82D4 032:496.841   Data:  00 90
T82D4 032:496.897 - 0.465ms returns 2 (0x2)
T82D4 032:500.936 JLINK_ReadMemEx(0x08007544, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:500.972   CPU_ReadMem(2 bytes @ 0x08007544)
T82D4 032:501.339   Data:  00 90
T82D4 032:501.385 - 0.457ms returns 2 (0x2)
T82D4 032:501.404 JLINK_ReadMemEx(0x08007546, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:501.423   CPU_ReadMem(2 bytes @ 0x08007546)
T82D4 032:501.753   Data:  05 E0
T82D4 032:501.799 - 0.403ms returns 2 (0x2)
T82D4 032:501.823 JLINK_ReadMemEx(0x08007546, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:501.843   CPU_ReadMem(2 bytes @ 0x08007546)
T82D4 032:502.188   Data:  05 E0
T82D4 032:502.220 - 0.405ms returns 2 (0x2)
T82D4 032:502.239 JLINK_ReadMemEx(0x08007548, 0x2 Bytes, Flags = 0x02000000)
T82D4 032:502.259   CPU_ReadMem(2 bytes @ 0x08007548)
T82D4 032:502.577   Data:  01 98
T82D4 032:502.608 - 0.376ms returns 2 (0x2)
T5E80 032:562.032 JLINK_HasError()
T5E80 032:562.133 JLINK_IsHalted()
T5E80 032:562.584 - 0.474ms returns FALSE
T5E80 032:662.851 JLINK_HasError()
T5E80 032:663.003 JLINK_IsHalted()
T5E80 032:663.479 - 0.516ms returns FALSE
T5E80 032:764.555 JLINK_HasError()
T5E80 032:764.617 JLINK_IsHalted()
T5E80 032:765.040 - 0.442ms returns FALSE
T5E80 032:865.687 JLINK_HasError()
T5E80 032:865.854 JLINK_HasError()
T5E80 032:865.873 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 032:865.903   Data:  E5 A9 BD 08
T5E80 032:865.956   Debug reg: DWT_CYCCNT
T5E80 032:866.019 - 0.154ms returns 1 (0x1)
T82D4 032:870.969 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:871.006   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 032:871.396   Data:  00 00 00 00
T82D4 032:871.432 - 0.482ms returns 4 (0x4)
T82D4 032:871.470 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:871.492   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 032:871.915   Data:  00 00 00 00
T82D4 032:871.966 - 0.506ms returns 4 (0x4)
T82D4 032:872.008 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:872.030   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 032:872.483   Data:  38 80 00 20
T82D4 032:872.551 - 0.552ms returns 4 (0x4)
T82D4 032:872.818 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 032:872.850   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 032:873.258   Data:  38 80 00 20
T82D4 032:873.290 - 0.480ms returns 4 (0x4)
T5E80 032:885.580 JLINK_IsHalted()
T5E80 032:886.058 - 0.499ms returns FALSE
T5E80 032:986.181 JLINK_HasError()
T5E80 032:986.324 JLINK_IsHalted()
T5E80 032:986.693 - 0.382ms returns FALSE
T5E80 033:087.590 JLINK_HasError()
T5E80 033:087.685 JLINK_IsHalted()
T5E80 033:088.083 - 0.414ms returns FALSE
T5E80 033:188.211 JLINK_HasError()
T5E80 033:188.274 JLINK_IsHalted()
T5E80 033:188.618 - 0.360ms returns FALSE
T5E80 033:288.942 JLINK_HasError()
T5E80 033:288.999 JLINK_IsHalted()
T5E80 033:289.346 - 0.361ms returns FALSE
T5E80 033:389.426 JLINK_HasError()
T5E80 033:389.643 JLINK_HasError()
T5E80 033:389.747 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 033:389.861   Data:  E5 A9 BD 08
T5E80 033:389.989   Debug reg: DWT_CYCCNT
T5E80 033:390.120 - 0.416ms returns 1 (0x1)
T82D4 033:402.289 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:402.349   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 033:403.000   Data:  00 00 00 00
T82D4 033:403.069 - 0.790ms returns 4 (0x4)
T82D4 033:403.122 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:403.154   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 033:403.825   Data:  00 00 00 00
T82D4 033:403.895 - 0.782ms returns 4 (0x4)
T82D4 033:403.942 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:403.969   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 033:404.627   Data:  38 80 00 20
T82D4 033:404.694 - 0.762ms returns 4 (0x4)
T82D4 033:405.071 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:405.116   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 033:405.784   Data:  38 80 00 20
T82D4 033:405.852 - 0.790ms returns 4 (0x4)
T5E80 033:418.643 JLINK_IsHalted()
T5E80 033:419.089 - 0.483ms returns FALSE
T5E80 033:519.475 JLINK_HasError()
T5E80 033:519.602 JLINK_IsHalted()
T5E80 033:520.119 - 0.563ms returns FALSE
T5E80 033:620.261 JLINK_HasError()
T5E80 033:620.316 JLINK_IsHalted()
T5E80 033:620.694 - 0.398ms returns FALSE
T5E80 033:721.913 JLINK_HasError()
T5E80 033:722.225 JLINK_IsHalted()
T5E80 033:722.990 - 0.933ms returns FALSE
T5E80 033:823.729 JLINK_HasError()
T5E80 033:823.781 JLINK_IsHalted()
T5E80 033:824.191 - 0.422ms returns FALSE
T5E80 033:924.499 JLINK_HasError()
T5E80 033:924.654 JLINK_HasError()
T5E80 033:924.674 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 033:924.715   Data:  E5 A9 BD 08
T5E80 033:924.756   Debug reg: DWT_CYCCNT
T5E80 033:924.782 - 0.116ms returns 1 (0x1)
T82D4 033:930.425 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:930.551   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 033:931.004   Data:  00 00 00 00
T82D4 033:931.043 - 0.627ms returns 4 (0x4)
T82D4 033:931.074 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:931.097   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 033:931.530   Data:  00 00 00 00
T82D4 033:931.567 - 0.501ms returns 4 (0x4)
T82D4 033:931.679 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:931.714   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 033:932.152   Data:  38 80 00 20
T82D4 033:932.189 - 0.519ms returns 4 (0x4)
T82D4 033:932.418 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 033:932.448   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 033:932.843   Data:  38 80 00 20
T82D4 033:932.881 - 0.471ms returns 4 (0x4)
T5E80 033:946.857 JLINK_IsHalted()
T5E80 033:947.487 - 0.661ms returns FALSE
T5E80 034:047.627 JLINK_HasError()
T5E80 034:047.770 JLINK_IsHalted()
T5E80 034:048.150 - 0.394ms returns FALSE
T5E80 034:148.901 JLINK_HasError()
T5E80 034:149.099 JLINK_IsHalted()
T5E80 034:149.818 - 0.837ms returns FALSE
T5E80 034:250.209 JLINK_HasError()
T5E80 034:250.264 JLINK_IsHalted()
T5E80 034:250.671 - 0.443ms returns FALSE
T5E80 034:350.987 JLINK_HasError()
T5E80 034:351.183 JLINK_IsHalted()
T5E80 034:351.878 - 0.805ms returns FALSE
T5E80 034:453.168 JLINK_HasError()
T5E80 034:453.258 JLINK_HasError()
T5E80 034:453.276 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 034:453.303   Data:  E5 A9 BD 08
T5E80 034:453.331   Debug reg: DWT_CYCCNT
T5E80 034:453.399 - 0.132ms returns 1 (0x1)
T82D4 034:458.894 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:458.934   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 034:459.284   Data:  00 00 00 00
T82D4 034:459.331 - 0.446ms returns 4 (0x4)
T82D4 034:459.358 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:459.379   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 034:459.714   Data:  00 00 00 00
T82D4 034:459.781 - 0.432ms returns 4 (0x4)
T82D4 034:459.823 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:459.844   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 034:460.186   Data:  38 80 00 20
T82D4 034:460.230 - 0.415ms returns 4 (0x4)
T82D4 034:460.444 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:460.473   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 034:460.807   Data:  38 80 00 20
T82D4 034:460.838 - 0.402ms returns 4 (0x4)
T5E80 034:473.501 JLINK_IsHalted()
T5E80 034:473.972 - 0.493ms returns FALSE
T5E80 034:575.166 JLINK_HasError()
T5E80 034:575.373 JLINK_IsHalted()
T5E80 034:576.024 - 0.750ms returns FALSE
T5E80 034:676.300 JLINK_HasError()
T5E80 034:676.440 JLINK_IsHalted()
T5E80 034:676.842 - 0.415ms returns FALSE
T5E80 034:777.106 JLINK_HasError()
T5E80 034:777.250 JLINK_IsHalted()
T5E80 034:777.648 - 0.411ms returns FALSE
T5E80 034:877.942 JLINK_HasError()
T5E80 034:878.090 JLINK_IsHalted()
T5E80 034:878.510 - 0.445ms returns FALSE
T5E80 034:978.830 JLINK_HasError()
T5E80 034:978.990 JLINK_HasError()
T5E80 034:979.025 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 034:979.061   Data:  E5 A9 BD 08
T5E80 034:979.091   Debug reg: DWT_CYCCNT
T5E80 034:979.121 - 0.105ms returns 1 (0x1)
T82D4 034:983.944 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:983.983   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 034:984.541   Data:  00 00 00 00
T82D4 034:984.587 - 0.652ms returns 4 (0x4)
T82D4 034:984.631 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:984.658   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 034:985.080   Data:  00 00 00 00
T82D4 034:985.117 - 0.495ms returns 4 (0x4)
T82D4 034:985.147 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:985.174   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 034:985.608   Data:  38 80 00 20
T82D4 034:985.646 - 0.507ms returns 4 (0x4)
T82D4 034:985.974 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 034:986.003   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 034:986.431   Data:  38 80 00 20
T82D4 034:986.469 - 0.504ms returns 4 (0x4)
T5E80 034:999.260 JLINK_IsHalted()
T5E80 034:999.716 - 0.476ms returns FALSE
T5E80 035:100.699 JLINK_HasError()
T5E80 035:100.750 JLINK_IsHalted()
T5E80 035:101.131 - 0.401ms returns FALSE
T5E80 035:201.591 JLINK_HasError()
T5E80 035:201.733 JLINK_IsHalted()
T5E80 035:202.122 - 0.402ms returns FALSE
T5E80 035:302.971 JLINK_HasError()
T5E80 035:303.118 JLINK_IsHalted()
T5E80 035:303.544 - 0.446ms returns FALSE
T5E80 035:403.595 JLINK_HasError()
T5E80 035:403.656 JLINK_IsHalted()
T5E80 035:404.041 - 0.398ms returns FALSE
T5E80 035:504.996 JLINK_HasError()
T5E80 035:505.055 JLINK_HasError()
T5E80 035:505.075 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 035:505.101   Data:  E5 A9 BD 08
T5E80 035:505.128   Debug reg: DWT_CYCCNT
T5E80 035:505.153 - 0.086ms returns 1 (0x1)
T82D4 035:511.038 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 035:511.082   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 035:511.487   Data:  00 00 00 00
T82D4 035:511.546 - 0.539ms returns 4 (0x4)
T82D4 035:511.597 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 035:511.637   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 035:512.071   Data:  00 00 00 00
T82D4 035:512.140 - 0.570ms returns 4 (0x4)
T82D4 035:512.212 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 035:512.262   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 035:512.757   Data:  38 80 00 20
T82D4 035:512.818 - 0.634ms returns 4 (0x4)
T82D4 035:513.165 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 035:513.196   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 035:513.632   Data:  38 80 00 20
T82D4 035:513.667 - 0.510ms returns 4 (0x4)
T5E80 035:524.802 JLINK_IsHalted()
T5E80 035:525.291 - 0.509ms returns FALSE
T5E80 035:625.427 JLINK_HasError()
T5E80 035:625.483 JLINK_IsHalted()
T5E80 035:625.887 - 0.416ms returns FALSE
T5E80 035:726.703 JLINK_HasError()
T5E80 035:726.788 JLINK_IsHalted()
T5E80 035:727.154 - 0.392ms returns FALSE
T5E80 035:827.290 JLINK_HasError()
T5E80 035:827.407 JLINK_IsHalted()
T5E80 035:827.773 - 0.387ms returns FALSE
T5E80 035:928.421 JLINK_HasError()
T5E80 035:928.530 JLINK_IsHalted()
T5E80 035:929.015 - 0.507ms returns FALSE
T5E80 036:030.116 JLINK_HasError()
T5E80 036:030.172 JLINK_HasError()
T5E80 036:030.220 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T5E80 036:030.247   Data:  E5 A9 BD 08
T5E80 036:030.273   Debug reg: DWT_CYCCNT
T5E80 036:030.299 - 0.087ms returns 1 (0x1)
T82D4 036:036.173 JLINK_ReadMemEx(0x2000057C, 0x4 Bytes, Flags = 0x02000000)
T82D4 036:036.229   CPU_ReadMem(4 bytes @ 0x2000057C)
T82D4 036:036.610   Data:  00 00 00 00
T82D4 036:036.657 - 0.492ms returns 4 (0x4)
T82D4 036:036.685 JLINK_ReadMemEx(0x20000580, 0x4 Bytes, Flags = 0x02000000)
T82D4 036:036.707   CPU_ReadMem(4 bytes @ 0x20000580)
T82D4 036:037.038   Data:  00 00 00 00
T82D4 036:037.084 - 0.407ms returns 4 (0x4)
T82D4 036:037.108 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 036:037.130   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 036:037.484   Data:  38 80 00 20
T82D4 036:037.531 - 0.431ms returns 4 (0x4)
T82D4 036:037.747 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T82D4 036:037.776   CPU_ReadMem(4 bytes @ 0x00000000)
T82D4 036:038.113   Data:  38 80 00 20
T82D4 036:038.160 - 0.422ms returns 4 (0x4)
T5E80 036:051.306 JLINK_IsHalted()
T5E80 036:051.783 - 0.492ms returns FALSE
T5E80 036:152.103 JLINK_HasError()
T5E80 036:152.167 JLINK_IsHalted()
T5E80 036:152.872 - 0.752ms returns FALSE
T5E80 036:253.746 JLINK_HasError()
T5E80 036:253.991 JLINK_IsHalted()
T5E80 036:254.744 - 0.856ms returns FALSE
T5E80 036:355.368 JLINK_HasError()
T5E80 036:355.420 JLINK_IsHalted()
T5E80 036:355.844 - 0.443ms returns FALSE
T5E80 036:455.935 JLINK_HasError()
T5E80 036:456.070 JLINK_IsHalted()
T5E80 036:456.446 - 0.389ms returns FALSE
T5E80 036:556.556 JLINK_HasError()
T5E80 036:556.687 JLINK_Halt()
T5E80 036:561.291 - 4.790ms returns 0x00
T5E80 036:561.564 JLINK_IsHalted()
T5E80 036:561.700 - 0.201ms returns TRUE
T5E80 036:561.840 JLINK_IsHalted()
T5E80 036:561.957 - 0.183ms returns TRUE
T5E80 036:562.082 JLINK_IsHalted()
T5E80 036:562.203 - 0.165ms returns TRUE
T5E80 036:562.301 JLINK_HasError()
T5E80 036:562.397 JLINK_ReadReg(R15 (PC))
T5E80 036:562.494 - 0.143ms returns 0x0800757A
T5E80 036:562.587 JLINK_ReadReg(XPSR)
T5E80 036:562.671 - 0.126ms returns 0x01000000
T5E80 036:562.774 JLINK_HasError()
T5E80 036:562.869 JLINK_ClrBPEx(BPHandle = 0x00000004)
T5E80 036:562.961 - 0.138ms returns 0x00
T5E80 036:563.053 JLINK_HasError()
T5E80 036:563.144 JLINK_HasError()
T5E80 036:563.240 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T5E80 036:563.347   CPU_ReadMem(4 bytes @ 0xE000ED30)
T5E80 036:564.089   Data:  01 00 00 00
T5E80 036:564.322 - 1.185ms returns 1 (0x1)
T5E80 036:564.580 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T5E80 036:564.740   CPU_ReadMem(4 bytes @ 0xE0001028)
T5E80 036:565.423   Data:  00 00 00 00
T5E80 036:565.658   Debug reg: DWT_FUNC[0]
T5E80 036:565.839 - 1.309ms returns 1 (0x1)
T5E80 036:565.942 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T5E80 036:566.042   CPU_ReadMem(4 bytes @ 0xE0001038)
T5E80 036:566.714   Data:  00 02 00 00
T5E80 036:566.901   Debug reg: DWT_FUNC[1]
T5E80 036:567.027 - 1.126ms returns 1 (0x1)
T5E80 036:567.134 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T5E80 036:567.244   CPU_ReadMem(4 bytes @ 0xE0001048)
T5E80 036:567.897   Data:  00 00 00 00
T5E80 036:568.086   Debug reg: DWT_FUNC[2]
T5E80 036:568.177 - 1.052ms returns 1 (0x1)
T5E80 036:568.196 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T5E80 036:568.217   CPU_ReadMem(4 bytes @ 0xE0001058)
T5E80 036:568.647   Data:  00 00 00 00
T5E80 036:568.679   Debug reg: DWT_FUNC[3]
T5E80 036:568.703 - 0.516ms returns 1 (0x1)
T82D4 037:179.757 JLINK_HasError()
T82D4 037:185.351 JLINK_Close()
T82D4 037:185.764   CPU_WriteMem(4 bytes @ 0xE0002008)
T82D4 037:186.144   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 037:204.941 - 19.691ms
T82D4 037:205.066   
T82D4 037:205.095   Closed
