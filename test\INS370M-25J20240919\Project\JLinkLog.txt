T82D4 000:012.028   SEGGER J-Link V7.22b Log File
T82D4 000:012.297   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:012.328   Logging started @ 2025-06-11 04:20
T82D4 000:012.350 - 12.360ms
T82D4 000:012.388 JLINK_SetWarnOutHandler(...)
T82D4 000:012.412 - 0.035ms
T82D4 000:012.435 JLINK_OpenEx(...)
T82D4 000:013.912   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.302   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.457   Decompressing FW timestamp took 109 us
T82D4 000:020.227   Hardware: V9.40
T82D4 000:020.269   S/N: 59406895
T82D4 000:020.301   OEM: SEGGER
T82D4 000:020.328   Feature(s): R<PERSON>, GDB, <PERSON>D<PERSON>, FlashB<PERSON>, JFlash
T82D4 000:021.177   TELNET listener socket opened on port 19021
T82D4 000:021.366   WEBSRV Starting webserver
T82D4 000:021.538   WEBSRV Webserver running on local port 19080
T82D4 000:021.574 - 9.148ms returns "O.K."
T82D4 000:021.602 JLINK_GetEmuCaps()
T82D4 000:021.622 - 0.030ms returns 0xB9FF7BBF
T82D4 000:021.646 JLINK_TIF_GetAvailable(...)
T82D4 000:021.763 - 0.131ms
T82D4 000:021.790 JLINK_SetErrorOutHandler(...)
T82D4 000:021.810 - 0.029ms
T82D4 000:021.849 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.340   Device "GD32F450II" selected.
T82D4 000:064.616 - 42.789ms returns 0x00
T82D4 000:064.678 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:065.359   Device "GD32F450II" selected.
T82D4 000:065.899 - 1.214ms returns 0x00
T82D4 000:066.017 JLINK_GetHardwareVersion()
T82D4 000:066.048 - 0.040ms returns 94000
T82D4 000:066.067 JLINK_GetDLLVersion()
T82D4 000:066.086 - 0.026ms returns 72202
T82D4 000:066.103 JLINK_GetOEMString(...)
T82D4 000:066.121 JLINK_GetFirmwareString(...)
T82D4 000:066.145 - 0.032ms
T82D4 000:066.175 JLINK_GetDLLVersion()
T82D4 000:066.195 - 0.028ms returns 72202
T82D4 000:066.213 JLINK_GetCompileDateTime()
T82D4 000:066.229 - 0.024ms
T82D4 000:066.252 JLINK_GetFirmwareString(...)
T82D4 000:066.273 - 0.029ms
T82D4 000:066.295 JLINK_GetHardwareVersion()
T82D4 000:066.313 - 0.026ms returns 94000
T82D4 000:066.337 JLINK_GetSN()
T82D4 000:066.355 - 0.027ms returns 59406895
T82D4 000:066.378 JLINK_GetOEMString(...)
T82D4 000:066.409 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:067.200 - 0.809ms returns 0x00
T82D4 000:067.231 JLINK_HasError()
T82D4 000:067.304 JLINK_SetSpeed(2000)
T82D4 000:067.570 - 0.301ms
T82D4 000:067.618 JLINK_GetId()
T82D4 000:068.408   Found SW-DP with ID 0x2BA01477
T82D4 000:071.109   DPIDR: 0x2BA01477
T82D4 000:071.200   Scanning AP map to find all available APs
T82D4 000:071.692   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:071.730   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:071.762   Iterating through AP map to find AHB-AP to use
T82D4 000:072.579   AP[0]: Core found
T82D4 000:072.642   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:073.126   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:073.170   Found Cortex-M4 r0p1, Little endian.
T82D4 000:174.145   -- Max. mem block: 0x00010E60
T82D4 000:174.710   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:175.485   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:176.287   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:177.225   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:177.459   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:178.200   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:178.968   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:179.756   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:180.566   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:181.014   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:181.415   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:181.788   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:182.269   CoreSight components:
T82D4 000:182.313   ROMTbl[0] @ E00FF000
T82D4 000:182.343   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:183.129   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:183.762   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:183.817   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:184.571   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:184.611   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:185.277   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:185.380   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:185.987   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:186.094   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:186.688   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:186.804   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:187.439   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:187.696 - 120.092ms returns 0x2BA01477
T82D4 000:187.726 JLINK_GetDLLVersion()
T82D4 000:187.743 - 0.025ms returns 72202
T82D4 000:187.763 JLINK_CORE_GetFound()
T82D4 000:187.781 - 0.026ms returns 0xE0000FF
T82D4 000:187.799 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:187.819   Value=0xE00FF000
T82D4 000:187.844 - 0.053ms returns 0
T82D4 000:187.869 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:187.889   Value=0xE00FF000
T82D4 000:187.914 - 0.052ms returns 0
T82D4 000:187.932 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:187.951   Value=0x00000000
T82D4 000:187.975 - 0.051ms returns 0
T82D4 000:187.998 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:188.034   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:188.444   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:188.475 - 0.485ms returns 16 (0x10)
T82D4 000:188.495 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:188.514   Value=0x00000000
T82D4 000:188.540 - 0.052ms returns 0
T82D4 000:188.558 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:188.576   Value=0x********
T82D4 000:188.602 - 0.052ms returns 0
T82D4 000:188.620 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:188.638   Value=0x********
T82D4 000:188.663 - 0.051ms returns 0
T82D4 000:188.680 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:188.699   Value=0xE0001000
T82D4 000:188.724 - 0.052ms returns 0
T82D4 000:188.744 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:188.761   Value=0xE0002000
T82D4 000:188.787 - 0.051ms returns 0
T82D4 000:188.805 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:188.823   Value=0xE000E000
T82D4 000:188.848 - 0.051ms returns 0
T82D4 000:188.866 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:188.885   Value=0xE000EDF0
T82D4 000:188.910 - 0.051ms returns 0
T82D4 000:188.928 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:188.947   Value=0x00000001
T82D4 000:188.972 - 0.053ms returns 0
T82D4 000:188.992 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:189.012   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:189.314   Data:  41 C2 0F 41
T82D4 000:189.345   Debug reg: CPUID
T82D4 000:189.370 - 0.386ms returns 1 (0x1)
T82D4 000:189.390 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:189.408   Value=0x00000000
T82D4 000:189.434 - 0.052ms returns 0
T82D4 000:189.454 JLINK_HasError()
T82D4 000:189.474 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:189.493 - 0.026ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:189.511 JLINK_Reset()
T82D4 000:189.539   CPU is running
T82D4 000:189.568   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:189.889   CPU is running
T82D4 000:189.920   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:190.232   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:190.533   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:190.565   CPU is running
T82D4 000:190.590   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:243.896   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:244.742   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:245.515   CPU is running
T82D4 000:245.713   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:246.479   CPU is running
T82D4 000:246.729   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:253.051   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:257.205   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:257.643   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:258.056   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:258.498 - 69.007ms
T82D4 000:258.545 JLINK_HasError()
T82D4 000:258.565 JLINK_ReadReg(R15 (PC))
T82D4 000:258.593 - 0.037ms returns 0x080001C4
T82D4 000:258.613 JLINK_ReadReg(XPSR)
T82D4 000:258.634 - 0.037ms returns 0x01000000
T82D4 000:258.661 JLINK_Halt()
T82D4 000:258.680 - 0.027ms returns 0x00
T82D4 000:258.698 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:258.720   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:259.062   Data:  03 00 03 00
T82D4 000:259.100   Debug reg: DHCSR
T82D4 000:259.125 - 0.435ms returns 1 (0x1)
T82D4 000:259.145 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:259.168   Debug reg: DHCSR
T82D4 000:259.393   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:259.792 - 0.666ms returns 0 (0x00000000)
T82D4 000:259.825 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:259.845   Debug reg: DEMCR
T82D4 000:259.878   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:260.269 - 0.511ms returns 0 (0x00000000)
T82D4 000:260.530 JLINK_GetHWStatus(...)
T82D4 000:260.728 - 0.218ms returns 0
T82D4 000:260.778 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:260.798 - 0.029ms returns 0x06
T82D4 000:260.817 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:260.834 - 0.025ms returns 0x2000
T82D4 000:260.851 JLINK_GetNumWPUnits()
T82D4 000:260.870 - 0.026ms returns 4
T82D4 000:260.897 JLINK_GetSpeed()
T82D4 000:260.916 - 0.028ms returns 2000
T82D4 000:260.939 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:260.963   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:261.322   Data:  02 00 00 00
T82D4 000:261.358 - 0.427ms returns 1 (0x1)
T82D4 000:261.377 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:261.400   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:261.708   Data:  02 00 00 00
T82D4 000:261.739 - 0.369ms returns 1 (0x1)
T82D4 000:261.757 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:261.776   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:261.806   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:262.279 - 0.536ms returns 0x1C
T82D4 000:262.313 JLINK_HasError()
T82D4 000:262.333 JLINK_ReadReg(R15 (PC))
T82D4 000:262.351 - 0.027ms returns 0x080001C4
T82D4 000:262.370 JLINK_ReadReg(XPSR)
T82D4 000:262.389 - 0.028ms returns 0x01000000
T82D4 000:265.493 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:265.545   Data:  00 00 00 00
T82D4 000:265.593   Debug reg: DWT_CYCCNT
T82D4 000:265.649 - 0.165ms returns 4 (0x4)
T82D4 000:346.046 JLINK_HasError()
T82D4 000:346.105 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:346.126 - 0.030ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:346.145 JLINK_Reset()
T82D4 000:346.173   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:346.611   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:347.031   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:347.418   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:347.452   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:401.213   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:401.696   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:402.106   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:402.529   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:408.466   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:412.289   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:412.763   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:413.103   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:413.451 - 67.319ms
T82D4 000:413.573 JLINK_HasError()
T82D4 000:413.599 JLINK_ReadReg(R15 (PC))
T82D4 000:413.620 - 0.030ms returns 0x080001C4
T82D4 000:413.639 JLINK_ReadReg(XPSR)
T82D4 000:413.656 - 0.027ms returns 0x01000000
T82D4 000:413.736 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:413.763   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:414.747    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:414.805    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:414.832   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:414.894 - 1.166ms returns 60 (0x3C)
T82D4 000:414.955 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:414.997    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:415.038   Data:  06 48
T82D4 000:415.064 - 0.117ms returns 2 (0x2)
T82D4 000:415.161 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:415.205    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:415.268   Data:  80 47
T82D4 000:415.309 - 0.156ms returns 2 (0x2)
T82D4 000:415.375 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:415.410    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:415.437   Data:  80 47
T82D4 000:415.461 - 0.095ms returns 2 (0x2)
T82D4 000:415.480 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:415.500   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:416.331    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:416.470    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:416.497   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:416.522 - 1.050ms returns 60 (0x3C)
T82D4 000:416.540 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:416.560    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:416.585   Data:  06 48
T82D4 000:416.611 - 0.079ms returns 2 (0x2)
T82D4 000:416.643 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:416.663    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:416.689   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:416.713 - 0.078ms returns 60 (0x3C)
T82D4 000:416.732 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:416.788    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:416.837   Data:  06 48
T82D4 000:416.879 - 0.155ms returns 2 (0x2)
T82D4 000:416.897 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:416.938    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:416.964   Data:  00 47
T82D4 000:417.005 - 0.115ms returns 2 (0x2)
T82D4 000:417.061 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:417.083    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:417.123   Data:  00 47
T82D4 000:417.150 - 0.097ms returns 2 (0x2)
T82D4 000:417.167 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:417.186    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:417.211   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:417.235 - 0.076ms returns 60 (0x3C)
T82D4 000:417.254 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:417.272    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:417.297   Data:  FE E7
T82D4 000:417.323 - 0.077ms returns 2 (0x2)
T82D4 000:417.345 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:417.365    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:417.391   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:417.415 - 0.077ms returns 60 (0x3C)
T82D4 000:417.433 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:417.451    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:417.475   Data:  FE E7
T82D4 000:417.502 - 0.077ms returns 2 (0x2)
T82D4 000:417.520 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:417.538    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:417.563   Data:  FE E7
T82D4 000:417.587 - 0.075ms returns 2 (0x2)
T82D4 001:884.230 JLINK_HasError()
T82D4 001:884.270 JLINK_ReadReg(R0)
T82D4 001:884.616 - 0.363ms returns 0x20009A57
T82D4 001:884.647 JLINK_ReadReg(R1)
T82D4 001:884.668 - 0.030ms returns 0x0000E29A
T82D4 001:884.687 JLINK_ReadReg(R2)
T82D4 001:884.706 - 0.028ms returns 0x00000000
T82D4 001:884.726 JLINK_ReadReg(R3)
T82D4 001:884.745 - 0.027ms returns 0x00000040
T82D4 001:884.824 JLINK_ReadReg(R4)
T82D4 001:884.853 - 0.040ms returns 0x00000000
T82D4 001:884.876 JLINK_ReadReg(R5)
T82D4 001:884.902 - 0.035ms returns 0x00000000
T82D4 001:884.925 JLINK_ReadReg(R6)
T82D4 001:884.949 - 0.036ms returns 0x00000000
T82D4 001:884.975 JLINK_ReadReg(R7)
T82D4 001:884.999 - 0.051ms returns 0x00000000
T82D4 001:885.037 JLINK_ReadReg(R8)
T82D4 001:885.071 - 0.043ms returns 0x00000000
T82D4 001:885.090 JLINK_ReadReg(R9)
T82D4 001:885.128 - 0.059ms returns 0x20000348
T82D4 001:885.163 JLINK_ReadReg(R10)
T82D4 001:885.184 - 0.029ms returns 0x00000000
T82D4 001:885.203 JLINK_ReadReg(R11)
T82D4 001:885.221 - 0.028ms returns 0x00000000
T82D4 001:885.241 JLINK_ReadReg(R12)
T82D4 001:885.260 - 0.027ms returns 0x00000008
T82D4 001:885.279 JLINK_ReadReg(R13 (SP))
T82D4 001:885.299 - 0.029ms returns 0x20008038
T82D4 001:885.318 JLINK_ReadReg(R14)
T82D4 001:885.337 - 0.028ms returns 0xFFFFFFFF
T82D4 001:885.356 JLINK_ReadReg(R15 (PC))
T82D4 001:885.374 - 0.028ms returns 0x080001C4
T82D4 001:885.395 JLINK_ReadReg(XPSR)
T82D4 001:885.413 - 0.027ms returns 0x01000000
T82D4 001:885.432 JLINK_ReadReg(MSP)
T82D4 001:885.453 - 0.030ms returns 0x20008038
T82D4 001:885.472 JLINK_ReadReg(PSP)
T82D4 001:885.492 - 0.028ms returns 0x20001000
T82D4 001:885.510 JLINK_ReadReg(CFBP)
T82D4 001:885.529 - 0.028ms returns 0x00000000
T82D4 001:885.549 JLINK_ReadReg(FPSCR)
T82D4 001:891.138 - 5.671ms returns 0x00000000
T82D4 001:891.235 JLINK_ReadReg(FPS0)
T82D4 001:891.255 - 0.030ms returns 0x00000000
T82D4 001:891.276 JLINK_ReadReg(FPS1)
T82D4 001:891.294 - 0.028ms returns 0x3FF00000
T82D4 001:891.315 JLINK_ReadReg(FPS2)
T82D4 001:891.334 - 0.027ms returns 0x40174000
T82D4 001:891.354 JLINK_ReadReg(FPS3)
T82D4 001:891.374 - 0.028ms returns 0x6A610350
T82D4 001:891.393 JLINK_ReadReg(FPS4)
T82D4 001:891.413 - 0.029ms returns 0x80CD402C
T82D4 001:891.438 JLINK_ReadReg(FPS5)
T82D4 001:891.461 - 0.033ms returns 0x35490280
T82D4 001:891.482 JLINK_ReadReg(FPS6)
T82D4 001:891.502 - 0.029ms returns 0xD8863040
T82D4 001:891.521 JLINK_ReadReg(FPS7)
T82D4 001:891.541 - 0.028ms returns 0x634222A1
T82D4 001:891.560 JLINK_ReadReg(FPS8)
T82D4 001:891.580 - 0.029ms returns 0x0A1008E1
T82D4 001:891.600 JLINK_ReadReg(FPS9)
T82D4 001:891.619 - 0.028ms returns 0x4F607726
T82D4 001:891.639 JLINK_ReadReg(FPS10)
T82D4 001:891.658 - 0.028ms returns 0x710E9008
T82D4 001:891.678 JLINK_ReadReg(FPS11)
T82D4 001:891.696 - 0.026ms returns 0x432091A0
T82D4 001:891.716 JLINK_ReadReg(FPS12)
T82D4 001:891.735 - 0.028ms returns 0x822280C8
T82D4 001:891.755 JLINK_ReadReg(FPS13)
T82D4 001:891.775 - 0.028ms returns 0x1B944171
T82D4 001:891.794 JLINK_ReadReg(FPS14)
T82D4 001:891.814 - 0.028ms returns 0x4502E888
T82D4 001:891.833 JLINK_ReadReg(FPS15)
T82D4 001:891.853 - 0.028ms returns 0x112E0105
T82D4 001:891.873 JLINK_ReadReg(FPS16)
T82D4 001:891.892 - 0.028ms returns 0x80880000
T82D4 001:891.912 JLINK_ReadReg(FPS17)
T82D4 001:891.931 - 0.028ms returns 0x01092210
T82D4 001:891.951 JLINK_ReadReg(FPS18)
T82D4 001:891.969 - 0.027ms returns 0x6FC00247
T82D4 001:891.990 JLINK_ReadReg(FPS19)
T82D4 001:892.008 - 0.026ms returns 0x42109945
T82D4 001:892.028 JLINK_ReadReg(FPS20)
T82D4 001:892.047 - 0.028ms returns 0x02052300
T82D4 001:892.067 JLINK_ReadReg(FPS21)
T82D4 001:892.086 - 0.028ms returns 0x2802CC28
T82D4 001:892.106 JLINK_ReadReg(FPS22)
T82D4 001:892.125 - 0.028ms returns 0x10034492
T82D4 001:892.145 JLINK_ReadReg(FPS23)
T82D4 001:892.165 - 0.028ms returns 0x8970B47C
T82D4 001:892.184 JLINK_ReadReg(FPS24)
T82D4 001:892.204 - 0.030ms returns 0x8624020A
T82D4 001:892.249 JLINK_ReadReg(FPS25)
T82D4 001:892.267 - 0.028ms returns 0xC00C1403
T82D4 001:892.304 JLINK_ReadReg(FPS26)
T82D4 001:892.322 - 0.027ms returns 0x202A04C0
T82D4 001:892.343 JLINK_ReadReg(FPS27)
T82D4 001:892.362 - 0.028ms returns 0x152712A2
T82D4 001:892.381 JLINK_ReadReg(FPS28)
T82D4 001:892.401 - 0.029ms returns 0x545840BB
T82D4 001:892.421 JLINK_ReadReg(FPS29)
T82D4 001:892.441 - 0.029ms returns 0x01482A20
T82D4 001:892.462 JLINK_ReadReg(FPS30)
T82D4 001:892.485 - 0.032ms returns 0x0247A931
T82D4 001:892.505 JLINK_ReadReg(FPS31)
T82D4 001:892.556 - 0.060ms returns 0x64CC89A5
T82D4 001:978.501 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:978.573   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 001:979.398    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 001:979.438    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:979.466   Data:  00 00
T82D4 001:979.508 - 1.017ms returns 2 (0x2)
T82D4 001:979.595 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:979.620    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:979.646   Data:  00 00
T82D4 001:979.671 - 0.084ms returns 2 (0x2)
T82D4 001:979.707 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:979.729    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:979.755   Data:  00 00
T82D4 001:979.781 - 0.082ms returns 2 (0x2)
T82D4 001:986.242 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:986.316    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:986.349   Data:  00 00 00 00
T82D4 001:986.382 - 0.151ms returns 4 (0x4)
T82D4 001:986.421 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:986.448    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:986.481   Data:  00 00 00 00
T82D4 001:986.513 - 0.102ms returns 4 (0x4)
T82D4 001:986.558 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:986.586    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:986.619   Data:  00 00 00 00
T82D4 001:986.652 - 0.105ms returns 4 (0x4)
T82D4 002:003.570 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:003.637    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:003.667   Data:  00 00 00 00
T82D4 002:003.693 - 0.132ms returns 4 (0x4)
T82D4 002:003.727 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:003.751    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:003.778   Data:  00 00 00 00
T82D4 002:003.805 - 0.088ms returns 4 (0x4)
T82D4 002:003.840 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:003.861    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:003.890   Data:  00 00 00 00
T82D4 002:003.916 - 0.085ms returns 4 (0x4)
T82D4 002:019.293 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:019.356    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:019.383   Data:  00 00 00 00
T82D4 002:019.409 - 0.125ms returns 4 (0x4)
T82D4 002:019.441 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:019.462    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:019.490   Data:  00 00 00 00
T82D4 002:019.514 - 0.081ms returns 4 (0x4)
T82D4 002:019.546 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:019.568    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:019.594   Data:  00 00 00 00
T82D4 002:019.620 - 0.083ms returns 4 (0x4)
T82D4 002:027.983 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:028.069    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:028.106   Data:  00 00 00 00
T82D4 002:028.133 - 0.158ms returns 4 (0x4)
T82D4 002:028.171 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:028.195    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:028.222   Data:  00 00 00 00
T82D4 002:028.250 - 0.088ms returns 4 (0x4)
T82D4 002:028.285 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:028.308    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:028.336   Data:  00 00 00 00
T82D4 002:028.362 - 0.085ms returns 4 (0x4)
T82D4 002:035.980 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:036.035    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:036.065   Data:  00 00 00 00
T82D4 002:036.091 - 0.120ms returns 4 (0x4)
T82D4 002:036.124 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:036.148    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:036.182   Data:  00 00 00 00
T82D4 002:036.210 - 0.095ms returns 4 (0x4)
T82D4 002:036.246 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:036.268    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:036.296   Data:  00 00 00 00
T82D4 002:036.323 - 0.085ms returns 4 (0x4)
T82D4 002:093.209 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:093.412   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:094.404    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:094.473    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:094.503   Data:  00 00 00 00
T82D4 002:094.533 - 1.335ms returns 4 (0x4)
T82D4 002:094.815 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:094.866    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:094.893   Data:  00 00 00 00
T82D4 002:094.919 - 0.112ms returns 4 (0x4)
T82D4 002:094.977 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:094.999    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:095.049   Data:  00 00 00 00
T82D4 002:095.078 - 0.109ms returns 4 (0x4)
T82D4 002:104.924 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:104.979    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:105.005   Data:  00 00 00 00
T82D4 002:105.030 - 0.115ms returns 4 (0x4)
T82D4 002:105.064 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:105.084    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:105.133   Data:  00 00 00 00
T82D4 002:105.172 - 0.116ms returns 4 (0x4)
T82D4 002:105.202 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:105.223    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:105.248   Data:  00 00 00 00
T82D4 002:105.273 - 0.080ms returns 4 (0x4)
T82D4 002:115.600 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:115.671   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:116.701    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:116.820    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:116.847   Data:  00 00 00 00
T82D4 002:116.874 - 1.282ms returns 4 (0x4)
T82D4 002:116.917 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:116.942    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:116.969   Data:  00 00 00 00
T82D4 002:116.994 - 0.087ms returns 4 (0x4)
T82D4 002:117.030 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:117.052    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:117.079   Data:  00 00 00 00
T82D4 002:117.108 - 0.086ms returns 4 (0x4)
T82D4 002:128.630 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:128.702   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:129.641    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:129.757    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:129.784   Data:  00 00 00 00
T82D4 002:129.811 - 1.190ms returns 4 (0x4)
T82D4 002:129.853 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:129.877    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:129.904   Data:  00 00 00 00
T82D4 002:129.929 - 0.085ms returns 4 (0x4)
T82D4 002:129.965 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:129.988    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:130.014   Data:  00 00 00 00
T82D4 002:130.041 - 0.084ms returns 4 (0x4)
T82D4 002:141.658 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:141.716    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:141.744   Data:  00 00 00 00
T82D4 002:141.771 - 0.121ms returns 4 (0x4)
T82D4 002:141.806 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:141.828    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:141.857   Data:  00 00 00 00
T82D4 002:141.882 - 0.085ms returns 4 (0x4)
T82D4 002:141.914 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:141.939    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:141.965   Data:  00 00 00 00
T82D4 002:141.992 - 0.086ms returns 4 (0x4)
T82D4 002:155.864 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:155.933   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:156.788    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:156.832    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:156.860   Data:  71
T82D4 002:156.886 - 1.030ms returns 1 (0x1)
T82D4 002:157.010 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:157.036    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:157.063   Data:  71
T82D4 002:157.089 - 0.087ms returns 1 (0x1)
T82D4 002:157.127 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:157.153    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:157.180   Data:  71
T82D4 002:157.206 - 0.088ms returns 1 (0x1)
T82D4 002:171.031 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:171.101   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 002:171.987    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 002:172.037    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 002:172.065   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:172.090 - 1.068ms returns 32 (0x20)
T82D4 002:249.899 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:249.966    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:249.994   Data:  00 00 00 00
T82D4 002:250.021 - 0.130ms returns 4 (0x4)
T82D4 002:250.084 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:250.107    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:250.135   Data:  00 00 00 00
T82D4 002:250.162 - 0.087ms returns 4 (0x4)
T82D4 002:250.198 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:250.221    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:250.249   Data:  00 00 00 00
T82D4 002:250.276 - 0.088ms returns 4 (0x4)
T82D4 002:263.547 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:263.613    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:263.641   Data:  00 00 00 00
T82D4 002:263.673 - 0.135ms returns 4 (0x4)
T82D4 002:263.709 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:263.731    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:263.758   Data:  00 00 00 00
T82D4 002:263.784 - 0.083ms returns 4 (0x4)
T82D4 002:263.816 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:263.839    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:263.864   Data:  00 00 00 00
T82D4 002:263.890 - 0.083ms returns 4 (0x4)
T82D4 002:276.427 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:276.491    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:276.519   Data:  00 00 00 00
T82D4 002:276.546 - 0.127ms returns 4 (0x4)
T82D4 002:276.613 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:276.653    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:276.694   Data:  00 00 00 00
T82D4 002:276.776 - 0.183ms returns 4 (0x4)
T82D4 002:276.940 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:277.005    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:277.067   Data:  00 00 00 00
T82D4 002:277.139 - 0.234ms returns 4 (0x4)
T82D4 002:341.476 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:341.535    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:341.563   Data:  71
T82D4 002:341.589 - 0.123ms returns 1 (0x1)
T82D4 002:341.626 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:341.649    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:341.676   Data:  71
T82D4 002:341.702 - 0.084ms returns 1 (0x1)
T82D4 002:341.734 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:341.757    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:341.783   Data:  71
T82D4 002:341.814 - 0.089ms returns 1 (0x1)
T82D4 002:432.184 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:432.244    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:432.270   Data:  00 00 00 00
T82D4 002:432.364 - 0.189ms returns 4 (0x4)
T82D4 002:432.400 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:432.429    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:432.460   Data:  00 00 00 00
T82D4 002:432.491 - 0.101ms returns 4 (0x4)
T82D4 002:432.532 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:432.560    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:432.587   Data:  00 00 00 00
T82D4 002:432.612 - 0.089ms returns 4 (0x4)
T82D4 002:446.922 JLINK_ReadMemEx(0x08019960, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:446.988   CPU_ReadMem(64 bytes @ 0x08019940)
T82D4 002:448.011    -- Updating C cache (64 bytes @ 0x08019940)
T82D4 002:448.049    -- Read from C cache (8 bytes @ 0x08019960)
T82D4 002:448.075   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:448.100 - 1.186ms returns 8 (0x8)
T82D4 002:448.173 JLINK_ReadMemEx(0x08019960, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:448.196    -- Read from C cache (8 bytes @ 0x08019960)
T82D4 002:448.222   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:448.247 - 0.082ms returns 8 (0x8)
T82D4 002:448.279 JLINK_ReadMemEx(0x08019960, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:448.300    -- Read from C cache (8 bytes @ 0x08019960)
T82D4 002:448.326   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:448.351 - 0.079ms returns 8 (0x8)
T82D4 002:474.334 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:474.400   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:475.562    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:475.787    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:475.873   Data:  00 00 00 00
T82D4 002:475.899 - 1.573ms returns 4 (0x4)
T82D4 002:475.978 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:476.006    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:476.031   Data:  00 00 00 00
T82D4 002:476.058 - 0.088ms returns 4 (0x4)
T82D4 002:476.147 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:476.192    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:476.239   Data:  00 00 00 00
T82D4 002:476.287 - 0.152ms returns 4 (0x4)
T82D4 002:552.350 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.413    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:552.441   Data:  00 00 00 00
T82D4 002:552.467 - 0.126ms returns 4 (0x4)
T82D4 002:552.496 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.518    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:552.544   Data:  00 00 00 00
T82D4 002:552.569 - 0.082ms returns 4 (0x4)
T82D4 002:552.597 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.619    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:552.644   Data:  00 00 00 00
T82D4 002:552.671 - 0.082ms returns 4 (0x4)
T4778 002:595.116 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T4778 002:595.188    -- Read from C cache (2 bytes @ 0x080001C4)
T4778 002:595.214   Data:  06 48
T4778 002:595.240 - 0.132ms returns 2 (0x2)
T4778 002:595.260 JLINK_HasError()
T4778 002:595.279 JLINK_SetBPEx(Addr = 0x08016AF0, Type = 0xFFFFFFF2)
T4778 002:595.303 - 0.032ms returns 0x00000001
T4778 002:595.322 JLINK_HasError()
T4778 002:595.340 JLINK_SetBPEx(Addr = 0x0800D8E0, Type = 0xFFFFFFF2)
T4778 002:595.359 - 0.027ms returns 0x00000002
T4778 002:595.376 JLINK_HasError()
T4778 002:595.395 JLINK_SetBPEx(Addr = 0x08016B7E, Type = 0xFFFFFFF2)
T4778 002:595.413 - 0.026ms returns 0x00000003
T4778 002:595.432 JLINK_HasError()
T4778 002:595.450 JLINK_SetBPEx(Addr = 0x0800E4B0, Type = 0xFFFFFFF2)
T4778 002:595.469 - 0.027ms returns 0x00000004
T4778 002:595.487 JLINK_HasError()
T4778 002:595.504 JLINK_HasError()
T4778 002:595.545 JLINK_Go()
T4778 002:596.050   CPU_WriteMem(4 bytes @ 0xE0002000)
T4778 002:596.434   CPU_WriteMem(4 bytes @ 0xE0002000)
T4778 002:596.877   CPU_WriteMem(4 bytes @ 0xE0002000)
T4778 002:597.257   CPU_WriteMem(4 bytes @ 0xE0002000)
T4778 002:597.615   CPU_ReadMem(4 bytes @ 0xE0001000)
T4778 002:597.984   CPU_WriteMem(4 bytes @ 0xE0002008)
T4778 002:598.034   CPU_WriteMem(4 bytes @ 0xE000200C)
T4778 002:598.062   CPU_WriteMem(4 bytes @ 0xE0002010)
T4778 002:598.092   CPU_WriteMem(4 bytes @ 0xE0002014)
T4778 002:598.120   CPU_WriteMem(4 bytes @ 0xE0002018)
T4778 002:598.149   CPU_WriteMem(4 bytes @ 0xE000201C)
T4778 002:599.626   CPU_WriteMem(4 bytes @ 0xE0001004)
T4778 002:600.445 - 4.922ms
T4778 002:701.501 JLINK_HasError()
T4778 002:701.640 JLINK_IsHalted()
T4778 002:702.016 - 0.390ms returns FALSE
T4778 002:802.798 JLINK_HasError()
T4778 002:802.855 JLINK_IsHalted()
T4778 002:806.316 - 3.526ms returns TRUE
T4778 002:806.394 JLINK_HasError()
T4778 002:806.412 JLINK_Halt()
T4778 002:806.430 - 0.026ms returns 0x00
T4778 002:806.448 JLINK_IsHalted()
T4778 002:806.467 - 0.027ms returns TRUE
T4778 002:806.484 JLINK_IsHalted()
T4778 002:806.529 - 0.053ms returns TRUE
T4778 002:806.548 JLINK_IsHalted()
T4778 002:806.579 - 0.045ms returns TRUE
T4778 002:806.623 JLINK_HasError()
T4778 002:806.644 JLINK_ReadReg(R15 (PC))
T4778 002:806.678 - 0.043ms returns 0x08016AF0
T4778 002:806.697 JLINK_ReadReg(XPSR)
T4778 002:806.735 - 0.046ms returns 0x61000000
T4778 002:806.770 JLINK_HasError()
T4778 002:806.833 JLINK_ClrBPEx(BPHandle = 0x00000001)
T4778 002:806.853 - 0.030ms returns 0x00
T4778 002:806.888 JLINK_HasError()
T4778 002:806.940 JLINK_ClrBPEx(BPHandle = 0x00000002)
T4778 002:806.959 - 0.042ms returns 0x00
T4778 002:807.012 JLINK_HasError()
T4778 002:807.031 JLINK_ClrBPEx(BPHandle = 0x00000003)
T4778 002:807.088 - 0.067ms returns 0x00
T4778 002:807.108 JLINK_HasError()
T4778 002:807.143 JLINK_ClrBPEx(BPHandle = 0x00000004)
T4778 002:807.161 - 0.026ms returns 0x00
T4778 002:807.180 JLINK_HasError()
T4778 002:807.213 JLINK_HasError()
T4778 002:807.253 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T4778 002:807.298   CPU_ReadMem(4 bytes @ 0xE000ED30)
T4778 002:807.696   Data:  02 00 00 00
T4778 002:807.746 - 0.501ms returns 1 (0x1)
T4778 002:807.765 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T4778 002:807.788   CPU_ReadMem(4 bytes @ 0xE0001028)
T4778 002:808.134   Data:  00 00 00 00
T4778 002:808.181   Debug reg: DWT_FUNC[0]
T4778 002:808.208 - 0.452ms returns 1 (0x1)
T4778 002:808.228 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T4778 002:808.252   CPU_ReadMem(4 bytes @ 0xE0001038)
T4778 002:808.566   Data:  00 02 00 00
T4778 002:808.612   Debug reg: DWT_FUNC[1]
T4778 002:808.640 - 0.422ms returns 1 (0x1)
T4778 002:808.668 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T4778 002:808.700   CPU_ReadMem(4 bytes @ 0xE0001048)
T4778 002:809.034   Data:  00 00 00 00
T4778 002:809.067   Debug reg: DWT_FUNC[2]
T4778 002:809.095 - 0.436ms returns 1 (0x1)
T4778 002:809.115 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T4778 002:809.138   CPU_ReadMem(4 bytes @ 0xE0001058)
T4778 002:809.476   Data:  00 00 00 00
T4778 002:809.513   Debug reg: DWT_FUNC[3]
T4778 002:809.539 - 0.433ms returns 1 (0x1)
T4778 002:809.635 JLINK_HasError()
T4778 002:809.658 JLINK_ReadReg(R0)
T4778 002:809.680 - 0.032ms returns 0x08016AF1
T4778 002:809.700 JLINK_ReadReg(R1)
T4778 002:809.719 - 0.029ms returns 0x20022A08
T4778 002:809.739 JLINK_ReadReg(R2)
T4778 002:809.758 - 0.028ms returns 0x00000000
T4778 002:809.779 JLINK_ReadReg(R3)
T4778 002:809.799 - 0.029ms returns 0x08012FC9
T4778 002:809.818 JLINK_ReadReg(R4)
T4778 002:809.839 - 0.030ms returns 0x08019ED0
T4778 002:809.858 JLINK_ReadReg(R5)
T4778 002:809.879 - 0.029ms returns 0x08019ED0
T4778 002:809.898 JLINK_ReadReg(R6)
T4778 002:809.916 - 0.028ms returns 0x00000000
T4778 002:809.937 JLINK_ReadReg(R7)
T4778 002:809.955 - 0.028ms returns 0x00000000
T4778 002:809.974 JLINK_ReadReg(R8)
T4778 002:809.995 - 0.030ms returns 0x00000000
T4778 002:810.014 JLINK_ReadReg(R9)
T4778 002:810.034 - 0.029ms returns 0x20000348
T4778 002:810.053 JLINK_ReadReg(R10)
T4778 002:810.072 - 0.028ms returns 0x00000000
T4778 002:810.093 JLINK_ReadReg(R11)
T4778 002:810.112 - 0.028ms returns 0x00000000
T4778 002:810.130 JLINK_ReadReg(R12)
T4778 002:810.151 - 0.029ms returns 0x00000008
T4778 002:810.173 JLINK_ReadReg(R13 (SP))
T4778 002:810.197 - 0.032ms returns 0x20022A08
T4778 002:810.216 JLINK_ReadReg(R14)
T4778 002:810.235 - 0.028ms returns 0x08004E6D
T4778 002:810.255 JLINK_ReadReg(R15 (PC))
T4778 002:810.274 - 0.028ms returns 0x08016AF0
T4778 002:810.293 JLINK_ReadReg(XPSR)
T4778 002:810.313 - 0.029ms returns 0x61000000
T4778 002:810.333 JLINK_ReadReg(MSP)
T4778 002:810.353 - 0.029ms returns 0x20022A08
T4778 002:810.372 JLINK_ReadReg(PSP)
T4778 002:810.390 - 0.028ms returns 0x20001000
T4778 002:810.411 JLINK_ReadReg(CFBP)
T4778 002:810.430 - 0.028ms returns 0x00000000
T4778 002:810.448 JLINK_ReadReg(FPSCR)
T4778 002:815.826 - 5.424ms returns 0x00000000
T4778 002:815.888 JLINK_ReadReg(FPS0)
T4778 002:815.910 - 0.031ms returns 0x00000000
T4778 002:815.930 JLINK_ReadReg(FPS1)
T4778 002:815.950 - 0.029ms returns 0x3FF00000
T4778 002:815.970 JLINK_ReadReg(FPS2)
T4778 002:815.990 - 0.029ms returns 0x40174000
T4778 002:816.010 JLINK_ReadReg(FPS3)
T4778 002:816.030 - 0.029ms returns 0x6A610350
T4778 002:816.049 JLINK_ReadReg(FPS4)
T4778 002:816.070 - 0.029ms returns 0x80CD402C
T4778 002:816.089 JLINK_ReadReg(FPS5)
T4778 002:816.109 - 0.029ms returns 0x35490280
T4778 002:816.129 JLINK_ReadReg(FPS6)
T4778 002:816.148 - 0.028ms returns 0xD8863040
T4778 002:816.169 JLINK_ReadReg(FPS7)
T4778 002:816.188 - 0.027ms returns 0x634222A1
T4778 002:816.209 JLINK_ReadReg(FPS8)
T4778 002:816.228 - 0.028ms returns 0x0A1008E1
T4778 002:816.248 JLINK_ReadReg(FPS9)
T4778 002:816.268 - 0.029ms returns 0x4F607726
T4778 002:816.287 JLINK_ReadReg(FPS10)
T4778 002:816.307 - 0.029ms returns 0x710E9008
T4778 002:816.327 JLINK_ReadReg(FPS11)
T4778 002:816.347 - 0.029ms returns 0x432091A0
T4778 002:816.367 JLINK_ReadReg(FPS12)
T4778 002:816.387 - 0.029ms returns 0x822280C8
T4778 002:816.406 JLINK_ReadReg(FPS13)
T4778 002:816.426 - 0.029ms returns 0x1B944171
T4778 002:816.446 JLINK_ReadReg(FPS14)
T4778 002:816.466 - 0.029ms returns 0x4502E888
T4778 002:816.487 JLINK_ReadReg(FPS15)
T4778 002:816.506 - 0.027ms returns 0x112E0105
T4778 002:816.526 JLINK_ReadReg(FPS16)
T4778 002:816.545 - 0.028ms returns 0x80880000
T4778 002:816.565 JLINK_ReadReg(FPS17)
T4778 002:816.585 - 0.029ms returns 0x01092210
T4778 002:816.604 JLINK_ReadReg(FPS18)
T4778 002:816.624 - 0.029ms returns 0x6FC00247
T4778 002:816.644 JLINK_ReadReg(FPS19)
T4778 002:816.664 - 0.029ms returns 0x42109945
T4778 002:816.684 JLINK_ReadReg(FPS20)
T4778 002:816.704 - 0.029ms returns 0x02052300
T4778 002:816.723 JLINK_ReadReg(FPS21)
T4778 002:816.743 - 0.031ms returns 0x2802CC28
T4778 002:816.766 JLINK_ReadReg(FPS22)
T4778 002:816.785 - 0.029ms returns 0x10034492
T4778 002:816.806 JLINK_ReadReg(FPS23)
T4778 002:816.824 - 0.028ms returns 0x8970B47C
T4778 002:816.846 JLINK_ReadReg(FPS24)
T4778 002:816.865 - 0.028ms returns 0x8624020A
T4778 002:816.885 JLINK_ReadReg(FPS25)
T4778 002:816.904 - 0.027ms returns 0xC00C1403
T4778 002:816.924 JLINK_ReadReg(FPS26)
T4778 002:816.944 - 0.030ms returns 0x202A04C0
T4778 002:816.965 JLINK_ReadReg(FPS27)
T4778 002:816.985 - 0.028ms returns 0x152712A2
T4778 002:817.004 JLINK_ReadReg(FPS28)
T4778 002:817.025 - 0.029ms returns 0x545840BB
T4778 002:817.045 JLINK_ReadReg(FPS29)
T4778 002:817.064 - 0.028ms returns 0x01482A20
T4778 002:817.084 JLINK_ReadReg(FPS30)
T4778 002:817.103 - 0.029ms returns 0x0247A931
T4778 002:817.124 JLINK_ReadReg(FPS31)
T4778 002:817.142 - 0.028ms returns 0x64CC89A5
T82D4 002:817.374 JLINK_ReadMemEx(0x200229D4, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:817.411   CPU_ReadMem(64 bytes @ 0x200229C0)
T82D4 002:818.240    -- Updating C cache (64 bytes @ 0x200229C0)
T82D4 002:818.368    -- Read from C cache (32 bytes @ 0x200229D4)
T82D4 002:818.396   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:818.422 - 1.056ms returns 32 (0x20)
T82D4 002:819.284 JLINK_ReadMemEx(0x200229D4, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:819.314    -- Read from C cache (32 bytes @ 0x200229D4)
T82D4 002:819.341   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:819.370 - 0.094ms returns 32 (0x20)
T82D4 002:819.397 JLINK_HasError()
T82D4 002:819.419 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:819.439   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:819.742   Data:  C7 F4 13 00
T82D4 002:819.772   Debug reg: DWT_CYCCNT
T82D4 002:819.796 - 0.385ms returns 1 (0x1)
T82D4 002:821.644 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:821.677   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 002:822.460    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 002:822.499    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 002:822.524   Data:  00 00
T82D4 002:822.550 - 0.915ms returns 2 (0x2)
T82D4 002:822.578 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:822.600    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:822.627   Data:  00 00 00 00
T82D4 002:822.652 - 0.082ms returns 4 (0x4)
T82D4 002:823.182 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:823.212    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:823.240   Data:  00 00 00 00
T82D4 002:823.265 - 0.091ms returns 4 (0x4)
T82D4 002:823.298 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:823.320    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:823.346   Data:  00 00 00 00
T82D4 002:823.372 - 0.083ms returns 4 (0x4)
T82D4 002:823.395 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:823.415    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:823.442   Data:  00 00 00 00
T82D4 002:823.467 - 0.079ms returns 4 (0x4)
T82D4 002:823.489 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:823.511    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:823.536   Data:  00 00 00 00
T82D4 002:823.561 - 0.081ms returns 4 (0x4)
T82D4 002:823.603 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:823.625   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:824.454    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:824.584    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:824.609   Data:  00 00 00 00
T82D4 002:824.634 - 1.040ms returns 4 (0x4)
T82D4 002:824.659 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:824.679    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:824.707   Data:  00 00 00 00
T82D4 002:824.732 - 0.081ms returns 4 (0x4)
T82D4 002:824.753 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:824.777   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:825.533    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:825.572    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:825.598   Data:  00 00 00 00
T82D4 002:825.625 - 0.880ms returns 4 (0x4)
T82D4 002:825.654 JLINK_ReadMemEx(0x200005F8, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:825.679   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:826.516    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:826.630    -- Read from C cache (4 bytes @ 0x200005F8)
T82D4 002:826.655   Data:  00 00 00 00
T82D4 002:826.682 - 1.037ms returns 4 (0x4)
T82D4 002:826.708 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:826.731    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:826.757   Data:  00 00 00 00
T82D4 002:826.782 - 0.083ms returns 4 (0x4)
T82D4 002:826.806 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:826.828   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:827.703    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:827.834    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:827.859   Data:  00
T82D4 002:827.883 - 1.086ms returns 1 (0x1)
T82D4 002:828.091 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:828.121   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 002:828.922    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 002:829.052    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 002:829.078   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:829.104 - 1.021ms returns 32 (0x20)
T82D4 002:829.226 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:829.253    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:829.279   Data:  00 00 00 00
T82D4 002:829.304 - 0.087ms returns 4 (0x4)
T82D4 002:829.329 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:829.349    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:829.376   Data:  00 00 00 00
T82D4 002:829.402 - 0.081ms returns 4 (0x4)
T82D4 002:829.424 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:829.447    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:829.473   Data:  00 00 00 00
T82D4 002:829.499 - 0.083ms returns 4 (0x4)
T82D4 002:829.532 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:829.552    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:829.579   Data:  00
T82D4 002:829.605 - 0.081ms returns 1 (0x1)
T82D4 002:829.654 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:829.677    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:829.708   Data:  00 00 00 00
T82D4 002:829.735 - 0.089ms returns 4 (0x4)
T82D4 002:829.758 JLINK_ReadMemEx(0x08019960, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:829.779   CPU_ReadMem(64 bytes @ 0x08019940)
T82D4 002:830.606    -- Updating C cache (64 bytes @ 0x08019940)
T82D4 002:830.735    -- Read from C cache (8 bytes @ 0x08019960)
T82D4 002:830.760   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:830.787 - 1.038ms returns 8 (0x8)
T82D4 002:830.926 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:830.952   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:831.741    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:831.773    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:831.799   Data:  00 00 00 00
T82D4 002:831.824 - 0.906ms returns 4 (0x4)
T82D4 002:835.139 JLINK_ReadMemEx(0x08016AF0, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:835.178   CPU_ReadMem(128 bytes @ 0x08016AC0)
T82D4 002:836.438    -- Updating C cache (128 bytes @ 0x08016AC0)
T82D4 002:836.479    -- Read from C cache (60 bytes @ 0x08016AF0)
T82D4 002:836.508   Data:  8E B0 00 24 00 25 00 26 F9 F7 48 FA F4 F7 26 FF ...
T82D4 002:836.534 - 1.405ms returns 60 (0x3C)
T82D4 002:836.556 JLINK_ReadMemEx(0x08016AF0, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:836.582    -- Read from C cache (2 bytes @ 0x08016AF0)
T82D4 002:836.622   Data:  8E B0
T82D4 002:836.660 - 0.117ms returns 2 (0x2)
T82D4 002:836.688 JLINK_ReadMemEx(0x08016AF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:836.713    -- Read from C cache (2 bytes @ 0x08016AF2)
T82D4 002:836.753   Data:  00 24
T82D4 002:836.792 - 0.118ms returns 2 (0x2)
T82D4 002:836.833 JLINK_ReadMemEx(0x08016AF2, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:836.864    -- Read from C cache (2 bytes @ 0x08016AF2)
T82D4 002:836.906   Data:  00 24
T82D4 002:836.948 - 0.129ms returns 2 (0x2)
T82D4 002:836.976 JLINK_ReadMemEx(0x08016AF4, 0x3C Bytes, Flags = 0x02000000)
T82D4 002:837.003    -- Read from C cache (60 bytes @ 0x08016AF4)
T82D4 002:837.043   Data:  00 25 00 26 F9 F7 48 FA F4 F7 26 FF 4F F4 7A 70 ...
T82D4 002:837.077 - 0.112ms returns 60 (0x3C)
T82D4 002:837.101 JLINK_ReadMemEx(0x08016AF4, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:837.128    -- Read from C cache (2 bytes @ 0x08016AF4)
T82D4 002:837.166   Data:  00 25
T82D4 002:837.204 - 0.115ms returns 2 (0x2)
