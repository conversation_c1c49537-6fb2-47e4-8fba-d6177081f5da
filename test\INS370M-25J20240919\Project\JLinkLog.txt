T82D4 000:013.114   SEGGER J-Link V7.22b Log File
T82D4 000:013.410   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:013.460   Logging started @ 2025-06-11 02:16
T82D4 000:013.485 - 13.496ms
T82D4 000:013.523 JLINK_SetWarnOutHandler(...)
T82D4 000:013.551 - 0.037ms
T82D4 000:013.571 JLINK_OpenEx(...)
T82D4 000:015.017   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:015.552   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:015.786   Decompressing FW timestamp took 145 us
T82D4 000:021.690   Hardware: V9.40
T82D4 000:021.731   S/N: 59406895
T82D4 000:021.764   OEM: SEGGER
T82D4 000:021.792   Feature(s): <PERSON><PERSON>, GD<PERSON>, FlashDL, FlashB<PERSON>, JFlash
T82D4 000:022.568   TELNET listener socket opened on port 19021
T82D4 000:022.789   WEBSRV Starting webserver
T82D4 000:022.981   WEBSRV Webserver running on local port 19080
T82D4 000:023.023 - 9.460ms returns "O.K."
T82D4 000:023.051 JLINK_GetEmuCaps()
T82D4 000:023.071 - 0.029ms returns 0xB9FF7BBF
T82D4 000:023.094 JLINK_TIF_GetAvailable(...)
T82D4 000:023.253 - 0.176ms
T82D4 000:023.284 JLINK_SetErrorOutHandler(...)
T82D4 000:023.304 - 0.029ms
T82D4 000:023.340 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:033.239   Device "GD32F450II" selected.
T82D4 000:033.905 - 10.582ms returns 0x00
T82D4 000:033.950 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:034.600   Device "GD32F450II" selected.
T82D4 000:035.228 - 1.273ms returns 0x00
T82D4 000:035.262 JLINK_GetHardwareVersion()
T82D4 000:035.286 - 0.035ms returns 94000
T82D4 000:035.308 JLINK_GetDLLVersion()
T82D4 000:035.325 - 0.026ms returns 72202
T82D4 000:035.345 JLINK_GetOEMString(...)
T82D4 000:035.364 JLINK_GetFirmwareString(...)
T82D4 000:035.385 - 0.030ms
T82D4 000:035.415 JLINK_GetDLLVersion()
T82D4 000:035.435 - 0.028ms returns 72202
T82D4 000:035.454 JLINK_GetCompileDateTime()
T82D4 000:035.471 - 0.025ms
T82D4 000:035.493 JLINK_GetFirmwareString(...)
T82D4 000:035.513 - 0.028ms
T82D4 000:035.535 JLINK_GetHardwareVersion()
T82D4 000:035.555 - 0.027ms returns 94000
T82D4 000:035.576 JLINK_GetSN()
T82D4 000:035.595 - 0.027ms returns 59406895
T82D4 000:035.616 JLINK_GetOEMString(...)
T82D4 000:035.648 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:036.126 - 0.496ms returns 0x00
T82D4 000:036.156 JLINK_HasError()
T82D4 000:036.186 JLINK_SetSpeed(2000)
T82D4 000:036.262 - 0.088ms
T82D4 000:036.287 JLINK_GetId()
T82D4 000:036.981   Found SW-DP with ID 0x2BA01477
T82D4 000:039.473   DPIDR: 0x2BA01477
T82D4 000:039.521   Scanning AP map to find all available APs
T82D4 000:040.031   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:040.070   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:040.102   Iterating through AP map to find AHB-AP to use
T82D4 000:040.884   AP[0]: Core found
T82D4 000:040.921   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:041.354   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:041.425   Found Cortex-M4 r0p1, Little endian.
T82D4 000:142.587   -- Max. mem block: 0x00010E60
T82D4 000:142.726   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:143.105   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:143.481   CPU_ReadMem(4 bytes @ 0xE0002000)
T82D4 000:143.817   FPUnit: 6 code (BP) slots and 2 literal slots
T82D4 000:143.848   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:144.227   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:144.553   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:144.856   CPU_WriteMem(4 bytes @ 0xE0001000)
T82D4 000:145.178   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:145.558   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:145.887   CPU_ReadMem(4 bytes @ 0xE000ED88)
T82D4 000:146.189   CPU_WriteMem(4 bytes @ 0xE000ED88)
T82D4 000:146.513   CoreSight components:
T82D4 000:146.547   ROMTbl[0] @ E00FF000
T82D4 000:146.575   CPU_ReadMem(64 bytes @ 0xE00FF000)
T82D4 000:147.416   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T82D4 000:148.142   ROMTbl[0][0]: E000E000, CID: B105E00D, PID: 000BB00C SCS-M7
T82D4 000:148.256   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T82D4 000:148.920   ROMTbl[0][1]: E0001000, CID: B105E00D, PID: 003BB002 DWT
T82D4 000:148.962   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T82D4 000:149.559   ROMTbl[0][2]: E0002000, CID: B105E00D, PID: 002BB003 FPB
T82D4 000:149.593   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T82D4 000:150.237   ROMTbl[0][3]: ********, CID: B105E00D, PID: 003BB001 ITM
T82D4 000:150.311   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T82D4 000:150.868   ROMTbl[0][4]: ********, CID: B105900D, PID: 000BB9A1 TPIU
T82D4 000:150.904   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T82D4 000:151.532   ROMTbl[0][5]: ********, CID: 00000000, PID: 00000000 ???
T82D4 000:151.864 - 115.590ms returns 0x2BA01477
T82D4 000:151.892 JLINK_GetDLLVersion()
T82D4 000:151.909 - 0.025ms returns 72202
T82D4 000:151.987 JLINK_CORE_GetFound()
T82D4 000:152.011 - 0.032ms returns 0xE0000FF
T82D4 000:152.029 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.048   Value=0xE00FF000
T82D4 000:152.073 - 0.051ms returns 0
T82D4 000:152.097 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T82D4 000:152.117   Value=0xE00FF000
T82D4 000:152.142 - 0.053ms returns 0
T82D4 000:152.161 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T82D4 000:152.179   Value=0x00000000
T82D4 000:152.203 - 0.050ms returns 0
T82D4 000:152.230 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T82D4 000:152.267   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T82D4 000:152.802   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T82D4 000:152.833 - 0.610ms returns 16 (0x10)
T82D4 000:152.852 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T82D4 000:152.871   Value=0x00000000
T82D4 000:152.897 - 0.052ms returns 0
T82D4 000:152.914 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T82D4 000:152.930   Value=0x********
T82D4 000:152.956 - 0.050ms returns 0
T82D4 000:152.974 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T82D4 000:152.991   Value=0x********
T82D4 000:153.016 - 0.050ms returns 0
T82D4 000:153.033 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T82D4 000:153.051   Value=0xE0001000
T82D4 000:153.074 - 0.049ms returns 0
T82D4 000:153.091 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T82D4 000:153.109   Value=0xE0002000
T82D4 000:153.133 - 0.049ms returns 0
T82D4 000:153.150 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T82D4 000:153.168   Value=0xE000E000
T82D4 000:153.192 - 0.050ms returns 0
T82D4 000:153.210 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T82D4 000:153.227   Value=0xE000EDF0
T82D4 000:153.251 - 0.050ms returns 0
T82D4 000:153.270 JLINK_GetDebugInfo(0x01 = Unknown)
T82D4 000:153.287   Value=0x00000001
T82D4 000:153.311 - 0.050ms returns 0
T82D4 000:153.329 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T82D4 000:153.350   CPU_ReadMem(4 bytes @ 0xE000ED00)
T82D4 000:153.681   Data:  41 C2 0F 41
T82D4 000:153.712   Debug reg: CPUID
T82D4 000:153.737 - 0.416ms returns 1 (0x1)
T82D4 000:153.755 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T82D4 000:153.773   Value=0x00000000
T82D4 000:153.798 - 0.050ms returns 0
T82D4 000:153.815 JLINK_HasError()
T82D4 000:153.834 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:153.851 - 0.025ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:153.870 JLINK_Reset()
T82D4 000:153.897   CPU is running
T82D4 000:153.926   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:154.245   CPU is running
T82D4 000:154.276   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:154.633   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:154.935   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:154.965   CPU is running
T82D4 000:154.991   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:207.578   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:208.263   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:208.900   CPU is running
T82D4 000:208.978   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:209.421   CPU is running
T82D4 000:209.553   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:215.192   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:219.239   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:219.707   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:220.148   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:220.556 - 66.698ms
T82D4 000:220.676 JLINK_HasError()
T82D4 000:220.709 JLINK_ReadReg(R15 (PC))
T82D4 000:220.738 - 0.038ms returns 0x080001C4
T82D4 000:220.757 JLINK_ReadReg(XPSR)
T82D4 000:220.775 - 0.027ms returns 0x01000000
T82D4 000:220.794 JLINK_Halt()
T82D4 000:220.811 - 0.024ms returns 0x00
T82D4 000:220.829 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T82D4 000:220.852   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:221.195   Data:  03 00 03 00
T82D4 000:221.224   Debug reg: DHCSR
T82D4 000:221.248 - 0.427ms returns 1 (0x1)
T82D4 000:221.266 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T82D4 000:221.287   Debug reg: DHCSR
T82D4 000:221.567   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:222.001 - 0.747ms returns 0 (0x00000000)
T82D4 000:222.025 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T82D4 000:222.042   Debug reg: DEMCR
T82D4 000:222.072   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:222.524 - 0.511ms returns 0 (0x00000000)
T82D4 000:222.577 JLINK_GetHWStatus(...)
T82D4 000:222.828 - 0.312ms returns 0
T82D4 000:222.936 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T82D4 000:222.973 - 0.046ms returns 0x06
T82D4 000:222.991 JLINK_GetNumBPUnits(Type = 0xF0)
T82D4 000:223.009 - 0.026ms returns 0x2000
T82D4 000:223.026 JLINK_GetNumWPUnits()
T82D4 000:223.044 - 0.026ms returns 4
T82D4 000:223.070 JLINK_GetSpeed()
T82D4 000:223.090 - 0.028ms returns 2000
T82D4 000:223.113 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.137   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.456   Data:  02 00 00 00
T82D4 000:223.486 - 0.381ms returns 1 (0x1)
T82D4 000:223.505 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T82D4 000:223.526   CPU_ReadMem(4 bytes @ 0xE000E004)
T82D4 000:223.938   Data:  02 00 00 00
T82D4 000:223.995 - 0.521ms returns 1 (0x1)
T82D4 000:224.039 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T82D4 000:224.073   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 000:224.146   CPU_WriteMem(28 bytes @ 0xE0001000)
T82D4 000:224.707 - 0.685ms returns 0x1C
T82D4 000:224.743 JLINK_HasError()
T82D4 000:224.763 JLINK_ReadReg(R15 (PC))
T82D4 000:224.782 - 0.029ms returns 0x080001C4
T82D4 000:224.801 JLINK_ReadReg(XPSR)
T82D4 000:224.820 - 0.027ms returns 0x01000000
T82D4 000:228.429 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T82D4 000:228.469   Data:  00 00 00 00
T82D4 000:228.496   Debug reg: DWT_CYCCNT
T82D4 000:228.522 - 0.102ms returns 4 (0x4)
T82D4 000:311.515 JLINK_HasError()
T82D4 000:311.575 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T82D4 000:311.594 - 0.029ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T82D4 000:311.614 JLINK_Reset()
T82D4 000:311.642   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:311.993   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:312.448   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T82D4 000:312.778   Reset: Reset device via AIRCR.SYSRESETREQ.
T82D4 000:312.811   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T82D4 000:365.642   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:365.991   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:366.384   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T82D4 000:366.720   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T82D4 000:372.869   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T82D4 000:376.679   CPU_WriteMem(4 bytes @ 0xE0002000)
T82D4 000:377.055   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T82D4 000:377.384   CPU_ReadMem(4 bytes @ 0xE0001000)
T82D4 000:377.710 - 66.110ms
T82D4 000:377.867 JLINK_HasError()
T82D4 000:377.890 JLINK_ReadReg(R15 (PC))
T82D4 000:377.913 - 0.033ms returns 0x080001C4
T82D4 000:377.934 JLINK_ReadReg(XPSR)
T82D4 000:377.953 - 0.028ms returns 0x01000000
T82D4 000:378.064 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:378.093   CPU_ReadMem(64 bytes @ 0x080001C0)
T82D4 000:378.884    -- Updating C cache (64 bytes @ 0x080001C0)
T82D4 000:378.919    -- Read from C cache (60 bytes @ 0x080001C4)
T82D4 000:378.949   Data:  06 48 80 47 06 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:378.976 - 0.920ms returns 60 (0x3C)
T82D4 000:378.995 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:379.017    -- Read from C cache (2 bytes @ 0x080001C4)
T82D4 000:379.044   Data:  06 48
T82D4 000:379.072 - 0.085ms returns 2 (0x2)
T82D4 000:379.132 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:379.154    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:379.181   Data:  80 47
T82D4 000:379.207 - 0.085ms returns 2 (0x2)
T82D4 000:379.258 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:379.280    -- Read from C cache (2 bytes @ 0x080001C6)
T82D4 000:379.307   Data:  80 47
T82D4 000:379.334 - 0.084ms returns 2 (0x2)
T82D4 000:379.353 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:379.375   CPU_ReadMem(64 bytes @ 0x08000200)
T82D4 000:380.128    -- Updating C cache (64 bytes @ 0x08000200)
T82D4 000:380.161    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:380.190   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:380.215 - 0.871ms returns 60 (0x3C)
T82D4 000:380.235 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:380.256    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:380.282   Data:  06 48
T82D4 000:380.311 - 0.085ms returns 2 (0x2)
T82D4 000:380.347 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:380.367    -- Read from C cache (60 bytes @ 0x080001C8)
T82D4 000:380.396   Data:  06 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:380.422 - 0.083ms returns 60 (0x3C)
T82D4 000:380.441 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:380.461    -- Read from C cache (2 bytes @ 0x080001C8)
T82D4 000:380.488   Data:  06 48
T82D4 000:380.516 - 0.084ms returns 2 (0x2)
T82D4 000:380.535 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:380.553    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:380.582   Data:  00 47
T82D4 000:380.608 - 0.081ms returns 2 (0x2)
T82D4 000:380.635 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:380.657    -- Read from C cache (2 bytes @ 0x080001CA)
T82D4 000:380.684   Data:  00 47
T82D4 000:380.711 - 0.086ms returns 2 (0x2)
T82D4 000:380.731 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:380.749    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:380.778   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:380.804 - 0.082ms returns 60 (0x3C)
T82D4 000:380.823 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:380.845    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:380.872   Data:  FE E7
T82D4 000:380.901 - 0.086ms returns 2 (0x2)
T82D4 000:380.926 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T82D4 000:380.947    -- Read from C cache (60 bytes @ 0x080001CC)
T82D4 000:380.976   Data:  FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T82D4 000:381.002 - 0.084ms returns 60 (0x3C)
T82D4 000:381.021 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:381.041    -- Read from C cache (2 bytes @ 0x080001CC)
T82D4 000:381.067   Data:  FE E7
T82D4 000:381.096 - 0.083ms returns 2 (0x2)
T82D4 000:381.114 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T82D4 000:381.134    -- Read from C cache (2 bytes @ 0x080001CE)
T82D4 000:381.161   Data:  FE E7
T82D4 000:381.187 - 0.081ms returns 2 (0x2)
T82D4 001:496.331 JLINK_ReadMemEx(0x08016700, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:496.378   CPU_ReadMem(64 bytes @ 0x08016700)
T82D4 001:497.160    -- Updating C cache (64 bytes @ 0x08016700)
T82D4 001:497.193    -- Read from C cache (60 bytes @ 0x08016700)
T82D4 001:497.222   Data:  8E B0 00 24 F9 F7 68 FB F8 F7 1A FF F5 F7 1E F9 ...
T82D4 001:497.250 - 0.987ms returns 60 (0x3C)
T82D4 001:497.340 JLINK_ReadMemEx(0x08016700, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:497.372    -- Read from C cache (2 bytes @ 0x08016700)
T82D4 001:497.410   Data:  8E B0
T82D4 001:497.447 - 0.119ms returns 2 (0x2)
T82D4 001:497.522 JLINK_ReadMemEx(0x08016702, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:497.547    -- Read from C cache (2 bytes @ 0x08016702)
T82D4 001:497.573   Data:  00 24
T82D4 001:497.601 - 0.089ms returns 2 (0x2)
T82D4 001:497.651 JLINK_ReadMemEx(0x08016702, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:497.682    -- Read from C cache (2 bytes @ 0x08016702)
T82D4 001:497.710   Data:  00 24
T82D4 001:497.739 - 0.097ms returns 2 (0x2)
T82D4 001:497.758 JLINK_ReadMemEx(0x08016704, 0x3C Bytes, Flags = 0x02000000)
T82D4 001:497.778    -- Read from C cache (60 bytes @ 0x08016704)
T82D4 001:497.806   Data:  F9 F7 68 FB F8 F7 1A FF F5 F7 1E F9 4F F4 7A 70 ...
T82D4 001:497.832 - 0.083ms returns 60 (0x3C)
T82D4 001:497.852 JLINK_ReadMemEx(0x08016704, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:497.872    -- Read from C cache (2 bytes @ 0x08016704)
T82D4 001:497.899   Data:  F9 F7
T82D4 001:497.927 - 0.084ms returns 2 (0x2)
T82D4 001:668.329 JLINK_HasError()
T82D4 001:668.393 JLINK_ReadReg(R0)
T82D4 001:668.750 - 0.381ms returns 0x00000003
T82D4 001:668.789 JLINK_ReadReg(R1)
T82D4 001:668.810 - 0.031ms returns 0x20022910
T82D4 001:668.831 JLINK_ReadReg(R2)
T82D4 001:668.850 - 0.028ms returns 0x080159BD
T82D4 001:668.870 JLINK_ReadReg(R3)
T82D4 001:668.890 - 0.028ms returns 0x080159BD
T82D4 001:668.909 JLINK_ReadReg(R4)
T82D4 001:668.929 - 0.029ms returns 0x0000003D
T82D4 001:668.949 JLINK_ReadReg(R5)
T82D4 001:668.968 - 0.029ms returns 0x00000000
T82D4 001:668.989 JLINK_ReadReg(R6)
T82D4 001:669.007 - 0.027ms returns 0x0800D95C
T82D4 001:669.027 JLINK_ReadReg(R7)
T82D4 001:669.047 - 0.029ms returns 0x00000000
T82D4 001:669.066 JLINK_ReadReg(R8)
T82D4 001:669.099 - 0.043ms returns 0x200229B4
T82D4 001:669.120 JLINK_ReadReg(R9)
T82D4 001:669.139 - 0.029ms returns 0x20000348
T82D4 001:669.159 JLINK_ReadReg(R10)
T82D4 001:669.178 - 0.028ms returns 0x080159BD
T82D4 001:669.199 JLINK_ReadReg(R11)
T82D4 001:669.218 - 0.030ms returns 0x00000000
T82D4 001:669.238 JLINK_ReadReg(R12)
T82D4 001:669.258 - 0.029ms returns 0x00000000
T82D4 001:669.278 JLINK_ReadReg(R13 (SP))
T82D4 001:669.298 - 0.029ms returns 0x20008038
T82D4 001:669.318 JLINK_ReadReg(R14)
T82D4 001:669.336 - 0.028ms returns 0xFFFFFFFF
T82D4 001:669.357 JLINK_ReadReg(R15 (PC))
T82D4 001:669.377 - 0.030ms returns 0x080001C4
T82D4 001:669.399 JLINK_ReadReg(XPSR)
T82D4 001:669.418 - 0.029ms returns 0x01000000
T82D4 001:669.438 JLINK_ReadReg(MSP)
T82D4 001:669.458 - 0.028ms returns 0x20008038
T82D4 001:669.477 JLINK_ReadReg(PSP)
T82D4 001:669.497 - 0.030ms returns 0x20001000
T82D4 001:669.518 JLINK_ReadReg(CFBP)
T82D4 001:669.538 - 0.028ms returns 0x00000000
T82D4 001:669.558 JLINK_ReadReg(FPSCR)
T82D4 001:674.977 - 5.449ms returns 0x00000000
T82D4 001:675.020 JLINK_ReadReg(FPS0)
T82D4 001:675.043 - 0.032ms returns 0x00000000
T82D4 001:675.063 JLINK_ReadReg(FPS1)
T82D4 001:675.083 - 0.030ms returns 0x3FF00000
T82D4 001:675.103 JLINK_ReadReg(FPS2)
T82D4 001:675.122 - 0.028ms returns 0x48164000
T82D4 001:675.143 JLINK_ReadReg(FPS3)
T82D4 001:675.163 - 0.030ms returns 0x48020358
T82D4 001:675.183 JLINK_ReadReg(FPS4)
T82D4 001:675.203 - 0.029ms returns 0x001E0018
T82D4 001:675.222 JLINK_ReadReg(FPS5)
T82D4 001:675.242 - 0.029ms returns 0x58C90680
T82D4 001:675.262 JLINK_ReadReg(FPS6)
T82D4 001:675.280 - 0.027ms returns 0x10867400
T82D4 001:675.301 JLINK_ReadReg(FPS7)
T82D4 001:675.322 - 0.030ms returns 0x62464681
T82D4 001:675.341 JLINK_ReadReg(FPS8)
T82D4 001:675.362 - 0.032ms returns 0x183008E3
T82D4 001:675.384 JLINK_ReadReg(FPS9)
T82D4 001:675.404 - 0.030ms returns 0x4CE0F726
T82D4 001:675.424 JLINK_ReadReg(FPS10)
T82D4 001:675.442 - 0.029ms returns 0x490D9850
T82D4 001:675.464 JLINK_ReadReg(FPS11)
T82D4 001:675.483 - 0.028ms returns 0x0301B320
T82D4 001:675.502 JLINK_ReadReg(FPS12)
T82D4 001:675.531 - 0.038ms returns 0x8C6080C8
T82D4 001:675.551 JLINK_ReadReg(FPS13)
T82D4 001:675.572 - 0.030ms returns 0x5D944171
T82D4 001:675.591 JLINK_ReadReg(FPS14)
T82D4 001:675.610 - 0.028ms returns 0x4501C880
T82D4 001:675.631 JLINK_ReadReg(FPS15)
T82D4 001:675.650 - 0.028ms returns 0x185C0000
T82D4 001:675.669 JLINK_ReadReg(FPS16)
T82D4 001:675.690 - 0.030ms returns 0x80A80000
T82D4 001:675.710 JLINK_ReadReg(FPS17)
T82D4 001:675.730 - 0.030ms returns 0x58092430
T82D4 001:675.750 JLINK_ReadReg(FPS18)
T82D4 001:675.768 - 0.029ms returns 0xEF80024E
T82D4 001:675.790 JLINK_ReadReg(FPS19)
T82D4 001:675.809 - 0.028ms returns 0x4820B903
T82D4 001:675.828 JLINK_ReadReg(FPS20)
T82D4 001:675.849 - 0.030ms returns 0x02052200
T82D4 001:675.869 JLINK_ReadReg(FPS21)
T82D4 001:675.889 - 0.030ms returns 0x2804C838
T82D4 001:675.909 JLINK_ReadReg(FPS22)
T82D4 001:675.927 - 0.028ms returns 0x58016426
T82D4 001:675.949 JLINK_ReadReg(FPS23)
T82D4 001:675.968 - 0.028ms returns 0x89303CF8
T82D4 001:675.987 JLINK_ReadReg(FPS24)
T82D4 001:676.008 - 0.030ms returns 0x8604020A
T82D4 001:676.027 JLINK_ReadReg(FPS25)
T82D4 001:676.048 - 0.030ms returns 0xC0190403
T82D4 001:676.067 JLINK_ReadReg(FPS26)
T82D4 001:676.086 - 0.028ms returns 0x202A04C0
T82D4 001:676.107 JLINK_ReadReg(FPS27)
T82D4 001:676.127 - 0.029ms returns 0x150C25A2
T82D4 001:676.146 JLINK_ReadReg(FPS28)
T82D4 001:676.167 - 0.030ms returns 0x614840F3
T82D4 001:676.186 JLINK_ReadReg(FPS29)
T82D4 001:676.208 - 0.031ms returns 0x01487A60
T82D4 001:676.227 JLINK_ReadReg(FPS30)
T82D4 001:676.246 - 0.028ms returns 0x0247A962
T82D4 001:676.267 JLINK_ReadReg(FPS31)
T82D4 001:676.286 - 0.028ms returns 0x00CC00A3
T82D4 001:732.392 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:732.466   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 001:733.262    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 001:733.307    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:733.335   Data:  00 00
T82D4 001:733.362 - 0.979ms returns 2 (0x2)
T82D4 001:733.446 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:733.474    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:733.501   Data:  00 00
T82D4 001:733.528 - 0.091ms returns 2 (0x2)
T82D4 001:733.562 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 001:733.586    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 001:733.613   Data:  00 00
T82D4 001:733.641 - 0.088ms returns 2 (0x2)
T82D4 001:741.582 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:741.656    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:741.684   Data:  00 00 00 00
T82D4 001:741.712 - 0.139ms returns 4 (0x4)
T82D4 001:741.747 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:741.771    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:741.798   Data:  00 00 00 00
T82D4 001:741.825 - 0.088ms returns 4 (0x4)
T82D4 001:741.862 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:741.884    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 001:741.912   Data:  00 00 00 00
T82D4 001:741.940 - 0.086ms returns 4 (0x4)
T82D4 001:756.290 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:756.360    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:756.388   Data:  00 00 00 00
T82D4 001:756.414 - 0.131ms returns 4 (0x4)
T82D4 001:756.447 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:756.471    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:756.497   Data:  00 00 00 00
T82D4 001:756.523 - 0.084ms returns 4 (0x4)
T82D4 001:756.600 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:756.621    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 001:756.649   Data:  00 00 00 00
T82D4 001:756.674 - 0.083ms returns 4 (0x4)
T82D4 001:770.031 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:770.094    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:770.171   Data:  00 00 00 00
T82D4 001:770.235 - 0.214ms returns 4 (0x4)
T82D4 001:770.281 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:770.310    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:770.337   Data:  00 00 00 00
T82D4 001:770.364 - 0.093ms returns 4 (0x4)
T82D4 001:770.399 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:770.422    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 001:770.450   Data:  00 00 00 00
T82D4 001:770.477 - 0.087ms returns 4 (0x4)
T82D4 001:779.172 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:779.237    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:779.265   Data:  00 00 00 00
T82D4 001:779.292 - 0.129ms returns 4 (0x4)
T82D4 001:779.326 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:779.350    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:779.377   Data:  00 00 00 00
T82D4 001:779.404 - 0.087ms returns 4 (0x4)
T82D4 001:779.437 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:779.460    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:779.488   Data:  00 00 00 00
T82D4 001:779.514 - 0.085ms returns 4 (0x4)
T82D4 001:787.626 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:787.686    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:787.716   Data:  00 00 00 00
T82D4 001:787.742 - 0.125ms returns 4 (0x4)
T82D4 001:787.774 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:787.798    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:787.825   Data:  00 00 00 00
T82D4 001:787.853 - 0.087ms returns 4 (0x4)
T82D4 001:787.885 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:787.907    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 001:787.935   Data:  00 00 00 00
T82D4 001:787.961 - 0.084ms returns 4 (0x4)
T82D4 001:843.506 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:843.578   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 001:844.439    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 001:844.484    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:844.511   Data:  00 00 00 00
T82D4 001:844.539 - 1.042ms returns 4 (0x4)
T82D4 001:844.625 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:844.652    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:844.679   Data:  00 00 00 00
T82D4 001:844.707 - 0.091ms returns 4 (0x4)
T82D4 001:844.741 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:844.763    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 001:844.792   Data:  00 00 00 00
T82D4 001:844.818 - 0.086ms returns 4 (0x4)
T82D4 001:854.554 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:854.615    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:854.645   Data:  00 00 00 00
T82D4 001:854.671 - 0.125ms returns 4 (0x4)
T82D4 001:854.706 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:854.730    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:854.757   Data:  00 00 00 00
T82D4 001:854.784 - 0.086ms returns 4 (0x4)
T82D4 001:854.817 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:854.839    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 001:854.874   Data:  00 00 00 00
T82D4 001:854.905 - 0.096ms returns 4 (0x4)
T82D4 001:865.167 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:865.238   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 001:866.086    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 001:866.129    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:866.158   Data:  00 00 00 00
T82D4 001:866.184 - 1.026ms returns 4 (0x4)
T82D4 001:866.231 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:866.257    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:866.284   Data:  00 00 00 00
T82D4 001:866.313 - 0.090ms returns 4 (0x4)
T82D4 001:866.346 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:866.369    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 001:866.403   Data:  00 00 00 00
T82D4 001:866.430 - 0.092ms returns 4 (0x4)
T82D4 001:876.589 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:876.655   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 001:877.465    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 001:877.507    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:877.533   Data:  00 00 00 00
T82D4 001:877.561 - 0.981ms returns 4 (0x4)
T82D4 001:877.604 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:877.630    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:877.657   Data:  00 00 00 00
T82D4 001:877.683 - 0.089ms returns 4 (0x4)
T82D4 001:877.718 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:877.741    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 001:877.769   Data:  00 00 00 00
T82D4 001:877.796 - 0.086ms returns 4 (0x4)
T82D4 001:888.345 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:888.400    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:888.431   Data:  00 00 00 00
T82D4 001:888.459 - 0.123ms returns 4 (0x4)
T82D4 001:888.512 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:888.538    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:888.566   Data:  00 00 00 00
T82D4 001:888.595 - 0.091ms returns 4 (0x4)
T82D4 001:888.631 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:888.654    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 001:888.683   Data:  00 00 00 00
T82D4 001:888.709 - 0.086ms returns 4 (0x4)
T82D4 001:901.441 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:901.498   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 001:902.377    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 001:902.495    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:902.523   Data:  71
T82D4 001:902.549 - 1.117ms returns 1 (0x1)
T82D4 001:902.594 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:902.620    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:902.648   Data:  71
T82D4 001:902.675 - 0.090ms returns 1 (0x1)
T82D4 001:902.713 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 001:902.736    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 001:902.763   Data:  71
T82D4 001:902.789 - 0.085ms returns 1 (0x1)
T82D4 001:913.451 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 001:913.519   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 001:914.454    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 001:914.486    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 001:914.513   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 001:914.538 - 1.095ms returns 32 (0x20)
T82D4 001:989.701 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:989.766    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:989.793   Data:  00 00 00 00
T82D4 001:989.818 - 0.125ms returns 4 (0x4)
T82D4 001:989.851 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:989.873    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:989.898   Data:  00 00 00 00
T82D4 001:989.924 - 0.081ms returns 4 (0x4)
T82D4 001:989.980 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 001:990.001    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 001:990.027   Data:  00 00 00 00
T82D4 001:990.052 - 0.080ms returns 4 (0x4)
T82D4 002:001.739 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:001.789    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:001.815   Data:  00 00 00 00
T82D4 002:001.841 - 0.110ms returns 4 (0x4)
T82D4 002:001.871 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:001.892    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:001.919   Data:  00 00 00 00
T82D4 002:001.943 - 0.080ms returns 4 (0x4)
T82D4 002:001.972 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:001.993    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:002.019   Data:  00 00 00 00
T82D4 002:002.044 - 0.142ms returns 4 (0x4)
T82D4 002:013.587 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:013.646    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:013.673   Data:  00 00 00 00
T82D4 002:013.698 - 0.119ms returns 4 (0x4)
T82D4 002:013.730 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:013.753    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:013.778   Data:  00 00 00 00
T82D4 002:013.804 - 0.082ms returns 4 (0x4)
T82D4 002:013.835 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:013.855    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:013.881   Data:  00 00 00 00
T82D4 002:013.905 - 0.079ms returns 4 (0x4)
T82D4 002:078.300 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:078.445    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:078.472   Data:  71
T82D4 002:078.498 - 0.207ms returns 1 (0x1)
T82D4 002:078.539 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:078.562    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:078.587   Data:  71
T82D4 002:078.611 - 0.080ms returns 1 (0x1)
T82D4 002:078.645 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:078.665    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:078.691   Data:  71
T82D4 002:078.717 - 0.080ms returns 1 (0x1)
T82D4 002:166.799 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:166.861    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:166.889   Data:  00 00 00 00
T82D4 002:166.914 - 0.123ms returns 4 (0x4)
T82D4 002:166.947 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:166.969    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:166.994   Data:  00 00 00 00
T82D4 002:167.020 - 0.082ms returns 4 (0x4)
T82D4 002:167.054 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:167.075    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:167.101   Data:  00 00 00 00
T82D4 002:167.125 - 0.080ms returns 4 (0x4)
T82D4 002:180.042 JLINK_ReadMemEx(0x08018DD8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:180.103   CPU_ReadMem(64 bytes @ 0x08018DC0)
T82D4 002:181.164    -- Updating C cache (64 bytes @ 0x08018DC0)
T82D4 002:181.204    -- Read from C cache (8 bytes @ 0x08018DD8)
T82D4 002:181.230   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:181.255 - 1.221ms returns 8 (0x8)
T82D4 002:181.292 JLINK_ReadMemEx(0x08018DD8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:181.314    -- Read from C cache (8 bytes @ 0x08018DD8)
T82D4 002:181.339   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:181.365 - 0.081ms returns 8 (0x8)
T82D4 002:181.398 JLINK_ReadMemEx(0x08018DD8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:181.419    -- Read from C cache (8 bytes @ 0x08018DD8)
T82D4 002:181.445   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:181.469 - 0.078ms returns 8 (0x8)
T82D4 002:206.579 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:206.648   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:207.564    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:207.625    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:207.651   Data:  00 00 00 00
T82D4 002:207.705 - 1.134ms returns 4 (0x4)
T82D4 002:207.804 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:207.839    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:207.866   Data:  00 00 00 00
T82D4 002:207.890 - 0.093ms returns 4 (0x4)
T82D4 002:207.925 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:207.947    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:207.972   Data:  00 00 00 00
T82D4 002:207.999 - 0.081ms returns 4 (0x4)
T82D4 002:281.329 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:281.392    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:281.418   Data:  00 00 00 00
T82D4 002:281.444 - 0.123ms returns 4 (0x4)
T82D4 002:281.473 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:281.495    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:281.527   Data:  00 00 00 00
T82D4 002:281.552 - 0.087ms returns 4 (0x4)
T82D4 002:281.580 JLINK_ReadMemEx(0x20000574, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:281.601    -- Read from C cache (4 bytes @ 0x20000574)
T82D4 002:281.626   Data:  00 00 00 00
T82D4 002:281.651 - 0.080ms returns 4 (0x4)
T7844 002:325.107 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T7844 002:325.261    -- Read from C cache (2 bytes @ 0x080001C4)
T7844 002:325.308   Data:  06 48
T7844 002:325.334 - 0.249ms returns 2 (0x2)
T7844 002:325.388 JLINK_HasError()
T7844 002:325.411 JLINK_SetBPEx(Addr = 0x08016700, Type = 0xFFFFFFF2)
T7844 002:325.449 - 0.046ms returns 0x00000001
T7844 002:325.467 JLINK_HasError()
T7844 002:325.485 JLINK_HasError()
T7844 002:325.504 JLINK_Go()
T7844 002:325.914   CPU_WriteMem(4 bytes @ 0xE0002000)
T7844 002:326.293   CPU_ReadMem(4 bytes @ 0xE0001000)
T7844 002:326.622   CPU_WriteMem(4 bytes @ 0xE0002008)
T7844 002:326.673   CPU_WriteMem(4 bytes @ 0xE000200C)
T7844 002:326.700   CPU_WriteMem(4 bytes @ 0xE0002010)
T7844 002:326.742   CPU_WriteMem(4 bytes @ 0xE0002014)
T7844 002:326.768   CPU_WriteMem(4 bytes @ 0xE0002018)
T7844 002:326.793   CPU_WriteMem(4 bytes @ 0xE000201C)
T7844 002:328.249   CPU_WriteMem(4 bytes @ 0xE0001004)
T7844 002:329.083 - 3.598ms
T7844 002:430.124 JLINK_HasError()
T7844 002:430.209 JLINK_IsHalted()
T7844 002:430.664 - 0.480ms returns FALSE
T7844 002:531.496 JLINK_HasError()
T7844 002:531.555 JLINK_IsHalted()
T7844 002:535.041 - 3.500ms returns TRUE
T7844 002:535.067 JLINK_HasError()
T7844 002:535.086 JLINK_Halt()
T7844 002:535.105 - 0.027ms returns 0x00
T7844 002:535.123 JLINK_IsHalted()
T7844 002:535.142 - 0.027ms returns TRUE
T7844 002:535.160 JLINK_IsHalted()
T7844 002:535.177 - 0.027ms returns TRUE
T7844 002:535.197 JLINK_IsHalted()
T7844 002:535.215 - 0.026ms returns TRUE
T7844 002:535.233 JLINK_HasError()
T7844 002:535.253 JLINK_ReadReg(R15 (PC))
T7844 002:535.273 - 0.030ms returns 0x08016700
T7844 002:535.294 JLINK_ReadReg(XPSR)
T7844 002:535.313 - 0.027ms returns 0x61000000
T7844 002:535.333 JLINK_HasError()
T7844 002:535.353 JLINK_ClrBPEx(BPHandle = 0x00000001)
T7844 002:535.372 - 0.029ms returns 0x00
T7844 002:535.393 JLINK_HasError()
T7844 002:535.411 JLINK_HasError()
T7844 002:535.431 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T7844 002:535.479   CPU_ReadMem(4 bytes @ 0xE000ED30)
T7844 002:535.913   Data:  02 00 00 00
T7844 002:535.951 - 0.540ms returns 1 (0x1)
T7844 002:535.983 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T7844 002:536.006   CPU_ReadMem(4 bytes @ 0xE0001028)
T7844 002:536.491   Data:  00 00 00 00
T7844 002:536.543   Debug reg: DWT_FUNC[0]
T7844 002:536.572 - 0.597ms returns 1 (0x1)
T7844 002:536.591 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T7844 002:536.615   CPU_ReadMem(4 bytes @ 0xE0001038)
T7844 002:536.965   Data:  00 02 00 00
T7844 002:537.004   Debug reg: DWT_FUNC[1]
T7844 002:537.032 - 0.448ms returns 1 (0x1)
T7844 002:537.052 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T7844 002:537.075   CPU_ReadMem(4 bytes @ 0xE0001048)
T7844 002:537.418   Data:  00 00 00 00
T7844 002:537.474   Debug reg: DWT_FUNC[2]
T7844 002:537.516 - 0.473ms returns 1 (0x1)
T7844 002:537.535 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T7844 002:537.557   CPU_ReadMem(4 bytes @ 0xE0001058)
T7844 002:538.029   Data:  00 00 00 00
T7844 002:538.061   Debug reg: DWT_FUNC[3]
T7844 002:538.086 - 0.560ms returns 1 (0x1)
T7844 002:538.256 JLINK_HasError()
T7844 002:538.277 JLINK_ReadReg(R0)
T7844 002:538.299 - 0.030ms returns 0x08016701
T7844 002:538.318 JLINK_ReadReg(R1)
T7844 002:538.338 - 0.029ms returns 0x20022A08
T7844 002:538.358 JLINK_ReadReg(R2)
T7844 002:538.381 - 0.032ms returns 0x00000000
T7844 002:538.401 JLINK_ReadReg(R3)
T7844 002:538.439 - 0.046ms returns 0x08012E29
T7844 002:538.457 JLINK_ReadReg(R4)
T7844 002:538.477 - 0.029ms returns 0x08019348
T7844 002:538.496 JLINK_ReadReg(R5)
T7844 002:538.515 - 0.028ms returns 0x08019348
T7844 002:538.533 JLINK_ReadReg(R6)
T7844 002:538.551 - 0.030ms returns 0x0800D95C
T7844 002:538.576 JLINK_ReadReg(R7)
T7844 002:538.595 - 0.027ms returns 0x00000000
T7844 002:538.613 JLINK_ReadReg(R8)
T7844 002:538.633 - 0.029ms returns 0x200229B4
T7844 002:538.652 JLINK_ReadReg(R9)
T7844 002:538.671 - 0.028ms returns 0x20000348
T7844 002:538.690 JLINK_ReadReg(R10)
T7844 002:538.708 - 0.027ms returns 0x080159BD
T7844 002:538.727 JLINK_ReadReg(R11)
T7844 002:538.745 - 0.026ms returns 0x00000000
T7844 002:538.764 JLINK_ReadReg(R12)
T7844 002:538.783 - 0.028ms returns 0x00000000
T7844 002:538.802 JLINK_ReadReg(R13 (SP))
T7844 002:538.822 - 0.028ms returns 0x20022A08
T7844 002:538.840 JLINK_ReadReg(R14)
T7844 002:538.858 - 0.027ms returns 0x08004E6D
T7844 002:538.878 JLINK_ReadReg(R15 (PC))
T7844 002:538.896 - 0.026ms returns 0x08016700
T7844 002:538.915 JLINK_ReadReg(XPSR)
T7844 002:538.934 - 0.029ms returns 0x61000000
T7844 002:538.954 JLINK_ReadReg(MSP)
T7844 002:538.973 - 0.028ms returns 0x20022A08
T7844 002:538.992 JLINK_ReadReg(PSP)
T7844 002:539.010 - 0.027ms returns 0x20001000
T7844 002:539.029 JLINK_ReadReg(CFBP)
T7844 002:539.047 - 0.026ms returns 0x00000000
T7844 002:539.065 JLINK_ReadReg(FPSCR)
T7844 002:544.557 - 5.524ms returns 0x00000000
T7844 002:544.612 JLINK_ReadReg(FPS0)
T7844 002:544.643 - 0.044ms returns 0x00000000
T7844 002:544.674 JLINK_ReadReg(FPS1)
T7844 002:544.705 - 0.042ms returns 0x3FF00000
T7844 002:544.732 JLINK_ReadReg(FPS2)
T7844 002:544.756 - 0.033ms returns 0x48164000
T7844 002:544.777 JLINK_ReadReg(FPS3)
T7844 002:544.835 - 0.067ms returns 0x48020358
T7844 002:544.856 JLINK_ReadReg(FPS4)
T7844 002:544.913 - 0.066ms returns 0x001E0018
T7844 002:544.934 JLINK_ReadReg(FPS5)
T7844 002:544.991 - 0.067ms returns 0x58C90680
T7844 002:545.012 JLINK_ReadReg(FPS6)
T7844 002:545.032 - 0.030ms returns 0x10867400
T7844 002:545.053 JLINK_ReadReg(FPS7)
T7844 002:545.073 - 0.044ms returns 0x62464681
T7844 002:545.129 JLINK_ReadReg(FPS8)
T7844 002:545.149 - 0.030ms returns 0x183008E3
T7844 002:545.170 JLINK_ReadReg(FPS9)
T7844 002:545.190 - 0.030ms returns 0x4CE0F726
T7844 002:545.211 JLINK_ReadReg(FPS10)
T7844 002:545.232 - 0.044ms returns 0x490D9850
T7844 002:545.266 JLINK_ReadReg(FPS11)
T7844 002:545.285 - 0.051ms returns 0x0301B320
T7844 002:545.328 JLINK_ReadReg(FPS12)
T7844 002:545.347 - 0.029ms returns 0x8C6080C8
T7844 002:545.369 JLINK_ReadReg(FPS13)
T7844 002:545.388 - 0.028ms returns 0x5D944171
T7844 002:545.410 JLINK_ReadReg(FPS14)
T7844 002:545.430 - 0.029ms returns 0x4501C880
T7844 002:545.452 JLINK_ReadReg(FPS15)
T7844 002:545.471 - 0.028ms returns 0x185C0000
T7844 002:545.491 JLINK_ReadReg(FPS16)
T7844 002:545.511 - 0.029ms returns 0x80A80000
T7844 002:545.532 JLINK_ReadReg(FPS17)
T7844 002:545.552 - 0.029ms returns 0x58092430
T7844 002:545.572 JLINK_ReadReg(FPS18)
T7844 002:545.607 - 0.066ms returns 0xEF80024E
T7844 002:545.649 JLINK_ReadReg(FPS19)
T7844 002:545.670 - 0.029ms returns 0x4820B903
T7844 002:545.690 JLINK_ReadReg(FPS20)
T7844 002:545.710 - 0.030ms returns 0x02052200
T7844 002:545.731 JLINK_ReadReg(FPS21)
T7844 002:545.752 - 0.031ms returns 0x2804C838
T7844 002:545.773 JLINK_ReadReg(FPS22)
T7844 002:545.794 - 0.030ms returns 0x58016426
T7844 002:545.814 JLINK_ReadReg(FPS23)
T7844 002:545.834 - 0.029ms returns 0x89303CF8
T7844 002:545.854 JLINK_ReadReg(FPS24)
T7844 002:545.875 - 0.029ms returns 0x8604020A
T7844 002:545.895 JLINK_ReadReg(FPS25)
T7844 002:545.914 - 0.029ms returns 0xC0190403
T7844 002:545.935 JLINK_ReadReg(FPS26)
T7844 002:545.954 - 0.028ms returns 0x202A04C0
T7844 002:545.976 JLINK_ReadReg(FPS27)
T7844 002:545.995 - 0.028ms returns 0x150C25A2
T7844 002:546.016 JLINK_ReadReg(FPS28)
T7844 002:546.036 - 0.029ms returns 0x614840F3
T7844 002:546.057 JLINK_ReadReg(FPS29)
T7844 002:546.077 - 0.029ms returns 0x01487A60
T7844 002:546.097 JLINK_ReadReg(FPS30)
T7844 002:546.117 - 0.029ms returns 0x0247A962
T7844 002:546.137 JLINK_ReadReg(FPS31)
T7844 002:546.157 - 0.029ms returns 0x00CC00A3
T82D4 002:546.381 JLINK_ReadMemEx(0x200229D4, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:546.429   CPU_ReadMem(64 bytes @ 0x200229C0)
T82D4 002:547.206    -- Updating C cache (64 bytes @ 0x200229C0)
T82D4 002:547.242    -- Read from C cache (32 bytes @ 0x200229D4)
T82D4 002:547.269   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:547.296 - 0.923ms returns 32 (0x20)
T82D4 002:548.407 JLINK_ReadMemEx(0x200229D4, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:548.444    -- Read from C cache (32 bytes @ 0x200229D4)
T82D4 002:548.472   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:548.498 - 0.099ms returns 32 (0x20)
T82D4 002:548.527 JLINK_HasError()
T82D4 002:548.548 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T82D4 002:548.572   CPU_ReadMem(4 bytes @ 0xE0001004)
T82D4 002:548.907   Data:  0E 37 23 00
T82D4 002:548.948   Debug reg: DWT_CYCCNT
T82D4 002:548.974 - 0.435ms returns 1 (0x1)
T82D4 002:550.687 JLINK_ReadMemEx(0x20019C5E, 0x2 Bytes, Flags = 0x02000000)
T82D4 002:550.722   CPU_ReadMem(64 bytes @ 0x20019C40)
T82D4 002:551.538    -- Updating C cache (64 bytes @ 0x20019C40)
T82D4 002:551.628    -- Read from C cache (2 bytes @ 0x20019C5E)
T82D4 002:551.657   Data:  00 00
T82D4 002:551.682 - 1.004ms returns 2 (0x2)
T82D4 002:551.709 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:551.733    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:551.760   Data:  00 00 00 00
T82D4 002:551.787 - 0.086ms returns 4 (0x4)
T82D4 002:552.293 JLINK_ReadMemEx(0x20019C5A, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.326    -- Read from C cache (4 bytes @ 0x20019C5A)
T82D4 002:552.352   Data:  00 00 00 00
T82D4 002:552.378 - 0.093ms returns 4 (0x4)
T82D4 002:552.410 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.434    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:552.461   Data:  00 00 00 00
T82D4 002:552.488 - 0.086ms returns 4 (0x4)
T82D4 002:552.511 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.533    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:552.559   Data:  00 00 00 00
T82D4 002:552.591 - 0.090ms returns 4 (0x4)
T82D4 002:552.616 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.637    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:552.663   Data:  00 00 00 00
T82D4 002:552.690 - 0.082ms returns 4 (0x4)
T82D4 002:552.730 JLINK_ReadMemEx(0x200005AC, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:552.754   CPU_ReadMem(64 bytes @ 0x20000580)
T82D4 002:553.692    -- Updating C cache (64 bytes @ 0x20000580)
T82D4 002:553.732    -- Read from C cache (4 bytes @ 0x200005AC)
T82D4 002:553.759   Data:  00 00 00 00
T82D4 002:553.785 - 1.064ms returns 4 (0x4)
T82D4 002:553.813 JLINK_ReadMemEx(0x200005B0, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:553.837    -- Read from C cache (4 bytes @ 0x200005B0)
T82D4 002:553.863   Data:  00 00 00 00
T82D4 002:553.890 - 0.085ms returns 4 (0x4)
T82D4 002:553.912 JLINK_ReadMemEx(0x20000568, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:553.934   CPU_ReadMem(64 bytes @ 0x20000540)
T82D4 002:554.908    -- Updating C cache (64 bytes @ 0x20000540)
T82D4 002:554.949    -- Read from C cache (4 bytes @ 0x20000568)
T82D4 002:554.991   Data:  00 00 00 00
T82D4 002:555.041 - 1.138ms returns 4 (0x4)
T82D4 002:555.084 JLINK_ReadMemEx(0x200005F4, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:555.110   CPU_ReadMem(64 bytes @ 0x200005C0)
T82D4 002:555.941    -- Updating C cache (64 bytes @ 0x200005C0)
T82D4 002:555.980    -- Read from C cache (4 bytes @ 0x200005F4)
T82D4 002:556.006   Data:  00 00 00 00
T82D4 002:556.033 - 0.958ms returns 4 (0x4)
T82D4 002:556.061 JLINK_ReadMemEx(0x20000570, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:556.085    -- Read from C cache (4 bytes @ 0x20000570)
T82D4 002:556.112   Data:  00 00 00 00
T82D4 002:556.138 - 0.086ms returns 4 (0x4)
T82D4 002:556.162 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:556.185   CPU_ReadMem(64 bytes @ 0x20000C40)
T82D4 002:557.145    -- Updating C cache (64 bytes @ 0x20000C40)
T82D4 002:557.190    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:557.216   Data:  00
T82D4 002:557.242 - 1.088ms returns 1 (0x1)
T82D4 002:557.494 JLINK_ReadMemEx(0x20019D5C, 0x20 Bytes, Flags = 0x02000000)
T82D4 002:557.525   CPU_ReadMem(64 bytes @ 0x20019D40)
T82D4 002:558.423    -- Updating C cache (64 bytes @ 0x20019D40)
T82D4 002:558.464    -- Read from C cache (32 bytes @ 0x20019D5C)
T82D4 002:558.490   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T82D4 002:558.517 - 1.032ms returns 32 (0x20)
T82D4 002:570.218 JLINK_ReadMemEx(0x20019C6C, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:570.279    -- Read from C cache (4 bytes @ 0x20019C6C)
T82D4 002:570.307   Data:  00 00 00 00
T82D4 002:570.334 - 0.125ms returns 4 (0x4)
T82D4 002:570.370 JLINK_ReadMemEx(0x20019C68, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:570.391    -- Read from C cache (4 bytes @ 0x20019C68)
T82D4 002:570.418   Data:  00 00 00 00
T82D4 002:570.445 - 0.084ms returns 4 (0x4)
T82D4 002:570.468 JLINK_ReadMemEx(0x20019C64, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:570.490    -- Read from C cache (4 bytes @ 0x20019C64)
T82D4 002:570.516   Data:  00 00 00 00
T82D4 002:570.542 - 0.083ms returns 4 (0x4)
T82D4 002:570.583 JLINK_ReadMemEx(0x20000C68, 0x1 Bytes, Flags = 0x02000000)
T82D4 002:570.604    -- Read from C cache (1 bytes @ 0x20000C68)
T82D4 002:570.631   Data:  00
T82D4 002:570.656 - 0.081ms returns 1 (0x1)
T82D4 002:570.713 JLINK_ReadMemEx(0x20019C52, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:570.737    -- Read from C cache (4 bytes @ 0x20019C52)
T82D4 002:570.762   Data:  00 00 00 00
T82D4 002:570.789 - 0.085ms returns 4 (0x4)
T82D4 002:570.812 JLINK_ReadMemEx(0x08018DD8, 0x8 Bytes, Flags = 0x02000000)
T82D4 002:570.838   CPU_ReadMem(64 bytes @ 0x08018DC0)
T82D4 002:571.677    -- Updating C cache (64 bytes @ 0x08018DC0)
T82D4 002:571.738    -- Read from C cache (8 bytes @ 0x08018DD8)
T82D4 002:571.765   Data:  7F 2A 0C 85 F8 30 B6 3E
T82D4 002:571.792 - 0.988ms returns 8 (0x8)
T82D4 002:572.098 JLINK_ReadMemEx(0x2001A352, 0x4 Bytes, Flags = 0x02000000)
T82D4 002:572.130   CPU_ReadMem(64 bytes @ 0x2001A340)
T82D4 002:572.973    -- Updating C cache (64 bytes @ 0x2001A340)
T82D4 002:573.027    -- Read from C cache (4 bytes @ 0x2001A352)
T82D4 002:573.053   Data:  00 00 00 00
T82D4 002:573.078 - 0.989ms returns 4 (0x4)
