T82D4 000:011.495   SEGGER J-Link V7.22b Log File
T82D4 000:011.751   DLL Compiled: Jun 17 2021 17:22:49
T82D4 000:011.913   Logging started @ 2025-06-11 02:27
T82D4 000:011.936 - 11.945ms
T82D4 000:011.971 JLINK_SetWarnOutHandler(...)
T82D4 000:011.995 - 0.036ms
T82D4 000:012.019 JLINK_OpenEx(...)
T82D4 000:013.693   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.034   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T82D4 000:014.293   Decompressing FW timestamp took 143 us
T82D4 000:020.106   Hardware: V9.40
T82D4 000:020.148   S/N: 59406895
T82D4 000:020.179   OEM: SEGGER
T82D4 000:020.206   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FlashDL, FlashB<PERSON>, J<PERSON><PERSON>
T82D4 000:020.984   TELNET listener socket opened on port 19021
T82D4 000:021.144   WEBSRV Starting webserver
T82D4 000:021.287   WEBSRV Webserver running on local port 19080
T82D4 000:021.320 - 9.309ms returns "O.K."
T82D4 000:021.346 JLINK_GetEmuCaps()
T82D4 000:021.366 - 0.028ms returns 0xB9FF7BBF
T82D4 000:021.388 JLINK_TIF_GetAvailable(...)
T82D4 000:021.530 - 0.156ms
T82D4 000:021.556 JLINK_SetErrorOutHandler(...)
T82D4 000:021.574 - 0.026ms
T82D4 000:021.610 JLINK_ExecCommand("ProjectFile = "D:\test\test\INS370M-25J20240919\Project\JLinkSettings.ini"", ...). 
T82D4 000:032.047   Device "GD32F450II" selected.
T82D4 000:032.701 - 11.107ms returns 0x00
T82D4 000:032.761 JLINK_ExecCommand("Device = GD32F470II", ...). 
T82D4 000:033.463   Device "GD32F450II" selected.
T82D4 000:034.038 - 1.250ms returns 0x00
T82D4 000:034.071 JLINK_GetHardwareVersion()
T82D4 000:034.090 - 0.027ms returns 94000
T82D4 000:034.108 JLINK_GetDLLVersion()
T82D4 000:034.127 - 0.027ms returns 72202
T82D4 000:034.145 JLINK_GetOEMString(...)
T82D4 000:034.162 JLINK_GetFirmwareString(...)
T82D4 000:034.185 - 0.031ms
T82D4 000:034.214 JLINK_GetDLLVersion()
T82D4 000:034.234 - 0.028ms returns 72202
T82D4 000:034.252 JLINK_GetCompileDateTime()
T82D4 000:034.268 - 0.025ms
T82D4 000:034.292 JLINK_GetFirmwareString(...)
T82D4 000:034.311 - 0.027ms
T82D4 000:034.333 JLINK_GetHardwareVersion()
T82D4 000:034.352 - 0.027ms returns 94000
T82D4 000:034.374 JLINK_GetSN()
T82D4 000:034.393 - 0.027ms returns 59406895
T82D4 000:034.415 JLINK_GetOEMString(...)
T82D4 000:034.445 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T82D4 000:035.126 - 0.700ms returns 0x00
T82D4 000:035.158 JLINK_HasError()
T82D4 000:035.192 JLINK_SetSpeed(2000)
T82D4 000:035.317 - 0.144ms
T82D4 000:035.348 JLINK_GetId()
T82D4 000:036.047   Found SW-DP with ID 0x2BA01477
T82D4 000:038.543   DPIDR: 0x2BA01477
T82D4 000:038.630   Scanning AP map to find all available APs
T82D4 000:039.160   AP[1]: Stopped AP scan as end of AP map has been reached
T82D4 000:039.221   AP[0]: AHB-AP (IDR: 0x24770011)
T82D4 000:039.265   Iterating through AP map to find AHB-AP to use
T82D4 000:040.137   AP[0]: Core found
T82D4 000:040.173   AP[0]: AHB-AP ROM base: 0xE00FF000
T82D4 000:040.592   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T82D4 000:040.636   Found Cortex-M4 r0p1, Little endian.
