/*!
    \file  	main.c
    \brief 	ins370m project for GD32F4xx
	\author	Bill
	\data	2024/09/19
*/
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"
#include "bsp_gpio.h"
#include "bsp_uart.h"
#include <string.h>
#include <stdio.h>

uint8_t uart4_sendImuFlag = 0;
uint8_t Original_Data = 0; // 0:正常版本，2:原始数据输出

extern uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
uint8_t g_StartUpdateFirm = 0; // 开始升级标志 1:开始升级 0:结束升级

// GD32F4xx平台的函数声明
void UartIrqInit(void);
void SDUartIrqInit(void);
void UartIrqSendMsg(char *txbuf, int size);
void SDUartIrqSendMsg(char *txbuf, int size);

void UartIrqInit(void)
{
    // GD32F4xx平台的串口初始化 - 改为UART3用于导航数据输出
    bsp_systick_init01(UART3);
}

void SDUartIrqInit(void)
{
    // GD32F4xx平台的SD串口初始化
    bsp_systick_init(USART0);
}

void UartIrqSendMsg(char *txbuf, int size)
{
    // 暂时禁用串口发送，避免UART3问题导致程序卡死
    // GD32F4xx平台的串口发送函数 - 改为UART3用于导航数据输出
    if(txbuf == NULL || size <= 0)
    {
        return;
    }

    // 暂时注释掉实际的串口发送操作
    /*
    for(int i = 0; i < size; i++)
    {
        // 等待发送缓冲区空
        while(RESET == usart_flag_get(UART3, USART_FLAG_TBE));
        // 发送数据
        usart_data_transmit(UART3, (uint8_t)txbuf[i]);
    }
    */

    // 直接返回，不进行实际发送
    return;
}

void SDUartIrqSendMsg(char *txbuf, int size)
{
    // 暂时禁用SD串口发送，避免USART0问题导致程序卡死
    // GD32F4xx平台的SD串口发送函数
    if(txbuf == NULL || size <= 0)
    {
        return;
    }

    // 暂时注释掉实际的串口发送操作
    /*
    for(int i = 0; i < size; i++)
    {
        // 等待发送缓冲区空
        while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
        // 发送数据
        usart_data_transmit(USART0, (uint8_t)txbuf[i]);
    }
    */

    // 直接返回，不进行实际发送
    return;
}

// l355_uart_recv_polling函数已在adxl355.c中定义，此处删除避免重复定义

int main(void)
{
    uint32_t uiLedCnt = 0;
    uint32_t debug_counter = 0;
    uint32_t last_fpga_syn_count = 0;

    // GD32F4xx平台初始化
    SysInit(); // Init inertial navigation device...

    // ReadParaFromFlash(); // 暂时注释掉，避免链接错误

    DeviceInit(); // 系统初始化完成后，进入循环前的准备
    delay_ms(1000);

    // 暂时注释掉串口发送，避免卡死
    // char txbuf[50] = {0};
    // sprintf(txbuf, "\r\n Into INS 370M Init!\n");
    // UartIrqSendMsg(txbuf, strlen(txbuf));

    // printf("=== INS370M Main Loop Started ===\n");  // 禁用printf
    // printf("Waiting for FPGA interrupts...\n");  // 禁用printf

    while (1)
    {
        debug_counter++;

        // 每1000000次循环检查一次FPGA中断状态
        if (debug_counter % 1000000 == 0) {
            // printf("Debug: fpga_syn=%d, fpga_syn_count=%d, loop_count=%d\n",
            //        fpga_syn, (int)fpga_syn_count, (int)fpga_loop_count);  // 禁用printf

            if (fpga_syn_count != last_fpga_syn_count) {
                // printf("FPGA interrupt detected! New count: %d\n", (int)fpga_syn_count);  // 禁用printf
                last_fpga_syn_count = fpga_syn_count;
            } else {
                // printf("No FPGA interrupts detected yet...\n");  // 禁用printf
            }
        }

        if (fpga_syn == 1) // 每一帧FPGA数据产生，处理
        {
            //printf("Processing FPGA data frame...\n");
            fpga_syn = 0;
            // SdFileReadOperate();     // 暂时注释掉，避免链接错误
            get_fpgadata();          // 1、获取当前帧FPGA数据，及相关
            AlgorithmDo();           // 2、对获取的数据进行算法处理
            INS912_Output(&gnavout); // 4、算法处理完成的数据，进行打包、发送处理
            uiLedCnt++;
            if (uiLedCnt >= 20)
            {
                uiLedCnt = 0;
                // Led_Control(); // 暂时注释掉LED控制
            }
        }
#ifdef DEVICE_ACC_TYPE_ADLX355
        l355_uart_recv_polling();
#endif
        loopDoOther(); // 循环中，处理其它事宜
        // sduart_recv_polling();    // 暂时注释掉，避免链接错误
        // analysisRxdata();         // 暂时注释掉，避免链接错误
        // SdFileWriteOperate();     // 暂时注释掉，避免链接错误
        // ff_handle_poll();         // 暂时注释掉，避免链接错误
    }

    return 0;
}
