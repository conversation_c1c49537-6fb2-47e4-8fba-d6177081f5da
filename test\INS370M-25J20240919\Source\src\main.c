/*!
    \file  	main.c
    \brief 	ins912-3a project
	\author	Bill
	\data	2023/10/27
*/
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"
#include "bsp_gpio.h"
#include "bsp_uart.h"
#include <string.h>
#include <stdio.h>

uint8_t uart4_sendImuFlag = 0;
uint8_t Original_Data = 0; // 0:正常版本，2:原始数据输出

extern uint8_t fpga_syn;
extern uint32_t fpga_syn_count;
extern uint32_t fpga_loop_count;
extern unsigned short gfpgadata[200];
extern navcanin_t gcanin;
extern navoutdata_t gnavout;
uint8_t g_StartUpdateFirm = 0; // 开始升级标志 1:开始升级 0:结束升级

// delay_ms函数已在systick.c中定义，此处删除重复定义

void ReadParaFromFlash(void)
{
    // 从Flash读取参数的实现
    // 这里需要根据具体需求实现
}

void UartIrqInit(void)
{
    // GD32F4xx平台的串口初始化 - 改为UART3用于导航数据输出
    bsp_systick_init01(UART3);
}

void SDUartIrqInit(void)
{
    // GD32F4xx平台的SD串口初始化
    bsp_systick_init(USART0);
}

void UartIrqSendMsg(char *txbuf, int size)
{
    // GD32F4xx平台的串口发送函数 - 改为UART3用于导航数据输出
    if(txbuf == NULL || size <= 0)
    {
        return;
    }

    for(int i = 0; i < size; i++)
    {
        // 等待发送缓冲区空
        while(RESET == usart_flag_get(UART3, USART_FLAG_TBE));
        // 发送数据
        usart_data_transmit(UART3, (uint8_t)txbuf[i]);
    }
}

void SDUartIrqSendMsg(char *txbuf, int size)
{
    // GD32F4xx平台的SD串口发送函数
    if(txbuf == NULL || size <= 0)
    {
        return;
    }

    for(int i = 0; i < size; i++)
    {
        // 等待发送缓冲区空
        while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
        // 发送数据
        usart_data_transmit(USART0, (uint8_t)txbuf[i]);
    }
}

// SysInit函数已在navi.c中定义，此处删除避免重复定义

// AlgorithmDo函数已在InsTestingEntry.c中定义，此处删除避免重复定义

void analysisRxdata(void)
{
    // 分析接收数据，用于参数设置和升级
    // 这里调用实际的数据分析函数
    // 具体实现在uart.c中的analysisRxdata函数
}

void SdFileWriteOperate(void)
{
    // SD卡文件写操作
    // 这里调用实际的SD卡写操作函数
    // 具体实现在sd_fatfs.c中
}

void SdFileReadOperate(void)
{
    // SD卡文件读操作
    // 这里调用实际的SD卡读操作函数
    // 具体实现在sd_fatfs.c中
}

void ff_handle_poll(void)
{
    // 文件系统轮询处理
    // 这里调用实际的文件系统轮询函数
    // 具体实现在ff_queue.c中
}

void test_uart_recv_polling(void)
{
    // GD32F4xx平台的串口接收轮询函数
    // 这里实现串口数据接收轮询
    // 具体实现需要根据GD32F4xx的串口驱动来完成
}

void sduart_recv_polling(void)
{
    // SD卡串口接收轮询
    // 这里调用实际的串口接收轮询函数
    test_uart_recv_polling();
}

// l355_uart_recv_polling函数已在adxl355.c中定义，此处删除避免重复定义

int main(void)
{
    // 最小化测试 - 只做最基本的操作
    volatile uint32_t test_counter = 0;

    // 简单的延时循环，确认程序在运行
    for(volatile int i = 0; i < 1000000; i++) {
        test_counter++;
    }

    // 尝试最基本的串口输出
    // 先不调用任何初始化函数，看看是否能输出

    // 如果连这个都不能输出，说明问题在更底层
    printf("=== MINIMAL MAIN TEST ===\n");

    // 无限循环，防止程序退出
    while(1) {
        test_counter++;
        if(test_counter % 10000000 == 0) {
            printf("Still alive: %lu\n", test_counter);
        }
    }
}
