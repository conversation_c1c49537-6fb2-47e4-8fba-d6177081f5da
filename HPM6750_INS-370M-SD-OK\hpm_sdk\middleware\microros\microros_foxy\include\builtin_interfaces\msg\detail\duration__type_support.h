// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from builtin_interfaces:msg/Duration.idl
// generated code does not contain a copyright notice

#ifndef BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_H_
#define BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "builtin_interfaces/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_builtin_interfaces
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  builtin_interfaces,
  msg,
  Duration
)();

#ifdef __cplusplus
}
#endif

#endif  // BUILTIN_INTERFACES__MSG__DETAIL__DURATION__TYPE_SUPPORT_H_
