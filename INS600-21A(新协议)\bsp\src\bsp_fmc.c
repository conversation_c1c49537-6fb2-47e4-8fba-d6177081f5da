#include "bsp_fmc.h"
#include "systick.h"
#include "fpgad.h"


/* define mode register content */
/* burst length */
#define SDRAM_MODEREG_BURST_LENGTH_1             ((uint16_t)0x0000)
#define SDRAM_MODEREG_BURST_LENGTH_2             ((uint16_t)0x0001)
#define SDRAM_MODEREG_BURST_LENGTH_4             ((uint16_t)0x0002)
#define SDRAM_MODEREG_BURST_LENGTH_8             ((uint16_t)0x0003)

/* burst type */
#define SDRAM_MODEREG_BURST_TYPE_SEQUENTIAL      ((uint16_t)0x0000)
#define SDRAM_MODEREG_BURST_TYPE_INTERLEAVED     ((uint16_t)0x0008)

/* cas latency */
#define SDRAM_MODEREG_CAS_LATENCY_2              ((uint16_t)0x0020)
#define SDRAM_MODEREG_CAS_LATENCY_3              ((uint16_t)0x0030)

/* write mode */
#define SDRAM_MODEREG_WRITEBURST_MODE_PROGRAMMED ((uint16_t)0x0000)
#define SDRAM_MODEREG_WRITEBURST_MODE_SINGLE     ((uint16_t)0x0200)

#define SDRAM_MODEREG_OPERATING_MODE_STANDARD    ((uint16_t)0x0000)

#define SDRAM_TIMEOUT                            ((uint32_t)0x0000FFFF)

/*
* brief    sdram peripheral initialize
* param[in]  sdram_device: specify the SDRAM device 
* param[out] none
* retval     none
*/
void exmc_synchronous_dynamic_ram_init(uint32_t sdram_device)
{
	exmc_sdram_parameter_struct sdram_init_struct;
	exmc_sdram_timing_parameter_struct sdram_timing_init_struct;
	exmc_sdram_command_parameter_struct sdram_command_init_struct;

	uint32_t command_content = 0, bank_select;
	uint32_t timeout = SDRAM_TIMEOUT;

	/* enable EXMC clock*/
	rcu_periph_clock_enable(RCU_EXMC);
	rcu_periph_clock_enable(RCU_GPIOC);
	rcu_periph_clock_enable(RCU_GPIOD);
	rcu_periph_clock_enable(RCU_GPIOE);
	rcu_periph_clock_enable(RCU_GPIOF);
	rcu_periph_clock_enable(RCU_GPIOG);
	rcu_periph_clock_enable(RCU_GPIOH);

	/* common GPIO configuration */
	/* SDNE0(PC2),SDCKE0(PC5) pin configuration */ 
	gpio_af_set(GPIOC, GPIO_AF_12, GPIO_PIN_2 | GPIO_PIN_5);
	gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_2 | GPIO_PIN_5);
	gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_2 | GPIO_PIN_5);

	/* D2(PD0),D3(PD1),D13(PD8),D14(PD9),D15(PD10),D0(PD14),D1(PD15) pin configuration */
	gpio_af_set(GPIOD, GPIO_AF_12, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_8 | GPIO_PIN_9 |
								   GPIO_PIN_10 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_8 | GPIO_PIN_9 |
														 GPIO_PIN_10 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_8 | GPIO_PIN_9 |
																	 GPIO_PIN_10 | GPIO_PIN_14 | GPIO_PIN_15);

	/* NBL0(PE0),NBL1(PE1),D4(PE7),D5(PE8),D6(PE9),D7(PE10),D8(PE11),D9(PE12),D10(PE13),D11(PE14),D12(PE15) pin configuration */
	gpio_af_set(GPIOE, GPIO_AF_12, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_7  | GPIO_PIN_8 |
								   GPIO_PIN_9  | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12 |
								   GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_mode_set(GPIOE, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_7  | GPIO_PIN_8 |
														 GPIO_PIN_9  | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12 |
														 GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_output_options_set(GPIOE, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_7  | GPIO_PIN_8 |
																	 GPIO_PIN_9  | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12 |
																	 GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);

	/* A0(PF0),A1(PF1),A2(PF2),A3(PF3),A4(PF4),A5(PF5),NRAS(PF11),A6(PF12),A7(PF13),A8(PF14),A9(PF15) pin configuration */
	gpio_af_set(GPIOF, GPIO_AF_12, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_2  | GPIO_PIN_3  |
								   GPIO_PIN_4  | GPIO_PIN_5  | GPIO_PIN_11 | GPIO_PIN_12 |
								   GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_2  | GPIO_PIN_3  |
														 GPIO_PIN_4  | GPIO_PIN_5  | GPIO_PIN_11 | GPIO_PIN_12 |
														 GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);
	gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_0  | GPIO_PIN_1  | GPIO_PIN_2  | GPIO_PIN_3  |
																	 GPIO_PIN_4  | GPIO_PIN_5  | GPIO_PIN_11 | GPIO_PIN_12 |
																	 GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15);

	/* A10(PG0),A11(PG1),A12(PG2),A14(PG4),A15(PG5),SDCLK(PG8),NCAS(PG15) pin configuration */
	gpio_af_set(GPIOG, GPIO_AF_12, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_4 | 
								   GPIO_PIN_5 | GPIO_PIN_8 | GPIO_PIN_15);
	gpio_mode_set(GPIOG, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_4 | 
														 GPIO_PIN_5 | GPIO_PIN_8 | GPIO_PIN_15);
	gpio_output_options_set(GPIOG, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_4 | 
																	 GPIO_PIN_5 | GPIO_PIN_8 | GPIO_PIN_15);
	/* SDNWE(PH5) pin configuration */
	gpio_af_set(GPIOH, GPIO_AF_12, GPIO_PIN_5);
	gpio_mode_set(GPIOH, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_5);
	gpio_output_options_set(GPIOH, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_5);

	/* specify which SDRAM to read and write */
	if(EXMC_SDRAM_DEVICE0 == sdram_device){
		bank_select = EXMC_SDRAM_DEVICE0_SELECT;
	}else{
		bank_select = EXMC_SDRAM_DEVICE1_SELECT;
	}

	/* EXMC SDRAM device initialization sequence */
	/* Step 1 : configure SDRAM timing registers */
	/* LMRD: 2 clock cycles */
	sdram_timing_init_struct.load_mode_register_delay = 2;
	/* XSRD: min = 67ns */
	sdram_timing_init_struct.exit_selfrefresh_delay = 1;
	/* RASD: min=45ns , max=120k (ns) */
	sdram_timing_init_struct.row_address_select_delay = 6;
	/* ARFD: min=64ns */
	sdram_timing_init_struct.auto_refresh_delay = 8;
	/* WRD:  min=1 Clock cycles +6ns */
	sdram_timing_init_struct.write_recovery_delay = 2;
	/* RPD:  min=20ns */
	sdram_timing_init_struct.row_precharge_delay = 1;
	/* RCD:  min=20ns */
	sdram_timing_init_struct.row_to_column_delay = 1;

	/* step 2 : configure SDRAM control registers */
	sdram_init_struct.sdram_device = sdram_device;
	sdram_init_struct.column_address_width = EXMC_SDRAM_COW_ADDRESS_9;
	sdram_init_struct.row_address_width = EXMC_SDRAM_ROW_ADDRESS_13;
	sdram_init_struct.data_width = EXMC_SDRAM_DATABUS_WIDTH_16B;
	sdram_init_struct.internal_bank_number = EXMC_SDRAM_4_INTER_BANK;
	sdram_init_struct.cas_latency = EXMC_CAS_LATENCY_3_SDCLK; 
	sdram_init_struct.write_protection = DISABLE;
	sdram_init_struct.sdclock_config = EXMC_SDCLK_PERIODS_2_HCLK;  
	sdram_init_struct.brust_read_switch = ENABLE;
	sdram_init_struct.pipeline_read_delay = EXMC_PIPELINE_DELAY_1_HCLK;
	sdram_init_struct.timing  = &sdram_timing_init_struct;
	/* EXMC SDRAM bank initialization */
	exmc_sdram_init(&sdram_init_struct);

	/* step 3 : configure CKE high command */
	sdram_command_init_struct.command = EXMC_SDRAM_CLOCK_ENABLE;
	sdram_command_init_struct.bank_select = bank_select;
	sdram_command_init_struct.auto_refresh_number = EXMC_SDRAM_AUTO_REFLESH_1_SDCLK;
	sdram_command_init_struct.mode_register_content = 0;
	/* wait until the SDRAM controller is ready */ 
	while((exmc_flag_get(sdram_device, EXMC_SDRAM_FLAG_NREADY) != RESET) && (timeout > 0)){
		timeout--;
	}
	/* send the command */
	exmc_sdram_command_config(&sdram_command_init_struct);

	/* step 4 : insert 10ms delay */
	delay_ms(10);

	/* step 5 : configure precharge all command */
	sdram_command_init_struct.command = EXMC_SDRAM_PRECHARGE_ALL;
	sdram_command_init_struct.bank_select = bank_select;
	sdram_command_init_struct.auto_refresh_number = EXMC_SDRAM_AUTO_REFLESH_1_SDCLK;
	sdram_command_init_struct.mode_register_content = 0;
	/* wait until the SDRAM controller is ready */
	timeout = SDRAM_TIMEOUT; 
	while((exmc_flag_get(sdram_device, EXMC_SDRAM_FLAG_NREADY) != RESET) && (timeout > 0)){
		timeout--;
	}
	/* send the command */
	exmc_sdram_command_config(&sdram_command_init_struct);

	/* step 6 : configure Auto-Refresh command */
	sdram_command_init_struct.command = EXMC_SDRAM_AUTO_REFRESH;
	sdram_command_init_struct.bank_select = bank_select;
	sdram_command_init_struct.auto_refresh_number = EXMC_SDRAM_AUTO_REFLESH_8_SDCLK;
	sdram_command_init_struct.mode_register_content = 0;
	/* wait until the SDRAM controller is ready */ 
	timeout = SDRAM_TIMEOUT; 
	while((exmc_flag_get(sdram_device, EXMC_SDRAM_FLAG_NREADY) != RESET) && (timeout > 0)){
		timeout--;
	}
	/* send the command */
	exmc_sdram_command_config(&sdram_command_init_struct);

	/* step 7 : configure load mode register command */
	/* program mode register */
	command_content = (uint32_t)SDRAM_MODEREG_BURST_LENGTH_1        |
								SDRAM_MODEREG_BURST_TYPE_SEQUENTIAL   |
								SDRAM_MODEREG_CAS_LATENCY_3           |
								SDRAM_MODEREG_OPERATING_MODE_STANDARD |
								SDRAM_MODEREG_WRITEBURST_MODE_SINGLE;

	sdram_command_init_struct.command = EXMC_SDRAM_LOAD_MODE_REGISTER;
	sdram_command_init_struct.bank_select = bank_select;
	sdram_command_init_struct.auto_refresh_number = EXMC_SDRAM_AUTO_REFLESH_1_SDCLK;
	sdram_command_init_struct.mode_register_content = command_content;

	/* wait until the SDRAM controller is ready */ 
	timeout = SDRAM_TIMEOUT; 
	while((exmc_flag_get(sdram_device, EXMC_SDRAM_FLAG_NREADY) != RESET) && (timeout > 0)){
		timeout--;
	}
	/* send the command */
	exmc_sdram_command_config(&sdram_command_init_struct);

	/* step 8 : set the auto-refresh rate counter */
	/* 64ms, 8192-cycle refresh, 64ms/8192=7.81us */
	/* SDCLK_Freq = SYS_Freq/2 */
	/* (7.81 us * SDCLK_Freq) - 20 */
	exmc_sdram_refresh_count_set(448);

	/* wait until the SDRAM controller is ready */ 
	timeout = SDRAM_TIMEOUT; 
	while((exmc_flag_get(sdram_device, EXMC_SDRAM_FLAG_NREADY) != RESET) && (timeout > 0)){
		timeout--;
	}
}

/*
* brief    fill the buffer with specified value
* param[in]  pbuffer: pointer on the buffer to fill
* param[in]  buffer_lengh: length of the buffer to fill
* param[in]  offset: the offset of the buffer
* param[out] none
* retval     none
*/
void fill_buffer(uint8_t *pbuffer, uint16_t buffer_lengh, uint16_t offset)
{
	uint16_t index = 0;

	/* put in global buffer same values */
	for (index = 0; index < buffer_lengh; index++ ){
		pbuffer[index] = index + offset;
	}
}

/*
* brief    write a byte buffer(data is 8 bits) to the EXMC SDRAM memory
* param[in]  sdram_device: specify which a SDRAM memory block is written
* param[in]  pbuffer: pointer to buffer
* param[in]  writeaddr: SDRAM memory internal address from which the data will be written
* param[in]  numbytetowrite: number of bytes to write
* param[out] none
* retval     none
*/
void sdram_writebuffer_8(uint32_t sdram_device,uint8_t* pbuffer, uint32_t writeaddr, uint32_t numbytetowrite)
{
	uint32_t temp_addr;

	/* Select the base address according to EXMC_Bank */
	if(sdram_device == EXMC_SDRAM_DEVICE0){
		temp_addr = SDRAM_DEVICE0_ADDR;
	}else{
		temp_addr = SDRAM_DEVICE1_ADDR;
	}

	 /* While there is data to write */
	for(; numbytetowrite != 0; numbytetowrite--){
		/* Transfer data to the memory */
		*(uint8_t *) (temp_addr + writeaddr) = *pbuffer++;

		/* Increment the address*/  
		writeaddr += 1;
	}
}

/*
* brief    read a block of 8-bit data from the EXMC SDRAM memory
* param[in]  sdram_device: specify which a SDRAM memory block is written
* param[in]  pbuffer: pointer to buffer
* param[in]  readaddr: SDRAM memory internal address to read from
* param[in]  numbytetoread: number of bytes to read
* param[out] none
* retval     none
*/
void sdram_readbuffer_8(uint32_t sdram_device,uint8_t* pbuffer, uint32_t readaddr, uint32_t numbytetoread)
{
	uint32_t temp_addr;

	/* select the base address according to EXMC_Bank */
	if(sdram_device == EXMC_SDRAM_DEVICE0){
		temp_addr = SDRAM_DEVICE0_ADDR;
	}else{
		temp_addr = SDRAM_DEVICE1_ADDR;
	}

	/* while there is data to read */
	for(; numbytetoread != 0; numbytetoread--){
		/* read a byte from the memory */
		*pbuffer++ = *(uint8_t*) (temp_addr + readaddr);

		/* increment the address */
		readaddr += 1;
	}
}

int DRam_Read(u32 addr, u16* data, u32 len)
{
    u32 i = 0;

    if ((addr + len) > DRAM_SIZE) return 0;

#ifndef WIN32
    for (i = 0; i < len; i++)
    {
        data[i] = DRAM_DATA(addr + i);
    }
#endif
    return 1;
}

int DRam_Write(u32 addr, u16* data, u32 len)
{
    u32 i = 0;

    if ((addr + len) > DRAM_SIZE) return 0;
#ifndef WIN32
    for (i = 0; i < len; i++)
    {
        DRAM_DATA(addr + i) = data[i];
    }
#endif
    return 1;
}

void FMC_ReadBuffer(EFpgaSpace space, u32 addr, u16* pBuffer, u32 size)
{
	__IO u32 pointer = (u32)addr;
    __IO u32 base = (space == SPACE_COM)? FPGA_BASE_ADDR : FPGA_DRAM_ADDR;

	for (; size != 0; size--)
	{
		*pBuffer++ = *(__IO u16*) (base + ADDR_SHIFT(pointer));
		pointer++;
	}
}


void FMC_WriteBuffer(EFpgaSpace space, u32 addr, u16* pBuffer, u32 size)
{
	__IO u32 pointer = (u32)addr;
    __IO u32 base = (space == SPACE_COM)? FPGA_BASE_ADDR : FPGA_DRAM_ADDR;

	for (; size != 0; size--)
	{
		*(u16*)(base + ADDR_SHIFT(pointer)) = *pBuffer++;
		pointer++;
	}
}

u16 FMC_ReadWord(EFpgaSpace space, u32 addr)
{
    __IO u32 base = (space == SPACE_COM)? FPGA_BASE_ADDR : FPGA_DRAM_ADDR;
	return (*(__IO u16*)(base + ADDR_SHIFT(addr)));
}

void FMC_WriteWord(EFpgaSpace space, u32 addr, u16 data)
{
    __IO u32 base = (space == SPACE_COM)? FPGA_BASE_ADDR : FPGA_DRAM_ADDR;
	*(u16*)(base + ADDR_SHIFT(addr)) = data;
}

/*
* brief    sram peripheral initialize
* param[in]  none 
* param[out] none
* retval     none
*/
void exmc_asynchronous_sram_init(void)
{
	exmc_norsram_parameter_struct		sram_init_struct;
	exmc_norsram_timing_parameter_struct read_timing;
	exmc_norsram_timing_parameter_struct write_timing;

	/* enable EXMC clock*/
	rcu_periph_clock_enable(RCU_EXMC);
	rcu_periph_clock_enable(RCU_GPIOB);
	rcu_periph_clock_enable(RCU_GPIOC);
	rcu_periph_clock_enable(RCU_GPIOD);
	rcu_periph_clock_enable(RCU_GPIOE);
	rcu_periph_clock_enable(RCU_GPIOF);
	rcu_periph_clock_enable(RCU_GPIOG);
	rcu_periph_clock_enable(RCU_GPIOH);

	/* common GPIO configuration */
	gpio_af_set(GPIOD, GPIO_AF_12, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_14|GPIO_PIN_15);
	gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_14|GPIO_PIN_15);
	gpio_output_options_set(GPIOD,GPIO_OTYPE_PP,GPIO_OSPEED_50MHZ,GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_14|GPIO_PIN_15);
	
	gpio_af_set(GPIOE, GPIO_AF_12, GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	gpio_mode_set(GPIOE, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	gpio_output_options_set(GPIOD,GPIO_OTYPE_PP,GPIO_OSPEED_50MHZ,GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	
	gpio_af_set(GPIOF, GPIO_AF_12, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	gpio_output_options_set(GPIOD,GPIO_OTYPE_PP,GPIO_OSPEED_50MHZ,GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5 \
						|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15);
	
	gpio_af_set(GPIOG, GPIO_AF_12, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_9);
	gpio_mode_set(GPIOG, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_9);
	gpio_output_options_set(GPIOD,GPIO_OTYPE_PP,GPIO_OSPEED_50MHZ,GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_9);
	
//	//��ʱ������
//	read_timing.asyn_access_mode  = EXMC_ACCESS_MODE_A;   //ģʽA���첽����SRAM
//	read_timing.asyn_address_setuptime = 0;       //�첽���ʵ�ַ����ʱ��
//	read_timing.asyn_address_holdtime = 2;       //�첽���ʵ�ַ����ʱ��
//	read_timing.asyn_data_setuptime  = 32;       //�첽�������ݽ���ʱ�� 160ns
//	read_timing.bus_latency    = 0;       //ͬ��/�첽����������ʱʱ��
//	read_timing.syn_clk_division  = EXMC_SYN_CLOCK_RATIO_2_CLK;   //ͬ������ʱ�ӷ�Ƶϵ������HCLK�з�Ƶ��
//	read_timing.syn_data_latency  = EXMC_DATALAT_2_CLK;   //ͬ�������л�õ�1����������Ҫ�ĵȴ��ӳ�

//	//дʱ������
//	write_timing.asyn_access_mode  = EXMC_ACCESS_MODE_A;   //ģʽA���첽����SRAM
//	write_timing.asyn_address_setuptime = 0;       //�첽���ʵ�ַ����ʱ��
//	write_timing.asyn_address_holdtime = 2;       //�첽���ʵ�ַ����ʱ��
//	write_timing.asyn_data_setuptime = 20;       //�첽�������ݽ���ʱ�� 100ns
//	write_timing.bus_latency   = 0;       //ͬ��/�첽����������ʱʱ��
//	write_timing.syn_clk_division  = EXMC_SYN_CLOCK_RATIO_15_CLK;  //ͬ������ʱ�ӷ�Ƶϵ������HCLK�з�Ƶ��
//	write_timing.syn_data_latency  = EXMC_DATALAT_2_CLK;   //ͬ�������л�õ�1����������Ҫ�ĵȴ��ӳ�

//	//Region1����
//	sram_init_struct.norsram_region		= EXMC_BANK0_NORSRAM_REGION0;	//Region0
//	sram_init_struct.address_data_mux	= DISABLE;						//��ַ���������߶�·����
//	sram_init_struct.memory_type		= EXMC_MEMORY_TYPE_SRAM;		//����������ΪSRAM
//	sram_init_struct.databus_width		= EXMC_NOR_DATABUS_WIDTH_16B;	//���ݿ���16λ
//	sram_init_struct.burst_mode			= DISABLE;						//����ͻ������
//	sram_init_struct.nwait_config		= EXMC_NWAIT_CONFIG_BEFORE;		//�ȴ���������
//	sram_init_struct.nwait_polarity		= EXMC_NWAIT_POLARITY_LOW;		//�ȴ������źŵ͵�ƽ��Ч
//	sram_init_struct.wrap_burst_mode	= DISABLE;						//���ð�ͻ������
//	sram_init_struct.asyn_wait			= DISABLE;						//�����첽�ȴ�
//	sram_init_struct.extended_mode		= DISABLE;						//ʹ����չģʽ
//	sram_init_struct.memory_write		= ENABLE;						//ʹ��д���ⲿ�洢��
//	sram_init_struct.nwait_signal		= DISABLE;						//���õȴ������ź�
//	sram_init_struct.write_mode			= EXMC_ASYN_WRITE;				//д��ģʽΪ�첽д��
//	sram_init_struct.read_write_timing	= &read_timing;					//��ʱ������
//	sram_init_struct.write_timing		= &write_timing;				//дʱ������
//	sram_init_struct.extended_mode		= ENABLE;						//��չģʽ
//	//��ʼ��Region1
//	exmc_norsram_init(&sram_init_struct);

//	//ʹ��Region1
//	exmc_norsram_enable(EXMC_BANK0_NORSRAM_REGION0);
//	
//	exmc_norsram_enable(EXMC_BANK0_NORSRAM_REGION1);
	
		
	//��ʱ������
	read_timing.asyn_access_mode		= EXMC_ACCESS_MODE_A;			//ģʽA���첽����SRAM
	read_timing.asyn_address_setuptime	= 2;							//�첽���ʵ�ַ����ʱ��
	read_timing.asyn_address_holdtime	= 0;							//�첽���ʵ�ַ����ʱ��
	read_timing.asyn_data_setuptime		= 32;							//�첽�������ݽ���ʱ�� 160ns
	read_timing.bus_latency				= 0;							//ͬ��/�첽����������ʱʱ��
	read_timing.syn_clk_division		= EXMC_SYN_CLOCK_RATIO_2_CLK;  	//ͬ������ʱ�ӷ�Ƶϵ������HCLK�з�Ƶ��
	read_timing.syn_data_latency		= EXMC_DATALAT_2_CLK;			//ͬ�������л�õ�1����������Ҫ�ĵȴ��ӳ�

	//дʱ������
	write_timing.asyn_access_mode		= EXMC_ACCESS_MODE_A;			//ģʽA���첽����SRAM
	write_timing.asyn_address_setuptime	= 2;							//�첽���ʵ�ַ����ʱ��
	write_timing.asyn_address_holdtime	= 2;							//�첽���ʵ�ַ����ʱ��
	write_timing.asyn_data_setuptime	= 20;							//�첽�������ݽ���ʱ�� 100ns
	write_timing.bus_latency			= 0;							//ͬ��/�첽����������ʱʱ��
	write_timing.syn_clk_division		= EXMC_SYN_CLOCK_RATIO_2_CLK; 	//ͬ������ʱ�ӷ�Ƶϵ������HCLK�з�Ƶ��
	write_timing.syn_data_latency		= EXMC_DATALAT_2_CLK;			//ͬ�������л�õ�1����������Ҫ�ĵȴ��ӳ�


	/* step 2 : configure SDRAM control registers */
	//Region0����

	sram_init_struct.norsram_region		= EXMC_BANK0_NORSRAM_REGION0;	//Region0
	sram_init_struct.address_data_mux	= DISABLE;						//��ַ���������߶�·����
	sram_init_struct.memory_type		= EXMC_MEMORY_TYPE_SRAM;		//����������ΪSRAM
	sram_init_struct.databus_width		= EXMC_NOR_DATABUS_WIDTH_16B;	//���ݿ���16λ
	sram_init_struct.burst_mode			= DISABLE;						//����ͻ������
	sram_init_struct.nwait_config		= EXMC_NWAIT_CONFIG_BEFORE;		//�ȴ���������
	sram_init_struct.nwait_polarity		= EXMC_NWAIT_POLARITY_HIGH;		//�ȴ������źŵ͵�ƽ��Ч
	sram_init_struct.wrap_burst_mode	= DISABLE;						//���ð�ͻ������
	sram_init_struct.asyn_wait			= DISABLE;						//�����첽�ȴ�
	sram_init_struct.extended_mode		= ENABLE;						//ʹ����չģʽ
	sram_init_struct.memory_write		= ENABLE;						//ʹ��д���ⲿ�洢��
	sram_init_struct.nwait_signal		= DISABLE;						//���õȴ������ź�
	sram_init_struct.write_mode			= EXMC_ASYN_WRITE;				//д��ģʽΪ�첽д��
	sram_init_struct.read_write_timing	= &read_timing;					//��ʱ������
	sram_init_struct.write_timing		= &write_timing;				//дʱ������
	//��ʼ��Region0
	exmc_norsram_init(&sram_init_struct);
	//ʹ��Region0
	exmc_norsram_enable(EXMC_BANK0_NORSRAM_REGION0);
	
	//Region1����
	sram_init_struct.norsram_region		= EXMC_BANK0_NORSRAM_REGION1;	//Region0
	//��ʼ��Region1
	exmc_norsram_init(&sram_init_struct);
	exmc_norsram_enable(EXMC_BANK0_NORSRAM_REGION1);
//	delay_us(200);
//	while (FMC_ReadWord(SPACE_COM, 0x220 >> 1) != 0x5555);
}


/*
* brief      get the sector number, size and range of the given address
* param[in]  address: The flash address
* param[out] none
* retval     fmc_sector_info_struct: The information of a sector
*/
fmc_sector_info_struct fmc_sector_info_get(uint32_t addr)
{
	fmc_sector_info_struct sector_info;
	uint32_t temp = 0x00000000U;
	if((FMC_START_ADDRESS <= addr)&&(FMC_END_ADDRESS >= addr)) {
		if ((FMC_BANK1_START_ADDRESS > addr)) {
			/* bank0 area */
			temp = (addr - FMC_BANK0_START_ADDRESS) / SIZE_16KB;	// get sector number
			if (4U > temp) {
				sector_info.sector_name = (uint32_t)temp;
				sector_info.sector_num = CTL_SN(temp);
				sector_info.sector_size = SIZE_16KB;
				sector_info.sector_start_addr = FMC_BANK0_START_ADDRESS + (SIZE_16KB * temp);
				sector_info.sector_end_addr = sector_info.sector_start_addr + SIZE_16KB - 1;
			} else if (8U > temp) {
				sector_info.sector_name = 0x00000004U;
				sector_info.sector_num = CTL_SN(4);
				sector_info.sector_size = SIZE_64KB;
				sector_info.sector_start_addr = 0x08010000U;
				sector_info.sector_end_addr = 0x0801FFFFU;
			} else {
				temp = (addr - FMC_BANK0_START_ADDRESS) / SIZE_128KB;
				sector_info.sector_name = (uint32_t)(temp + 4);
				sector_info.sector_num = CTL_SN(temp + 4);
				sector_info.sector_size = SIZE_128KB;
				sector_info.sector_start_addr = FMC_BANK0_START_ADDRESS + (SIZE_128KB * temp);
				sector_info.sector_end_addr = sector_info.sector_start_addr + SIZE_128KB - 1;
			}
		} else {
			/* bank1 area */
			temp = (addr - FMC_BANK1_START_ADDRESS) / SIZE_16KB;
			if (4U > temp) {
				sector_info.sector_name = (uint32_t)(temp + 12);
				sector_info.sector_num = CTL_SN(temp + 16);
				sector_info.sector_size = SIZE_16KB;
				sector_info.sector_start_addr = FMC_BANK0_START_ADDRESS + (SIZE_16KB * temp);
				sector_info.sector_end_addr = sector_info.sector_start_addr + SIZE_16KB - 1;
			} else if (8U > temp) {
				sector_info.sector_name = 0x00000010;
				sector_info.sector_num = CTL_SN(20);
				sector_info.sector_size = SIZE_64KB;
				sector_info.sector_start_addr = 0x08110000U;
				sector_info.sector_end_addr = 0x0811FFFFU;
			} else if (64U > temp){
				temp = (addr - FMC_BANK1_START_ADDRESS) / SIZE_128KB;
				sector_info.sector_name = (uint32_t)(temp + 16);
				sector_info.sector_num = CTL_SN(temp + 20);
				sector_info.sector_size = SIZE_128KB;
				sector_info.sector_start_addr = FMC_BANK1_START_ADDRESS + (SIZE_128KB * temp);
				sector_info.sector_end_addr = sector_info.sector_start_addr + SIZE_128KB - 1;
			} else {
				temp = (addr - FMC_BANK1_START_ADDRESS) / SIZE_256KB;
				sector_info.sector_name = (uint32_t)(temp + 20);
				sector_info.sector_num = CTL_SN(temp + 8);
				sector_info.sector_size = SIZE_256KB;
				sector_info.sector_start_addr = FMC_BANK1_START_ADDRESS + (SIZE_256KB * temp);
				sector_info.sector_end_addr = sector_info.sector_start_addr + SIZE_256KB - 1;
			}
		}
	} else {
		/* invalid address */
		sector_info.sector_name = FMC_WRONG_SECTOR_NAME;
		sector_info.sector_num = FMC_WRONG_SECTOR_NUM;
		sector_info.sector_size = FMC_INVALID_SIZE;
		sector_info.sector_start_addr = FMC_INVALID_ADDR;
		sector_info.sector_end_addr = FMC_INVALID_ADDR;
	}
	return sector_info;
}

/*
* brief      get the sector number by a given sector name
* param[in]  address: a given sector name
* param[out] none
* retval     uint32_t: sector number
*/
uint32_t sector_name_to_number(uint32_t sector_name)
{
	if(11 >= sector_name){
		return CTL_SN(sector_name);
	}else if(23 >= sector_name){
		return CTL_SN(sector_name + 4);
	}else if(27 >= sector_name){
		return CTL_SN(sector_name - 12);
	}else{
		while(1);
	}
}

/*
* brief      erases the sector of a given address
* param[in]  address: a given address
* param[out] none
* retval     none
*/
int fmc_erase_sector_by_address(uint32_t address)
{
	fmc_sector_info_struct sector_info;
	/* get information about the sector in which the specified address is located */
	sector_info = fmc_sector_info_get(address);
	if(FMC_WRONG_SECTOR_NAME == sector_info.sector_name){
		return 1;
	}else{
		/* unlock the flash program erase controller */
		fmc_unlock(); 
		/* clear pending flags */
		fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
		/* wait the erase operation complete*/
		if(FMC_READY != fmc_sector_erase(sector_info.sector_num)){
			while(1);
		}
		/* lock the flash program erase controller */
		fmc_lock();
		return 0;
	}
}

/*
* brief      write 32 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_32: data pointer
* param[out] none
* retval     none
*/
void fmc_write_32bit_data(uint32_t address, uint16_t length, int32_t* data_32)
{
	fmc_sector_info_struct start_sector_info;
	fmc_sector_info_struct end_sector_info;
	uint32_t sector_num,i;

	/* unlock the flash program erase controller */
	fmc_unlock();
	/* clear pending flags */
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	/* get the information of the start and end sectors */
	start_sector_info = fmc_sector_info_get(address);
	end_sector_info = fmc_sector_info_get(address + 4*length);
	/* erase sector */
	for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++){
		sector_num = sector_name_to_number(i);
		if(FMC_READY != fmc_sector_erase(sector_num)){
			while(1);
		}
	}

	/* write data_32 to the corresponding address */
	for(i=0; i<length; i++){
		if(FMC_READY == fmc_word_program(address, data_32[i])){
			address = address + 4;
		}else{ 
			while(1);
		}
	}
	/* lock the flash program erase controller */
	fmc_lock();
}

/*
* brief      read 32 bit length data from a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_32: data pointer
* param[out] none
* retval     none
*/
void fmc_read_32bit_data(uint32_t address, uint16_t length, int32_t* data_32)
{
	uint8_t i;
	for(i=0; i<length; i++){
		data_32[i] = *(__IO int32_t*)address;
		address=address + 4;
	}
}

/*
* brief      write 16 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_16: data pointer
* param[out] none
* retval     none
*/
void fmc_write_16bit_data(uint32_t address, uint16_t length, int16_t* data_16)
	{
	fmc_sector_info_struct start_sector_info;
	fmc_sector_info_struct end_sector_info;
	uint32_t sector_num,i;

	/* unlock the flash program erase controller */
	fmc_unlock();
	/* clear pending flags */
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	/* get the information of the start and end sectors */
	start_sector_info = fmc_sector_info_get(address);
	end_sector_info = fmc_sector_info_get(address + 2*length);
	/* erase sector */
	for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++){
		sector_num = sector_name_to_number(i);
		if(FMC_READY != fmc_sector_erase(sector_num)){
			while(1);
		}
	}

	/* write data_16 to the corresponding address */
	for(i=0; i<length; i++){
		if(FMC_READY == fmc_halfword_program(address, data_16[i])){
			address = address + 2;
		}else{ 
			while(1);
		}
	}
	/* lock the flash program erase controller */
	fmc_lock();
}

/*
* brief      read 16 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_16: data pointer
* param[out] none
* retval     none
*/
void fmc_read_16bit_data(uint32_t address, uint16_t length, int16_t* data_16)
{
	uint8_t i;
	for(i=0; i<length; i++){
		data_16[i] = *(__IO int16_t*)address;
		address = address + 2;
	}
}

/*
* brief      write 8 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_8: data pointer
* param[out] none
* retval     none
*/
void fmc_write_8bit_data(uint32_t address, uint16_t length, int8_t* data_8)
{
	fmc_sector_info_struct start_sector_info;
	fmc_sector_info_struct end_sector_info;
	uint32_t sector_num,i;

	/* unlock the flash program erase controller */
	fmc_unlock();
	/* clear pending flags */
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	/* get the information of the start and end sectors */
	start_sector_info = fmc_sector_info_get(address);
	end_sector_info = fmc_sector_info_get(address + 2*length);
	/* erase sector */
	for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++){
		sector_num = sector_name_to_number(i);
		if(FMC_READY != fmc_sector_erase(sector_num)){	//bill 2023-0709
			while(1);
		}
	}

	/* write data_8 to the corresponding address */
	for(i=0; i<length; i++){
		if(FMC_READY == fmc_byte_program(address, data_8[i])){
			address++;
		}else{
			while(1);
		}
	}
	/* lock the flash program erase controller */
	fmc_lock();
}

void fmc_write_8bit_data1(uint32_t address, uint16_t length, int8_t* data_8)
{
	fmc_sector_info_struct start_sector_info;
	fmc_sector_info_struct end_sector_info;
	uint32_t sector_num,i;

	/* unlock the flash program erase controller */
	fmc_unlock();
	/* clear pending flags */
	fmc_flag_clear(FMC_FLAG_END | FMC_FLAG_OPERR | FMC_FLAG_WPERR | FMC_FLAG_PGMERR | FMC_FLAG_PGSERR);
	/* get the information of the start and end sectors */
	start_sector_info = fmc_sector_info_get(address);
	end_sector_info = fmc_sector_info_get(address + 2*length);
	/* erase sector */
	for(i = start_sector_info.sector_name; i <= end_sector_info.sector_name; i++){
		sector_num = sector_name_to_number(i);
		//if(FMC_READY != fmc_sector_erase(sector_num)){	//bill 2023-0709
		//	while(1);
		//}
	}

	/* write data_8 to the corresponding address */
	for(i=0; i<length; i++){
		if(FMC_READY == fmc_byte_program(address, data_8[i])){
			address++;
		}else{
			while(1);
		}
	}
	/* lock the flash program erase controller */
	fmc_lock();
}

/*
* brief      read 8 bit length data to a given address
* param[in]  address: a given address(0x08000000~0x082FFFFF)
* param[in]  length: data length
* param[in]  data_8: data pointer
* param[out] none
* retval     none
*/
void fmc_read_8bit_data(uint32_t address, uint16_t length, int8_t* data_8)
{
	int i;
	for(i=0; i<length; i++){
		data_8[i] = *(__IO int8_t*)address;
		address++;
	}
}

/********************************************************************************\
����: ���մ��ڳ�ʼ��
����: rxPort - ���ں�
     baudRate - ������
     parityBits - У��λ
     stopBits - ֹͣλ
     mode - RS232/422/485ѡ��
     enable - ����ʹ��
����: ��
\********************************************************************************/
void Uart_RxInit(EUartRxPort rxPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable)
{
	UCHAR sel = 0x00;
	#ifndef WIN32
//	if (UART_RXPORT_NULL == rxPort) return;
//	if (UART_RXPORT_RS232_1 == rxPort)
//	{
//		FUART_RxInit(rxPort, baudRate, parityBits, stopBits);
//		return;
//	}

	if (rxPort >= UART_RXPORT_COMPLEX_8)
	{
		sel = (rxPort - UART_RXPORT_COMPLEX_8) / 2;
		if (mode == UART_RS422)
		{
			*(UART_MODE_SEL) |= (1 << sel); // 422ģʽ            
			*(UART_MODE_SEL) &= ~(1 << (sel + 4)); // ȫ˫��ģʽ
		}
		else if (mode == UART_RS232)
		{
			*(UART_MODE_SEL) &= ~(1 << sel); // 232ģʽ
			*(UART_MODE_SEL) &= ~(1 << (sel + 4)); // ȫ˫��ģʽ
		}
		else if (mode == UART_RS485)
		{
			*(UART_MODE_SEL) |= (1 << sel); // 422ģʽ
			*(UART_MODE_SEL) |= (1 << (sel + 4)); // ��˫��ģʽ
		}
		else {}
	}
	// ������ջ�����
	*(UART_RHR(rxPort)) = 0x0001;
	// ���ò�����
	*(UART_RBAUD(rxPort)) = baudRate;
	// �������ã�У��λ/ֹͣλ/ʹ�ܣ�
	*(UART_RCONFIG(rxPort)) = parityBits | stopBits | enable;
#endif
}

/********************************************************************************\
����: ���ʹ��ڳ�ʼ��
����: txPort - ���ں�
     baudRate - ������
     parityBits - У��λ
     stopBits - ֹͣλ
     mode - RS232/422/485ѡ��
     enable - ����ʹ��
����: ��
\********************************************************************************/
void Uart_TxInit(EUartTxPort txPort, EUartBaudrate baudRate, EUartParitybits parityBits, EUartStopbits stopBits, EUartMode mode, EUartEnable enable)
{
	UCHAR sel = 0x00;
#ifndef WIN32
	if (UART_TXPORT_NULL == txPort) return;
//	if (UART_TXPORT_RS232_1 == txPort)
//	{
//		FUART_TxInit(txPort, baudRate, parityBits, stopBits);
//		return;
//	}

	if (txPort >= UART_RXPORT_COMPLEX_8)
	{
		sel = (txPort - UART_RXPORT_COMPLEX_8) / 2;
		if (mode == UART_RS422)
		{
			*(UART_MODE_SEL) |= (1 << sel); // 422ģʽ            
			*(UART_MODE_SEL) &= ~(1 << (sel + 4)); // ȫ˫��ģʽ
		}
		else if (mode == UART_RS232)
		{
			*(UART_MODE_SEL) &= ~(1 << sel); // 232ģʽ
			*(UART_MODE_SEL) &= ~(1 << (sel + 4)); // ȫ˫��ģʽ
		}
		else if (mode == UART_RS485)
		{
			*(UART_MODE_SEL) |= (1 << sel); // 422ģʽ
			*(UART_MODE_SEL) |= (1 << (sel + 4)); // ��˫��ģʽ
		}
		else {}
	}
	// ���ò�����
	*(UART_TBAUD(txPort)) = baudRate;
	// �������ã�У��λ/ֹͣλ/ʹ�ܣ�
	*(UART_TCONFIG(txPort)) = parityBits | stopBits | enable;
#endif
}

/********************************************************************************\
����: ���ʹ�������
����: txPort - ���ں�
     buffer - ������Ϣ
     start - ֡ʼ
     len - ֡��
����: ��
\********************************************************************************/
void Uart_SendMsg(EUartTxPort txPort, USHORT start, USHORT len, UCHAR* buffer)
{
#ifndef WIN32
	USHORT i = 0;

	if (UART_TXPORT_NULL == txPort) return;
//	if (UART_TXPORT_RS232_1 == txPort)
//	{
//		FUART_SendData(txPort, buffer + start, len);
//		return;
//	}

	for (i = start; i < len; i++)
	{
		//*(UART_THR(txPort)) = *(buffer + i);
	}
	uart4sendmsg((char*)buffer, len);
#endif
}


/********************************************************************************\
����: ���մ�������
����: rxPort - ���ں�
     buffer - ������Ϣ
     len - ��Ҫ���յĳ���
����: ʵ�ʶ�ȡ����
\********************************************************************************/
USHORT Uart_RecvMsg(EUartRxPort rxPort, USHORT len, UCHAR* buffer)
{
#ifndef WIN32
	USHORT revLen = 0;
	USHORT i = 0;

	if (UART_RXPORT_NULL == rxPort) return 0;
//	if (UART_RXPORT_RS232_1 == rxPort)
//	{
//		return FUART_RecvData(rxPort, buffer, len);
//	}

	revLen = *(UART_RFIFO_STATE(rxPort));

	if (len < revLen) revLen = len;

	for (i = 0; i < revLen; i++)
	{
		buffer[i] = (UCHAR)*(UART_RHR(rxPort));
	}

	return revLen;
#else
	return 0;
#endif
}

/********************************************************************************\
����: ��ս��ջ�����
����: rxPort - ���ں�
����: ��
\********************************************************************************/
void Uart_ClearRecvBuffer(EUartRxPort rxPort)
{
#ifndef WIN32
	if (UART_RXPORT_NULL == rxPort) return;
	*(UART_RHR(rxPort)) = 0x0001;
#endif
}

/*
* brief      FMC self test function
* param[in]  none
* param[out] none
* retval     none
*/
void fmc_selftest(void)
{
	// Basic FMC self test - test read/write operations
	uint32_t test_address = 0x08020000; // Use a safe test address in flash
	int32_t test_data = 0x12345678;
	int32_t read_data = 0;

	// Test 32-bit read operation
	fmc_read_32bit_data(test_address, 1, &read_data);

	// For a complete self test, you would also test write operations
	// but this requires careful consideration of flash sectors
	// fmc_write_32bit_data(test_address, 1, &test_data);

	// Add more comprehensive tests as needed
}



